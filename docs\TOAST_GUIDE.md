# 🍞 Toast Notification Sistemi Kullanım Kılavuzu

## 📋 Genel Bilgi

Bu proje modern, performanslı ve kullanıcı dostu bir toast notification sistemi içerir. Sistem Zustand ile state management, Framer Motion ile animasyonlar kullanır ve tüm CRUD işlemlerinde kullanıcı geri bildirimi sağlar.

## 🏗️ Sistem Mimarisi

### 1. **State Management (Zustand)**
- Global toast state yönetimi
- Performanslı ve hafif
- TypeScript desteği

### 2. **Animation (Framer Motion)**
- Smooth giriş/çıkış animasyonları
- Layout animations
- Responsive design

### 3. **Toast Types**
- `success` - Başarılı işlemler
- `error` - Hata durumları  
- `warning` - Uyarı mesajları
- `info` - Bilgilendirme

## 🚀 Kullanım Örnekleri

### Temel Kullanım
```tsx
import { toast } from "@/hooks/use-toast"

// Başarı bildirimi
toast.success("İşlem başarıyla tamamlandı!")

// Hata bildirimi
toast.error("Bir hata oluştu!")

// Uyarı bildirimi
toast.warning("Dikkat! Bu işlem geri alınamaz.")

// Bilgi bildirimi
toast.info("Sistem güncellemesi yapılıyor.")
```

### Gelişmiş Seçenekler
```tsx
// Başlık ve süre ile
toast.success("Proje kaydedildi!", {
  title: "Başarılı",
  duration: 4000 // 4 saniye
})

// Action button ile
toast.success("Öğe silindi", {
  title: "Silindi",
  action: {
    label: "Geri Al",
    onClick: () => toast.info("Geri alındı!")
  }
})

// Manuel kapatma (duration: 0)
toast.error("Kritik hata", {
  title: "Hata",
  duration: 0 // Manuel olarak kapatılmalı
})
```

### Promise-based Toast
```tsx
// API çağrıları için ideal
const apiCall = fetch('/api/data')

toast.promise(apiCall, {
  loading: "Veriler yükleniyor...",
  success: "Veriler başarıyla yüklendi!",
  error: "Veri yükleme başarısız!"
})

// Dinamik mesajlar
toast.promise(saveProject(data), {
  loading: "Proje kaydediliyor...",
  success: (result) => `"${result.ad}" projesi kaydedildi!`,
  error: (error) => `Hata: ${error.message}`
})
```

### Component İçinde Hook Kullanımı
```tsx
import { useToast } from "@/hooks/use-toast"

function MyComponent() {
  const { addToast, removeToast, clearAllToasts } = useToast()
  
  const handleSave = () => {
    addToast({
      message: "Özel toast mesajı",
      type: "success",
      title: "Başarılı",
      duration: 5000
    })
  }
  
  return <button onClick={handleSave}>Kaydet</button>
}
```

## 🎨 Stil Özellikleri

### Toast Tipleri ve Renkleri
- **Success**: Yeşil tema (green-50, green-200, green-500)
- **Error**: Kırmızı tema (red-50, red-200, red-500)  
- **Warning**: Sarı tema (yellow-50, yellow-200, yellow-500)
- **Info**: Mavi tema (blue-50, blue-200, blue-500)

### İkonlar (Lucide React)
- Success: `CheckCircle`
- Error: `XCircle`
- Warning: `AlertTriangle`
- Info: `Info`

### Pozisyon ve Layout
- Sağ üst köşede
- Max width: 384px (24rem)
- Stack layout (alt alta)
- Z-index: 50

## 🔧 Konfigürasyon

### Varsayılan Ayarlar
```typescript
const defaultSettings = {
  duration: 5000,        // 5 saniye
  position: "top-right", // Sağ üst
  maxToasts: 5,          // Max aynı anda
  pauseOnHover: true,    // Hover'da durdur
}
```

### Özelleştirme
```tsx
// Özel separator ile
<Breadcrumb separator={<span>/</span>} />

// Özel stil ile
<ToastContainer className="custom-toast-position" />
```

## 🚀 CRUD Entegrasyonu

Toast sistemi tüm CRUD işlemlerine entegre edilmiştir:

### Proje Yönetimi
```typescript
// Başarılı kaydetme
toast.success("Proje başarıyla güncellendi!", {
  title: "Başarılı",
  duration: 4000
})

// Başarılı silme  
toast.success(`"${project.ad}" projesi başarıyla silindi!`, {
  title: "Silindi",
  duration: 4000
})
```

### Error Handling Pattern
```typescript
try {
  // API çağrısı
  const response = await fetch('/api/endpoint')
  if (!response.ok) throw new Error('API Error')
  
  // Başarı
  toast.success("İşlem başarılı!")
} catch (error) {
  // Hata
  toast.error(
    error instanceof Error ? error.message : "Beklenmeyen hata",
    { title: "Hata", duration: 6000 }
  )
}
```

## 📱 Responsive Özellikler

- Mobilde küçük ekran için optimize edilmiş
- Touch-friendly close butonları
- Adaptive text sizing
- Stack overflow handling

## 🎯 Best Practices

### ✅ Yapılması Gerekenler
- API işlemleri için promise toast kullanın
- Error mesajlarında kullanıcı dostu açıklamalar yazın
- Success toast'ları kısa tutun (3-4 saniye)
- Kritik hatalar için manuel kapatma kullanın

### ❌ Yapılmaması Gerekenler
- Çok fazla toast'ı aynı anda göstermeyin
- Çok uzun mesajlar yazmayın
- Her küçük işlem için toast kullanmayın
- Spam'a neden olacak otomatik toast'lar oluşturmayın

## 🔮 Gelecek Özellikler

- [ ] Toast positioning seçenekleri
- [ ] Custom themes
- [ ] Sound notification desteği
- [ ] Toast gruplandırma
- [ ] Undo/Redo functionality
- [ ] Offline durumu için özel toast'lar

## 🧪 Test

Test sayfası: `/test-toast`

Bu sayfada tüm toast tiplerini ve özelliklerini test edebilirsiniz.

---

**Not**: Toast sistemi otomatik olarak çalışır ve manuel konfigürasyon gerektirmez. Sadece `toast.success()`, `toast.error()` vb. fonksiyonları kullanmanız yeterlidir!
