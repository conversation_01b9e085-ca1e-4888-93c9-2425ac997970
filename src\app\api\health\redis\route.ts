import { NextResponse } from "next/server"
import { checkRedisHealth, restartRedisConnection } from "@/lib/redis"
import redis from "@/lib/redis"

export async function GET() {
  try {
    const healthy = await checkRedisHealth()
    const status = redis.status
    
    if (healthy) {
      return NextResponse.json({ 
        status: "ok", 
        redisStatus: status,
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({ 
        status: "error", 
        redisStatus: status,
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    return NextResponse.json({ 
      status: "error", 
      error: String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function POST() {
  try {
    console.log('🔄 Restarting Redis connection via API...')
    
    const success = await restartRedisConnection()
    const status = redis.status
    
    if (success) {
      return NextResponse.json({ 
        status: "ok", 
        redisStatus: status,
        message: "Redis connection restarted successfully",
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({ 
        status: "error", 
        redisStatus: status,
        message: "Redis connection restart failed",
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    console.error('Redis restart API failed:', error)
    return NextResponse.json({ 
      status: "error", 
      error: String(error),
      message: "Failed to restart Redis connection",
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
} 