import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CacheManager, CACHE_TTL } from "@/lib/cache"

export async function GET(request: NextRequest) {
  try {
    // Try to get from cache first
    const cacheKey = 'api:categories:all'
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for categories: ${cacheKey}`)
      return NextResponse.json(cached)
    }
    
    console.log(`Cache miss for categories: ${cacheKey}`)
    
    const categories = await prisma.arizaTip.findMany({
      where: {
        silindi_mi: false,
      },
      orderBy: {
        ad: "asc",
      },
    })

    const result = { categories }
    
    // Store in cache for 1 hour
    await CacheManager.set(cacheKey, result, CACHE_TTL.CATEGORIES)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching categories:", error)
    return NextResponse.json(
      { message: "<PERSON><PERSON>iler yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { ad, aciklama, renk, ikon } = body

    if (!ad) {
      return NextResponse.json(
        { message: "Kategori adı gereklidir" },
        { status: 400 }
      )
    }

    const category = await prisma.arizaTip.create({
      data: {
        ad,
        aciklama,
        renk: renk || "#6B7280",
        ikon,
        olusturan_id: "system", // TODO: Get from session
      },
    })

    // Invalidate categories cache
    await CacheManager.delete('api:categories:all')

    return NextResponse.json({
      message: "Kategori başarıyla oluşturuldu",
      category,
    })
  } catch (error) {
    console.error("Error creating category:", error)
    return NextResponse.json(
      { message: "Kategori oluşturulurken hata oluştu" },
      { status: 500 }
    )
  }
}
