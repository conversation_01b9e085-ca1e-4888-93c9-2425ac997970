import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const now = new Date()
    const months = []
    
    // Get last 6 months
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1)
      
      const faultCount = await prisma.ariza.count({
        where: {
          silindi_mi: false,
          olusturulma_tarihi: {
            gte: date,
            lt: nextMonth,
          }
        }
      })
      
      months.push({
        month: date.toLocaleDateString('tr-TR', { month: 'short' }),
        faults: faultCount,
        year: date.getFullYear(),
        monthNumber: date.getMonth()
      })
    }

    return NextResponse.json({
      data: months,
    })
  } catch (error) {
    console.error("Error fetching monthly stats:", error)
    return NextResponse.json(
      { message: "<PERSON><PERSON><PERSON><PERSON> istatistikler yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
} 