// Using built-in fetch (Node.js 18+)

async function testCacheAPI() {
  console.log('🔍 Testing API Cache...')
  
  const baseUrl = 'http://localhost:3001'
  
  try {
    // Test 1: Faults Stats (should cache for 5 minutes)
    console.log('\n1. Testing faults stats cache...')
    
    console.log('First request (should be cache miss)...')
    const start1 = Date.now()
    const response1 = await fetch(`${baseUrl}/api/faults/stats`)
    const data1 = await response1.json()
    const time1 = Date.now() - start1
    console.log(`Response time: ${time1}ms`)
    console.log('Data:', data1)
    
    console.log('\nSecond request (should be cache hit)...')
    const start2 = Date.now()
    const response2 = await fetch(`${baseUrl}/api/faults/stats`)
    const data2 = await response2.json()
    const time2 = Date.now() - start2
    console.log(`Response time: ${time2}ms`)
    console.log('Data:', data2)
    
    console.log(`✅ Cache performance: ${time1}ms → ${time2}ms (${Math.round((time1 - time2) / time1 * 100)}% improvement)`)
    
    // Test 2: Categories (should cache for 1 hour)
    console.log('\n2. Testing categories cache...')
    
    console.log('First request (should be cache miss)...')
    const start3 = Date.now()
    const response3 = await fetch(`${baseUrl}/api/categories`)
    const data3 = await response3.json()
    const time3 = Date.now() - start3
    console.log(`Response time: ${time3}ms`)
    
    console.log('\nSecond request (should be cache hit)...')
    const start4 = Date.now()
    const response4 = await fetch(`${baseUrl}/api/categories`)
    const data4 = await response4.json()
    const time4 = Date.now() - start4
    console.log(`Response time: ${time4}ms`)
    
    console.log(`✅ Cache performance: ${time3}ms → ${time4}ms (${Math.round((time3 - time4) / time3 * 100)}% improvement)`)
    
    // Test 3: Technicians (should cache for 15 minutes)
    console.log('\n3. Testing technicians cache...')
    
    console.log('First request (should be cache miss)...')
    const start5 = Date.now()
    const response5 = await fetch(`${baseUrl}/api/technicians`)
    const data5 = await response5.json()
    const time5 = Date.now() - start5
    console.log(`Response time: ${time5}ms`)
    
    console.log('\nSecond request (should be cache hit)...')
    const start6 = Date.now()
    const response6 = await fetch(`${baseUrl}/api/technicians`)
    const data6 = await response6.json()
    const time6 = Date.now() - start6
    console.log(`Response time: ${time6}ms`)
    
    console.log(`✅ Cache performance: ${time5}ms → ${time6}ms (${Math.round((time5 - time6) / time5 * 100)}% improvement)`)
    
    console.log('\n🎉 All API cache tests completed!')
    
  } catch (error) {
    console.error('❌ API cache test failed:', error)
  }
}

testCacheAPI() 