"use client";
import { useState, useEffect } from "react";
import { notFound, useRouter } from "next/navigation";
import { ArrowLeft, Calendar, Clock, User, MapPin, Edit, Trash2, Plus, Wrench, Package, Save, ChevronRight, CheckCircle } from "lucide-react";
import Link from "next/link";
import dynamic from "next/dynamic";
import { toast } from "sonner";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
const MDEditor = dynamic(() => import("@uiw/react-md-editor"), { ssr: false });
import "@uiw/react-md-editor/markdown-editor.css";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { AddTechnicianToAppointmentDialog } from "@/components/appointments/add-technician-dialog";
import { AppointmentWorkRecords } from "@/components/appointments/appointment-work-records";
import { AppointmentMaterialUsage } from "@/components/appointments/appointment-material-usage";
import { CloseAppointmentModal } from "@/components/appointments/close-appointment-modal";
import { AppointmentStatus, APPOINTMENT_STATUS_LABELS, APPOINTMENT_STATUS_COLORS, ExpertiseLevel, EXPERTISE_LEVEL_LABELS } from "@/lib/enums";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Appointment } from "@/types";

interface RandevuDetayClientProps {
  params: {
    projeSlug: string;
    blokSlug: string;
    daireSlug: string;
    id: string; // arizaId
    randevuId: string;
  };
}

export default function RandevuDetayClient({ params }: RandevuDetayClientProps) {
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAddTechnicianDialogOpen, setIsAddTechnicianDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [closingAppointment, setClosingAppointment] = useState(false);
  const [yapilanIslemler, setYapilanIslemler] = useState("");
  const [yeniDurum, setYeniDurum] = useState<AppointmentStatus | "">("");
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchAppointment = async () => {
      setLoading(true);
      const { randevuId } = params;
      const response = await fetch(`/api/appointments/${randevuId}`);
      if (response.ok) {
        setAppointment(await response.json());
      } else {
        setAppointment(null);
      }
      setLoading(false);
    };
    fetchAppointment();
  }, [params.randevuId]);

  const handleStatusChange = async (newStatus: AppointmentStatus) => {
    if (!appointment) return;
    // Eğer randevu TAMAMLANDI yapılıyorsa ve henüz tamamlanmamışsa
    if (newStatus === "TAMAMLANDI" && appointment.durum !== "TAMAMLANDI") {
      setShowCloseModal(true);
      return;
    }
    try {
      setUpdatingStatus(true);
      const response = await fetch(`/api/appointments/${appointment.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            durum: newStatus,
            yapilan_islemler: yapilanIslemler || null,
          }),
        }
      );
      if (!response.ok) {
        throw new Error("Durum güncellenirken hata oluştu");
      }
      // Refresh appointment data
      const updatedAppointment = await fetch(`/api/appointments/${appointment.id}`);
      if (updatedAppointment.ok) {
        setAppointment(await updatedAppointment.json());
      }
      toast.success("Randevu durumu başarıyla güncellendi");
      setYapilanIslemler("");
      setYeniDurum("");
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Durum güncellenirken bir hata oluştu");
    } finally {
      setUpdatingStatus(false);
    }
  };

  const handleCloseModalConfirm = async (closeFaultToo: boolean) => {
    if (!appointment) return;
    try {
      setClosingAppointment(true);
      const response = await fetch(`/api/appointments/${appointment.id}/close`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ closeFaultToo }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to close appointment");
      }
      const result = await response.json();
      toast.success(result.message, {
        description: result.details,
        duration: 5000
      });
      // Yönlendirme: Daire detay sayfasına
      const projeSlug = appointment.ariza?.daire?.blok?.proje?.slug;
      const blokSlug = appointment.ariza?.daire?.blok?.slug;
      const daireSlug = appointment.ariza?.daire?.slug;
      if (projeSlug && blokSlug && daireSlug) {
        router.push(`/projeler/${projeSlug}/bloklar/${blokSlug}/${daireSlug}`);
      }
    } catch (error: any) {
      console.error("Error closing appointment:", error);
      toast.error("Randevu kapatılırken hata oluştu", {
        description: error.message || "Beklenmeyen bir hata oluştu",
        duration: 5000
      });
    } finally {
      setClosingAppointment(false);
      setShowCloseModal(false);
    }
  };

  const handleCompleteAppointment = async () => {
    if (!yapilanIslemler.trim()) {
      toast.error("Yapılan işlemler alanı gereklidir");
      return;
    }
    
    await handleStatusChange("TAMAMLANDI");
  };

  if (loading) return <div>Yükleniyor...</div>;
  if (!appointment) return notFound();

  return (
    <div className="p-8 space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Ana Sayfa
        </Link>
        <ChevronRight className="h-4 w-4" />
        {appointment.ariza?.daire?.blok?.proje?.slug ? (
          <Link 
            href={`/projeler/${appointment.ariza.daire.blok.proje.slug}`} 
            className="hover:text-foreground transition-colors"
          >
            {appointment.ariza.daire.blok.proje.ad}
          </Link>
        ) : (
          <span>{appointment.ariza?.daire?.blok?.proje?.ad}</span>
        )}
        <ChevronRight className="h-4 w-4" />
        {appointment.ariza?.daire?.blok?.slug ? (
          <Link 
            href={`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}`} 
            className="hover:text-foreground transition-colors"
          >
            {appointment.ariza.daire.blok.ad}
          </Link>
        ) : (
          <span>{appointment.ariza?.daire?.blok?.ad}</span>
        )}
        <ChevronRight className="h-4 w-4" />
        {appointment.ariza?.daire?.slug ? (
          <Link 
            href={`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}/${appointment.ariza.daire.slug}`} 
            className="hover:text-foreground transition-colors"
          >
            Daire {appointment.ariza.daire.numara}
          </Link>
        ) : (
          <span>Daire {appointment.ariza?.daire?.numara}</span>
        )}
        <ChevronRight className="h-4 w-4" />
        {appointment.ariza?.id ? (
          <Link 
            href={`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}/${appointment.ariza.daire.slug}/arizalar/${appointment.ariza.id}`}
          >
            Arızayı Görüntüle
          </Link>
        ) : (
          <span>Arıza</span>
        )}
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground">Randevu</span>
      </div>

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/randevu">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Randevulara Dön
          </Link>
        </Button>
        <div className="flex-1">
          <h2 className="text-3xl font-bold tracking-tight">Randevu Detayı</h2>
          <div className="flex items-center gap-4 mt-1">
            <p className="text-muted-foreground">{appointment.ariza?.baslik}</p>
            <Badge variant="outline" className="text-xs">
              Arıza: {appointment.ariza?.baslik}
            </Badge>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <MapPin className="h-3 w-3" />
              {appointment.ariza?.daire?.blok?.proje?.ad} - {appointment.ariza?.daire?.blok?.ad} - Daire {appointment.ariza?.daire?.numara}
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/arizalar/${appointment.ariza?.id}`}>
              Arızayı Görüntüle
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/randevu/${appointment.id}/duzenle`}>
              <Edit className="mr-2 h-4 w-4" />
              Düzenle
            </Link>
          </Button>
          <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
            <Trash2 className="mr-2 h-4 w-4" />
            Sil
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Ana Bilgiler */}
        <div className="md:col-span-2 space-y-6">
          {/* Randevu Detayları */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Randevu Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Tarih & Saat</h4>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{new Date(appointment.randevu_tarihi).toLocaleString("tr-TR")}</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Durum</h4>
                  <div className="flex items-center gap-2">
                    <Badge 
                      style={{ 
                        backgroundColor: APPOINTMENT_STATUS_COLORS[appointment.durum as keyof typeof APPOINTMENT_STATUS_COLORS] + "20", 
                        borderColor: APPOINTMENT_STATUS_COLORS[appointment.durum as keyof typeof APPOINTMENT_STATUS_COLORS], 
                        color: APPOINTMENT_STATUS_COLORS[appointment.durum as keyof typeof APPOINTMENT_STATUS_COLORS] 
                      }}
                    >
                      {APPOINTMENT_STATUS_LABELS[appointment.durum as keyof typeof APPOINTMENT_STATUS_LABELS]}
                    </Badge>
                    {appointment.durum !== "TAMAMLANDI" && appointment.durum !== "IPTAL" && (
                      <Select
                        value={yeniDurum}
                        onValueChange={(value) => setYeniDurum(value as AppointmentStatus)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="Değiştir" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="PLANLI">Planlı</SelectItem>
                          <SelectItem value="DEVAM_EDIYOR">Devam Ediyor</SelectItem>
                          <SelectItem value="TAMAMLANDI">Tamamlandı</SelectItem>
                          <SelectItem value="IPTAL">İptal</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </div>
              </div>
              {appointment.aciklama && (
                <div>
                  <h4 className="font-semibold mb-2">Açıklama</h4>
                  <p className="whitespace-pre-wrap">{appointment.aciklama}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Sonuçlandırma Kısmı */}
          {appointment.durum !== "TAMAMLANDI" && appointment.durum !== "IPTAL" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Randevu Sonuçlandırma
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="yapilan_islemler">Yapılan İşlemler</Label>
                  <Textarea
                    id="yapilan_islemler"
                    placeholder="Yapılan işlemleri detaylı olarak yazın..."
                    value={yapilanIslemler}
                    onChange={(e) => setYapilanIslemler(e.target.value)}
                    rows={4}
                    maxLength={1000}
                  />
                  <p className="text-xs text-muted-foreground text-right">
                    {yapilanIslemler.length}/1000 karakter
                  </p>
                </div>
                
                <div className="flex gap-2">
                  {yeniDurum && (
                    <Button 
                      onClick={() => handleStatusChange(yeniDurum)}
                      disabled={updatingStatus}
                      variant="outline"
                    >
                      {updatingStatus ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                          Güncelleniyor...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Durumu Güncelle
                        </>
                      )}
                    </Button>
                  )}
                  <Button 
                    onClick={handleCompleteAppointment}
                    disabled={updatingStatus || !yapilanIslemler.trim()}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {updatingStatus ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Tamamlanıyor...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Randevuyu Tamamla
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Atanan Teknisyenler */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Atanan Teknisyenler
                </CardTitle>
                <Button onClick={() => setIsAddTechnicianDialogOpen(true)} size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Teknisyen Ekle
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {appointment.teknisyenler?.length === 0 ? (
                <div className="text-center py-8">
                  <User className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p className="text-muted-foreground mb-4">Henüz teknisyen atanmamış</p>
                  <Button onClick={() => setIsAddTechnicianDialogOpen(true)} variant="outline">
                    <Plus className="mr-2 h-4 w-4" />
                    İlk Teknisyeni Ekle
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Teknisyen</TableHead>
                      <TableHead>Telefon</TableHead>
                      <TableHead>Uzmanlık Alanları</TableHead>
                      <TableHead>İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {appointment.teknisyenler?.map((at: any) => (
                      <TableRow key={at.teknisyen.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={at.teknisyen.resim || ""} />
                              <AvatarFallback>
                                {at.teknisyen.ad.charAt(0)}{at.teknisyen.soyad.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">
                                {at.teknisyen.ad} {at.teknisyen.soyad}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{at.teknisyen.telefon || "Belirtilmemiş"}</TableCell>
                        <TableCell>
                          {at.teknisyen.uzmanlik_alanlari && at.teknisyen.uzmanlik_alanlari.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              <TooltipProvider>
                                {at.teknisyen.uzmanlik_alanlari.map((expertise: any) => (
                                  <Tooltip key={expertise.id}>
                                    <TooltipTrigger>
                                      <Badge 
                                        variant="outline" 
                                        className="text-xs" 
                                        style={{ 
                                          borderColor: expertise.renk, 
                                          color: expertise.renk, 
                                          backgroundColor: `${expertise.renk}10` 
                                        }}
                                      >
                                        {expertise.ad}
                                        <span className="ml-1 text-[10px] opacity-70">
                                          ({EXPERTISE_LEVEL_LABELS[expertise.seviye as keyof typeof EXPERTISE_LEVEL_LABELS]})
                                        </span>
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <div className="text-center">
                                        <p className="font-medium">{expertise.ad}</p>
                                        <p className="text-xs text-muted-foreground">
                                          Seviye: {EXPERTISE_LEVEL_LABELS[expertise.seviye as keyof typeof EXPERTISE_LEVEL_LABELS]}
                                        </p>
                                        {expertise.aciklama && (
                                          <p className="text-xs mt-1">{expertise.aciklama}</p>
                                        )}
                                      </div>
                                    </TooltipContent>
                                  </Tooltip>
                                ))}
                              </TooltipProvider>
                            </div>
                          ) : (
                            <span className="text-xs text-muted-foreground">Belirtilmemiş</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm" disabled>
                            Kaldır
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
        {/* Yan Panel */}
        <div className="space-y-6">
          {/* İlgili Arıza */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                İlgili Arıza
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Başlık</p>
                <p className="font-medium">{appointment.ariza?.baslik}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Konum</p>
                <p className="text-sm">
                  {appointment.ariza?.daire?.blok?.proje?.ad} - {appointment.ariza?.daire?.blok?.ad} - Daire {appointment.ariza?.daire?.numara}
                </p>
              </div>
            </CardContent>
          </Card>
          {/* Zaman Bilgileri */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Zaman Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Oluşturulma</p>
                <p className="font-medium">
                  {new Date(appointment.olusturulma_tarihi).toLocaleString("tr-TR")}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Son Güncelleme</p>
                <p className="font-medium">
                  {new Date(appointment.guncelleme_tarihi).toLocaleString("tr-TR")}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Close Appointment Modal */}
      <CloseAppointmentModal
        open={showCloseModal}
        onOpenChange={setShowCloseModal}
        appointmentId={appointment.id}
        appointmentDate={new Date(appointment.randevu_tarihi).toLocaleDateString("tr-TR")}
        faultNumber={appointment.ariza?.numara || ""}
        faultTitle={appointment.ariza?.baslik || ""}
        onConfirm={handleCloseModalConfirm}
        loading={closingAppointment}
      />
    </div>
  );
} 