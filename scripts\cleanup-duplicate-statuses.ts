/**
 * Dublicate İptal durumlarını temizleme scripti
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function cleanupDuplicateStatuses() {
  console.log('🧹 Dublicate durum kayıtları temizleniyor...')

  try {
    // İptal durumlarını bul
    const iptalDurumlar = await prisma.arizaDurum.findMany({
      where: {
        OR: [
          { ad: 'İptal' },
          { ad: 'İptal Edildi' }
        ]
      },
      orderBy: { olusturulma_tarihi: 'asc' }
    })

    console.log(`Bulunan İptal durumları: ${iptalDurumlar.length}`)
    iptalDurumlar.forEach((durum: { ad: string; id: string; olusturulma_tarihi: string }) => {
      console.log(`  • ${durum.ad} (${durum.id}) - ${durum.olusturulma_tarihi}`)
    })

    if (iptalDurumlar.length > 1) {
      // <PERSON>lk olanı tut, diğerlerini sil
      const keepStatus = iptalDurumlar[0]
      const duplicateStatuses = iptalDurumlar.slice(1)

      console.log(`\n🔄 "${keepStatus.ad}" tutulacak, ${duplicateStatuses.length} dublicate silinecek`)

      // Arızalardaki referansları güncelle
      for (const duplicateStatus of duplicateStatuses) {
        await prisma.ariza.updateMany({
          where: { durum_id: duplicateStatus.id },
          data: { durum_id: keepStatus.id }
        })

        console.log(`  ✅ ${duplicateStatus.ad} (${duplicateStatus.id}) referansları güncellendi`)
      }

      // Geçmiş kayıtlarındaki referansları güncelle
      for (const duplicateStatus of duplicateStatuses) {
        await prisma.arizaGecmis.updateMany({
          where: { durum_id: duplicateStatus.id },
          data: { durum_id: keepStatus.id }
        })
      }

      // Dublicate kayıtları sil
      for (const duplicateStatus of duplicateStatuses) {
        await prisma.arizaDurum.delete({
          where: { id: duplicateStatus.id }
        })
        console.log(`  🗑️  ${duplicateStatus.ad} (${duplicateStatus.id}) silindi`)
      }

      // Tutilan kaydı standart hale getir
      await prisma.arizaDurum.update({
        where: { id: keepStatus.id },
        data: {
          ad: 'İptal',
          renk: '#6B7280'
        }
      })

      console.log(`  ✅ "${keepStatus.ad}" standardize edildi`)
    }

    // Güncellenmiş durumları göster
    const finalDurumlar = await prisma.arizaDurum.findMany({
      orderBy: { sira: 'asc' }
    })

    console.log('\n📋 Final Durum Listesi:')
    finalDurumlar.forEach((durum: { ad: string; renk: string }) => {
      console.log(`  • ${durum.ad} (${durum.renk})`)
    })

    console.log('\n✅ Temizleme tamamlandı!')

  } catch (error) {
    console.error('❌ Temizleme hatası:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Script çalıştırılıyorsa temizleme yap
if (require.main === module) {
  cleanupDuplicateStatuses()    .then(() => {
      console.log('✅ Temizleme scripti başarıyla tamamlandı')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Temizleme scripti hatası:', error)
      process.exit(1)
    })
}

export { cleanupDuplicateStatuses }
