"use client"

import { useState } from "react"
import Step1AppointmentInfo from "./SmartAppointmentStep1Info"
import Step2TechnicianSelection from "./SmartAppointmentStep2Technicians"
import Step3SummaryConfirm from "./SmartAppointmentStep3Summary"
import { DialogTitle, DialogDescription } from "@/components/ui/dialog"

const steps = [
  { label: "Randevu Bilgileri" },
  { label: "Teknisyen Seçimi" },
  { label: "Özet & Onay" },
]

interface SmartAppointmentFormProps {
  ariza_id: string
  onSuccess?: () => void
  onClose?: () => void
}

export default function SmartAppointmentForm({ ariza_id, onSuccess, onClose }: SmartAppointmentFormProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<any>({ ariza_id })

  const goNext = (data?: any) => {
    if (data) setFormData((prev: any) => ({ ...prev, ...data }))
    setCurrentStep((s) => Math.min(s + 1, steps.length - 1))
  }
  const goBack = () => setCurrentStep((s) => Math.max(s - 1, 0))

  // Success sonrası modalı kapat veya callback çağır
  const handleSuccess = () => {
    if (onSuccess) onSuccess()
    if (onClose) onClose()
  }

  return (
    <div className="w-full max-w-2xl mx-auto bg-white dark:bg-zinc-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-zinc-800 px-8 py-4 transition-all">
      <DialogTitle className="text-2xl font-bold mb-1 text-center">Akıllı Randevu Oluşturma</DialogTitle>
      <DialogDescription className="mb-8 text-center text-gray-500 dark:text-zinc-400">Yeni bir randevu oluşturmak için formu doldurun.</DialogDescription>
      {/* Adım göstergesi */}
      <div className="flex items-center justify-center gap-0 mb-10 select-none">
        {steps.map((step, i) => (
          <div key={i} className="flex items-center">
            <div className={`flex flex-col items-center min-w-[90px]`}>
              <span className={`rounded-full w-9 h-9 flex items-center justify-center border-2 text-lg font-bold transition-all
                ${i === currentStep ? 'bg-blue-600 text-white border-blue-600 shadow-lg scale-110' : 'bg-gray-100 dark:bg-zinc-800 text-gray-400 border-gray-300 dark:border-zinc-700'}
              `}>{i+1}</span>
              <span className={`mt-2 text-xs font-medium ${i === currentStep ? 'text-blue-600' : 'text-gray-400 dark:text-zinc-500'}`}>{step.label}</span>
            </div>
            {i < steps.length - 1 && <div className="w-10 h-1 bg-gray-200 dark:bg-zinc-700 mx-2 rounded" />}
          </div>
        ))}
      </div>
      {/* Adım içeriği */}
      <div className="mt-2">
        {currentStep === 0 && <div className="py-2"><Step1AppointmentInfo data={formData} onNext={goNext} /></div>}
        {currentStep === 1 && <div className="py-2"><Step2TechnicianSelection data={formData} onNext={goNext} onBack={goBack} /></div>}
        {currentStep === 2 && <div className="py-2"><Step3SummaryConfirm data={formData} onBack={goBack} onSuccess={handleSuccess} /></div>}
      </div>
    </div>
  )
} 