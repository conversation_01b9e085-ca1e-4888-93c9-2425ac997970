import React, { useState, useRef, ReactNode } from 'react';

// Basit Modal UI
type ModalProps = {
  open: boolean;
  onClose: () => void;
  children: ReactNode;
};
function Modal({ open, onClose, children }: ModalProps) {
  if (!open) return null;
  return (
    <div style={{position:'fixed',top:0,left:0,width:'100vw',height:'100vh',background:'rgba(0,0,0,0.4)',zIndex:9999,display:'flex',alignItems:'center',justifyContent:'center'}}>
      <div style={{background:'#fff',borderRadius:8,padding:24,minWidth:350,maxWidth:600, boxShadow:'0 8px 32px rgba(0,0,0,0.18)'}}>
        <button style={{float:'right',marginBottom:8}} onClick={onClose}>Kapat</button>
        {children}
      </div>
    </div>
  );
}

type Crop = { x: number; y: number; width: number; height: number };
function cropImage(
  image: HTMLImageElement,
  crop: Crop,
  callback: (blob: Blob) => void
) {
  const canvas = document.createElement('canvas');
  canvas.width = crop.width;
  canvas.height = crop.height;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  ctx.drawImage(
    image,
    crop.x,
    crop.y,
    crop.width,
    crop.height,
    0,
    0,
    crop.width,
    crop.height
  );
  canvas.toBlob(blob => {
    if (blob) callback(blob);
  }, 'image/jpeg', 0.95);
}

type CropperProps = {
  src: string;
  onCrop: (data: { file: File; url: string }) => void;
  aspect?: number;
};
function Cropper({ src, onCrop, aspect = 16 / 9 }: CropperProps) {
  const imgRef = useRef<HTMLImageElement>(null);
  const [crop, setCrop] = useState<Crop>({ x: 0, y: 0, width: 200, height: 112 });
  const [drag, setDrag] = useState<null | (Crop & { startX: number; startY: number })>(null);

  const onMouseDown = (e: React.MouseEvent<HTMLImageElement>) => {
    setDrag({
      startX: e.nativeEvent.offsetX,
      startY: e.nativeEvent.offsetY,
      ...crop
    });
  };
  const onMouseMove = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!drag) return;
    const dx = e.nativeEvent.offsetX - drag.startX;
    let newWidth = Math.max(50, drag.width + dx);
    let newHeight = Math.max(50, drag.height + (aspect ? dx / aspect : 0));
    if (aspect) {
      newHeight = newWidth / aspect;
    }
    setCrop({ ...crop, width: newWidth, height: newHeight });
  };
  const onMouseUp = () => setDrag(null);

  return (
    <div style={{position:'relative',display:'inline-block'}}>
      <img
        ref={imgRef}
        src={src}
        alt="Kırpılacak görsel"
        style={{maxWidth:400,border:'1px solid #ccc'}}
        onMouseDown={onMouseDown}
        onMouseMove={onMouseMove}
        onMouseUp={onMouseUp}
        draggable={false}
      />
      <div style={{
        position:'absolute',
        left:crop.x,top:crop.y,
        width:crop.width,height:crop.height,
        border:'2px dashed #1976d2',
        pointerEvents:'none'
      }} />
      <button style={{marginTop:8}} onClick={() => {
        const img = imgRef.current;
        if (!img) return;
        cropImage(img, crop, blob => {
          const url = URL.createObjectURL(blob);
          onCrop({ file: new File([blob], 'cropped.jpg', { type: 'image/jpeg' }), url });
        });
      }}>Kırp ve Kaydet</button>
    </div>
  );
}

// Context7 API ile medya çekme/yükleme (örnek endpointler, kendi API'nıza göre uyarlayın)
async function fetchMediaList(): Promise<any[]> {
  // Proje görselleri için kendi API'nı kullan
  const res = await fetch('/api/media?customFolder=images/projeler');
  const data = await res.json();
  // MediaSelector ile uyumlu olması için data.data döndürülüyor
  return data.data || [];
}
async function uploadMedia(file: File): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('folder', 'images/projeler');
  const res = await fetch('/api/upload', {
    method: 'POST',
    body: formData
  });
  return res.json();
}

type GlobalMediaSelectorStandaloneProps = {
  onSelect: (media: any) => void;
  buttonText?: string;
  selectedImageUrl?: string;
  trigger?: ReactNode;
};

export default function GlobalMediaSelectorStandalone({
  onSelect,
  buttonText = 'Görsel Seç',
  selectedImageUrl,
  trigger
}: GlobalMediaSelectorStandaloneProps) {
  const [open, setOpen] = useState(false);
  const [tab, setTab] = useState<'gallery' | 'upload'>('gallery');
  const [mediaList, setMediaList] = useState<any[]>([]);
  const [selected, setSelected] = useState<any>(null);
  const [uploadFile, setUploadFile] = useState<{ file: File; url: string } | null>(null);
  const [cropData, setCropData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Galeri yükle
  React.useEffect(() => {
    if (open && tab === 'gallery') {
      setLoading(true);
      fetchMediaList().then(list => {
        setMediaList(list);
        setLoading(false);
      });
    }
  }, [open, tab]);

  // Yükleme işlemi
  const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = ev => {
        setUploadFile({ file, url: ev.target?.result as string });
      };
      reader.readAsDataURL(file);
    }
  };

  // Kırpma sonrası yükle
  const handleCropAndUpload = async (data: { file: File; url: string }) => {
    setLoading(true);
    const result = await uploadMedia(data.file);
    setLoading(false);
    if (result && result.url) {
      setMediaList(prev => [result, ...prev]);
      setTab('gallery');
      onSelect(result);
      setOpen(false);
    }
  };

  // Varsayılan tetikleyici buton
  const defaultTrigger = (
    <button
      type="button"
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: 8,
        border: '1px solid #d1d5db',
        borderRadius: 6,
        padding: '8px 16px',
        background: '#f9fafb',
        cursor: 'pointer',
        fontWeight: 500,
        fontSize: 15
      }}
      onClick={() => setOpen(true)}
    >
      <span style={{fontSize:18,opacity:0.7}}>🖼️</span>
      {buttonText}
      {selectedImageUrl && (
        <img src={selectedImageUrl} alt="Seçili görsel" style={{width:32,height:32,objectFit:'cover',borderRadius:4,marginLeft:8}} />
      )}
    </button>
  );

  return (
    <div>
      <span onClick={e => { e.preventDefault(); if (!open) setOpen(true); }}>
        {trigger || defaultTrigger}
      </span>
      <Modal open={open} onClose={() => setOpen(false)}>
        <div style={{display:'flex',gap:16,marginBottom:16}}>
          <button onClick={()=>setTab('gallery')} style={{fontWeight:tab==='gallery'?'bold':'normal'}}>Galeri</button>
          <button onClick={()=>setTab('upload')} style={{fontWeight:tab==='upload'?'bold':'normal'}}>Yükle</button>
        </div>
        {tab==='gallery' && (
          <div style={{minHeight:200}}>
            {loading ? 'Yükleniyor...' : (
              <div style={{display:'flex',flexWrap:'wrap',gap:8}}>
                {mediaList.length === 0 && <div>Hiç dosya yok</div>}
                {mediaList.map((m,i)=>(
                  <div key={i} style={{border:'1px solid #ccc',padding:4,cursor:'pointer'}} onClick={()=>{setSelected(m);onSelect(m);setOpen(false);}}>
                    <img src={m.url} alt="" style={{width:80,height:80,objectFit:'cover'}} />
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        {tab==='upload' && (
          <div>
            {!uploadFile && (
              <input type="file" accept="image/*" onChange={handleUpload} />
            )}
            {uploadFile && !cropData && (
              <Cropper src={uploadFile.url} onCrop={data=>{setCropData(data);handleCropAndUpload(data);}} />
            )}
            {loading && <div>Yükleniyor...</div>}
          </div>
        )}
      </Modal>
    </div>
  );
} 