"use client"

import { useState, use<PERSON><PERSON>back, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Filter, X } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import {
  statusLabels,
  priorityLabels,
} from "@/lib/validations/fault"
import { SELECT_VALUES } from "@/components/ui/safe-select"

interface Category {
  id: string
  ad: string
  renk: string
}

interface Project {
  id: string
  ad: string
}

interface Block {
  id: string
  ad: string
  projeId: string
}

export function FaultsFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Filter states
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [selectedPriorities, setSelectedPriorities] = useState<string[]>([])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedProject, setSelectedProject] = useState<string>(SELECT_VALUES.ALL)
  const [selectedBlock, setSelectedBlock] = useState<string>(SELECT_VALUES.ALL)
  const [searchQuery, setSearchQuery] = useState<string>("")
    // Data states
  const [categories, setCategories] = useState<Category[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [blocks, setBlocks] = useState<Block[]>([])
  const [loading, setLoading] = useState(true)

  const loadData = useCallback(async () => {
    try {
      setLoading(true)
      const [categoriesRes, projectsRes] = await Promise.all([
        fetch("/api/categories"),
        fetch("/api/projects?simple=true"),
      ])

      if (!categoriesRes.ok || !projectsRes.ok) {
        throw new Error("Failed to fetch data")
      }

      const [categoriesData, projectsData] = await Promise.all([
        categoriesRes.json(),
        projectsRes.json(),
      ])

      setCategories(Array.isArray(categoriesData.categories) ? categoriesData.categories : [])
      setProjects(Array.isArray(projectsData) ? projectsData : [])
    } catch (error) {
      console.error("Error loading filter data:", error)
      setCategories([])
      setProjects([])
    } finally {
      setLoading(false)
    }
  }, [])
  const loadBlocks = useCallback(async (projectId: string) => {
    try {
      const response = await fetch(`/api/blocks?proje_id=${projectId}`)
      if (!response.ok) {
        throw new Error("Failed to fetch blocks")
      }
      const data = await response.json()
      setBlocks(Array.isArray(data.blocks) ? data.blocks : [])
    } catch (error) {
      console.error("Error loading blocks:", error)
      setBlocks([])
    }
  }, [])
  const syncWithUrlParams = useCallback(() => {
    const statuses = searchParams.getAll("durum")
    const priorities = searchParams.getAll("oncelik")
    const categories = searchParams.getAll("kategori")
    const project = searchParams.get("proje") || ""
    const block = searchParams.get("blok") || ""
    const search = searchParams.get("arama") || ""

    // Convert Turkish labels back to enum values
    const statusEnums = statuses.map(status => {
      const enumKey = Object.keys(statusLabels).find(
        key => statusLabels[key as keyof typeof statusLabels] === status
      )
      return enumKey || status
    })

    const priorityEnums = priorities.map(priority => {
      const enumKey = Object.keys(priorityLabels).find(
        key => priorityLabels[key as keyof typeof priorityLabels] === priority
      )
      return enumKey || priority
    })

    setSelectedStatuses(statusEnums)
    setSelectedPriorities(priorityEnums)
    setSelectedCategories(categories)
    setSelectedProject(project)
    setSelectedBlock(block)
    setSearchQuery(search)

    // Load blocks if project is selected
    if (project) {
      loadBlocks(project)
    }
  }, [searchParams, loadBlocks])
  const updateUrl = useCallback(() => {
    const params = new URLSearchParams()

    selectedStatuses.forEach(status => {
      const label = statusLabels[status as keyof typeof statusLabels]
      if (label) params.append("durum", label)
    })
    selectedPriorities.forEach(priority => {
      const label = priorityLabels[priority as keyof typeof priorityLabels]
      if (label) params.append("oncelik", label)
    })
    selectedCategories.forEach(category => params.append("kategori", category))
      if (selectedProject && selectedProject !== SELECT_VALUES.ALL) params.set("proje", selectedProject)
    if (selectedBlock && selectedBlock !== SELECT_VALUES.ALL) params.set("blok", selectedBlock)
    if (searchQuery) params.set("arama", searchQuery)

            const url = params.toString() ? `/arizalar?${params.toString()}` : "/arizalar"
    router.push(url)
  }, [selectedStatuses, selectedPriorities, selectedCategories, selectedProject, selectedBlock, searchQuery, router])

  // Load initial data
  useEffect(() => {
    loadData()
  }, [loadData])

  // Sync with URL parameters
  useEffect(() => {
    syncWithUrlParams()
  }, [syncWithUrlParams])
  // Update URL when filters change
  useEffect(() => {
    updateUrl()
  }, [updateUrl])

  // Load blocks when project changes
  useEffect(() => {
    if (selectedProject && selectedProject !== SELECT_VALUES.ALL) {
      loadBlocks(selectedProject)
    } else {
      setBlocks([])
    }
  }, [selectedProject, loadBlocks])

  const clearAllFilters = () => {
    setSelectedStatuses([])
    setSelectedPriorities([])
    setSelectedCategories([])
    setSelectedProject("")
    setSelectedBlock("")
    setSearchQuery("")
    router.push("/arizalar")
  }

  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      setSelectedStatuses([...selectedStatuses, status])
    } else {
      setSelectedStatuses(selectedStatuses.filter(s => s !== status))
    }
  }

  const handlePriorityChange = (priority: string, checked: boolean) => {
    if (checked) {
      setSelectedPriorities([...selectedPriorities, priority])
    } else {
      setSelectedPriorities(selectedPriorities.filter(p => p !== priority))
    }
  }

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    if (checked) {
      setSelectedCategories([...selectedCategories, categoryId])
    } else {
      setSelectedCategories(selectedCategories.filter(c => c !== categoryId))
    }
  }

  const hasActiveFilters = 
    selectedStatuses.length > 0 ||
    selectedPriorities.length > 0 ||
    selectedCategories.length > 0 ||
    selectedProject ||
    selectedBlock ||
    searchQuery

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Filter className="mr-2 h-4 w-4" />
            Filtreler
          </div>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="h-8 px-2 lg:px-3"
            >
              <X className="mr-1 h-3 w-3" />
              Temizle
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">        {/* Arama */}
        <div className="space-y-2">
          <Label htmlFor="search">Arama</Label>
          <Input
            id="search"
            placeholder="Arıza başlığı veya açıklama..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                updateUrl()
              }
            }}
          />
        </div>

        <Separator />

        {/* Durum Filtreleri */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Durum</Label>
          <div className="space-y-2">
            {Object.entries(statusLabels).map(([value, label]) => (
              <div key={value} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${value}`}
                  checked={selectedStatuses.includes(value)}
                  onCheckedChange={(checked) =>
                    handleStatusChange(value, checked as boolean)
                  }
                />
                <Label
                  htmlFor={`status-${value}`}
                  className="text-sm font-normal"
                >
                  {label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Öncelik Filtreleri */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Öncelik</Label>
          <div className="space-y-2">
            {Object.entries(priorityLabels).map(([value, label]) => (
              <div key={value} className="flex items-center space-x-2">
                <Checkbox
                  id={`priority-${value}`}
                  checked={selectedPriorities.includes(value)}
                  onCheckedChange={(checked) =>
                    handlePriorityChange(value, checked as boolean)
                  }
                />
                <Label
                  htmlFor={`priority-${value}`}
                  className="text-sm font-normal"
                >
                  {label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Separator />        {/* Kategori Filtreleri */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Kategori</Label>
          <div className="space-y-2">            {loading ? (
              <div className="text-sm text-muted-foreground">Kategoriler yükleniyor...</div>
            ) : !Array.isArray(categories) || categories.length === 0 ? (
              <div className="text-sm text-muted-foreground">Kategori bulunamadı</div>
            ) : (
              categories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`category-${category.id}`}
                    checked={selectedCategories.includes(category.id)}
                    onCheckedChange={(checked) =>
                      handleCategoryChange(category.id, checked as boolean)
                    }
                  />
                  <Label
                    htmlFor={`category-${category.id}`}
                    className="text-sm font-normal flex items-center"
                  >
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: category.renk }}
                    />
                    {category.ad}
                  </Label>
                </div>
              ))
            )}
          </div>
        </div>

        <Separator />

        {/* Konum Filtreleri */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Konum</Label>
            {/* Proje Seçimi */}
          <div className="space-y-2">
            <Label htmlFor="project" className="text-xs">Proje</Label>
            <Select
              value={selectedProject || "all"}
              onValueChange={(value) => setSelectedProject(value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Proje seçiniz" />
              </SelectTrigger>              <SelectContent>
                <SelectItem value={SELECT_VALUES.ALL}>Tüm Projeler</SelectItem>                {loading ? (
                  <SelectItem value="loading" disabled>Projeler yükleniyor...</SelectItem>
                ) : Array.isArray(projects) && projects.length > 0 ? (
                  projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.ad}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-data" disabled>Proje bulunamadı</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>          {/* Blok Seçimi */}
          <div className="space-y-2">
            <Label htmlFor="block" className="text-xs">Blok</Label>
            <Select
              value={selectedBlock || "all"}
              onValueChange={(value) => setSelectedBlock(value === "all" ? "" : value)}
              disabled={!selectedProject}
            >
              <SelectTrigger>
                <SelectValue placeholder="Blok seçiniz" />
              </SelectTrigger>              <SelectContent>
                <SelectItem value={SELECT_VALUES.ALL}>Tüm Bloklar</SelectItem>                {!selectedProject ? (
                  <SelectItem value="no-project" disabled>Önce proje seçiniz</SelectItem>
                ) : !Array.isArray(blocks) || blocks.length === 0 ? (
                  <SelectItem value="loading" disabled>Bloklar yükleniyor...</SelectItem>
                ) : (
                  blocks.map((block) => (
                    <SelectItem key={block.id} value={block.id}>
                      {block.ad}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
