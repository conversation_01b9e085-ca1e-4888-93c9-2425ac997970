"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Save, Wrench, Package, CheckCircle, Plus, Trash2 } from "lucide-react"
import { toast } from "sonner"

interface Material {
  id: string
  ad: string
  birim: string
  stok_durumu: string
}

interface UsedMaterial {
  id: string
  malzeme_id: string
  miktar: number
  malzeme: Material
  teknisyen: {
    ad: string
    soyad: string
  }
}

interface AppointmentWorkAreaProps {
  appointmentId: string
  appointmentTechnicians: Array<{
    teknisyen: {
      id: string
      ad: string
      soyad: string
    }
  }>
}

export function AppointmentWorkArea({ appointmentId, appointmentTechnicians }: AppointmentWorkAreaProps) {
  // States
  const [workDescription, setWorkDescription] = useState("")
  const [materials, setMaterials] = useState<Material[]>([])
  const [usedMaterials, setUsedMaterials] = useState<UsedMaterial[]>([])
  const [newMaterial, setNewMaterial] = useState({ malzeme_id: "", miktar: 1, teknisyen_id: "" })
  
  // Result states
  const [faultSolved, setFaultSolved] = useState(false)
  const [completionPercentage, setCompletionPercentage] = useState(0)
  const [nextAppointmentNeeded, setNextAppointmentNeeded] = useState(false)
  
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  // Load initial data
  useEffect(() => {
    loadWorkArea()
    loadMaterials()
    loadUsedMaterials()
  }, [appointmentId])

  const loadWorkArea = async () => {
    try {
      // Load existing work description and result
      const resultResponse = await fetch(`/api/appointments/${appointmentId}/sonuc`)
      if (resultResponse.ok) {
        const result = await resultResponse.json()
        setWorkDescription(result.teknisyen_notlari || "")
        setFaultSolved(result.ariza_cozuldu_mu || false)
        setCompletionPercentage(result.tamamlanma_orani || 0)
        setNextAppointmentNeeded(result.sonraki_randevu_gerekli || false)
      }
    } catch (error) {
      console.error("Error loading work area:", error)
    }
  }

  const loadMaterials = async () => {
    try {
      const response = await fetch("/api/materials")
      if (response.ok) {
        const data = await response.json()
        setMaterials(data)
      }
    } catch (error) {
      console.error("Error loading materials:", error)
    }
  }

  const loadUsedMaterials = async () => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler`)
      if (response.ok) {
        const data = await response.json()
        setUsedMaterials(data)
      }
    } catch (error) {
      console.error("Error loading used materials:", error)
    }
  }

  const addMaterial = async () => {
    if (!newMaterial.malzeme_id || !newMaterial.teknisyen_id || newMaterial.miktar <= 0) {
      toast.error("⚠️ Eksik bilgi", {
        description: "Lütfen tüm alanları doldurun"
      })
      return
    }

    try {
      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          malzeme_id: newMaterial.malzeme_id,
          teknisyen_id: newMaterial.teknisyen_id,
          miktar: newMaterial.miktar,
          kullanim_aciklama: "Randevu sırasında kullanıldı"
        })
      })

      if (response.ok) {
        setNewMaterial({ malzeme_id: "", miktar: 1, teknisyen_id: "" })
        loadUsedMaterials()
        toast.success("✅ Malzeme başarıyla eklendi!", {
          description: "Malzeme listesine kaydedildi"
        })
      } else {
        throw new Error("Malzeme eklenemedi")
      }
    } catch (error) {
      toast.error("❌ Malzeme eklenirken hata oluştu", {
        description: "Lütfen tekrar deneyin"
      })
    }
  }

  const removeMaterial = async (materialId: string) => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler/${materialId}`, {
        method: "DELETE"
      })

      if (response.ok) {
        loadUsedMaterials()
        toast.success("🗑️ Malzeme listeden çıkarıldı", {
          description: "Malzeme başarıyla kaldırıldı"
        })
      }
    } catch (error) {
      toast.error("❌ Malzeme kaldırılırken hata oluştu", {
        description: "Lütfen tekrar deneyin"
      })
    }
  }

  const saveWorkArea = async () => {
    setSaving(true)
    try {
      // Save work description and result
      const response = await fetch(`/api/appointments/${appointmentId}/sonuc`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          durum: faultSolved ? "TAMAMEN_COZULDU" : "KISMI_COZULDU",
          ariza_cozuldu_mu: faultSolved,
          tamamlanma_orani: completionPercentage,
          sonraki_randevu_gerekli: nextAppointmentNeeded,
          teknisyen_notlari: workDescription,
        })
      })

      if (response.ok) {
        toast.success("🎉 Çalışma alanı başarıyla kaydedildi!", {
          description: "Tüm veriler sisteme başarıyla kaydedildi."
        })
        
        // Reload data to reflect changes
        await loadWorkArea()
      } else {
        const errorData = await response.json()
        toast.error("❌ Kaydederken hata oluştu", {
          description: errorData.error || "Lütfen tekrar deneyin."
        })
      }
    } catch (error) {
      console.error("Error saving work area:", error)
      toast.error("❌ Kaydederken hata oluştu", {
        description: "Bir hata oluştu. Lütfen tekrar deneyin."
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Work Description */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            📝 Yapılan İşlemler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="work-description">
              Randevu süresince yapılan tüm işlemleri detaylı olarak açıklayın
            </Label>
            <Textarea
              id="work-description"
              placeholder="Örnek: Elektrik panelini kontrol ettim, arızalı sigortayı değiştirdim, tüm devre testlerini yaptım..."
              value={workDescription}
              onChange={(e) => setWorkDescription(e.target.value)}
              rows={6}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              💡 İpucu: Detaylı açıklama gelecekteki randevularda yardımcı olacaktır
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Materials */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            🧰 Kullanılan Malzemeler
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add Material */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-3 p-4 bg-muted/50 rounded-lg">
            <div>
              <Label className="text-xs">Malzeme</Label>
              <Select value={newMaterial.malzeme_id} onValueChange={(value) => 
                setNewMaterial(prev => ({ ...prev, malzeme_id: value }))
              }>
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {materials.map((material) => (
                    <SelectItem key={material.id} value={material.id}>
                      {material.ad} ({material.birim})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs">Miktar</Label>
              <Input
                type="number"
                min="0.1"
                step="0.1"
                value={newMaterial.miktar}
                onChange={(e) => setNewMaterial(prev => ({ 
                  ...prev, 
                  miktar: parseFloat(e.target.value) || 1 
                }))}
                className="h-9"
              />
            </div>

            <div>
              <Label className="text-xs">Teknisyen</Label>
              <Select value={newMaterial.teknisyen_id} onValueChange={(value) => 
                setNewMaterial(prev => ({ ...prev, teknisyen_id: value }))
              }>
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {appointmentTechnicians.map((at) => (
                    <SelectItem key={at.teknisyen.id} value={at.teknisyen.id}>
                      {at.teknisyen.ad} {at.teknisyen.soyad}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs">&nbsp;</Label>
              <Button onClick={addMaterial} size="sm" className="h-9 w-full">
                <Plus className="h-4 w-4 mr-1" />
                Ekle
              </Button>
            </div>
          </div>

          {/* Used Materials List */}
          {usedMaterials.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Kullanılan Malzemeler:</Label>
              {usedMaterials.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline">{item.miktar} {item.malzeme.birim}</Badge>
                    <span className="font-medium">{item.malzeme.ad}</span>
                    <span className="text-sm text-muted-foreground">
                      - {item.teknisyen.ad} {item.teknisyen.soyad}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeMaterial(item.id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      {/* Simple Result */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            ✅ Randevu Durumu
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Fault Solved */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Arıza Durumu</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="fault-solved"
                  checked={faultSolved}
                  onCheckedChange={(checked) => setFaultSolved(!!checked)}
                />
                <label htmlFor="fault-solved" className="text-sm cursor-pointer">
                  Arıza çözüldü
                </label>
              </div>
            </div>

            {/* Completion Percentage */}
            <div className="space-y-2">
              <Label htmlFor="completion" className="text-sm font-medium">
                Tamamlanma Oranı: %{completionPercentage}
              </Label>
              <Input
                id="completion"
                type="range"
                min="0"
                max="100"
                value={completionPercentage}
                onChange={(e) => setCompletionPercentage(parseInt(e.target.value))}
                className="w-full"
              />
            </div>

            {/* Next Appointment */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Takip</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="next-appointment"
                  checked={nextAppointmentNeeded}
                  onCheckedChange={(checked) => setNextAppointmentNeeded(!!checked)}
                />
                <label htmlFor="next-appointment" className="text-sm cursor-pointer">
                  Sonraki randevu gerekli
                </label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button 
          onClick={saveWorkArea} 
          disabled={saving}
          className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 px-8"
        >
          <Save className="h-4 w-4 mr-2" />
          {saving ? "Kaydediliyor..." : "💾 Çalışma Alanını Kaydet"}
        </Button>
      </div>
    </div>
  )
} 