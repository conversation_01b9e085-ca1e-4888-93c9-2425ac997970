import { z } from "zod"
import { 
  FaultStatus, 
  Priority,
  FAULT_STATUS_LABELS,
  PRIORITY_LABELS,
  FAULT_STATUS_COLORS,
  PRIORITY_COLORS
} from "@/lib/enums"

// Arıza oluşturma şeması
export const createFaultSchema = z.object({
  baslik: z
    .string()
    .min(1, "Başlık gereklidir")
    .min(5, "Başlık en az 5 karakter olmalıdır")
    .max(100, "Başlık en fazla 100 karakter olabilir"),
  aciklama: z
    .string()
    .min(1, "Açıklama gereklidir")
    .min(10, "Açıklama en az 10 karakter olmalıdır")
    .max(1000, "Açıklama en fazla 1000 karakter olabilir"),
  kategoriId: z
    .string()
    .min(1, "Kategori seçimi gereklidir"),
  // Bildiren kişi bilgileri (daire sakini)
  bildirenAdSoyad: z
    .string()
    .min(1, "Bildiren ad soyad gereklidir")
    .min(2, "Ad soyad en az 2 karakter olmalıdır")
    .max(100, "Ad soyad en fazla 100 karakter olabilir"),
  bildirenTelefon: z
    .string()
    .min(1, "Bildiren telefon gereklidir")
    .regex(/^[\d\s\-\+\(\)]{10,15}$/, "Geçerli bir telefon numarası girin"),
  oncelik: z
    .enum([Priority.DUSUK, Priority.ORTA, Priority.YUKSEK, Priority.KRITIK]),
  projeId: z
    .string()
    .min(1, "Proje seçimi gereklidir"),
  blokId: z
    .string()
    .min(1, "Blok seçimi gereklidir"),
  daireId: z
    .string()
    .min(1, "Daire seçimi gereklidir"),
  fotograflar: z
    .array(z.string()),
  teknisyenId: z
    .string()
    .optional(),
})

// Arıza güncelleme şeması
export const updateFaultSchema = z.object({
  baslik: z
    .string()
    .min(5, "Başlık en az 5 karakter olmalıdır")
    .max(100, "Başlık en fazla 100 karakter olabilir")
    .optional(),
  aciklama: z
    .string()
    .min(10, "Açıklama en az 10 karakter olmalıdır")
    .max(1000, "Açıklama en fazla 1000 karakter olabilir")
    .optional(),
  kategoriId: z
    .string()
    .min(1, "Kategori seçimi gereklidir")
    .optional(),  oncelik: z
    .enum([Priority.DUSUK, Priority.ORTA, Priority.YUKSEK, Priority.KRITIK])
    .optional(),
  durum: z
    .enum([FaultStatus.ACIK, FaultStatus.BEKLEMEDE, FaultStatus.DEVAM_EDIYOR, FaultStatus.COZULDU, FaultStatus.IPTAL])
    .optional(),
  teknisyenId: z
    .string()
    .optional(),
})

// Arıza filtreleme şeması
export const faultFiltersSchema = z.object({  durum: z
    .array(z.enum([FaultStatus.ACIK, FaultStatus.BEKLEMEDE, FaultStatus.DEVAM_EDIYOR, FaultStatus.COZULDU, FaultStatus.IPTAL]))
    .optional(),
  oncelik: z
    .array(z.enum([Priority.DUSUK, Priority.ORTA, Priority.YUKSEK, Priority.KRITIK]))
    .optional(),
  kategoriId: z
    .array(z.string())
    .optional(),
  teknisyenId: z
    .string()
    .optional(),
  daireId: z
    .string()
    .optional(),
  blokId: z
    .string()
    .optional(),
  projeId: z
    .string()
    .optional(),
  baslangicTarihi: z
    .string()
    .optional(),
  bitisTarihi: z
    .string()
    .optional(),
  arama: z
    .string()
    .optional(),
})

// Yorum ekleme şeması
export const addCommentSchema = z.object({
  icerik: z
    .string()
    .min(1, "Yorum içeriği gereklidir")
    .min(5, "Yorum en az 5 karakter olmalıdır")
    .max(500, "Yorum en fazla 500 karakter olabilir"),
  arızaId: z
    .string()
    .min(1, "Arıza ID gereklidir"),
})

// Arıza durum değiştirme şeması
export const changeStatusSchema = z.object({  durum: z
    .enum([FaultStatus.ACIK, FaultStatus.BEKLEMEDE, FaultStatus.DEVAM_EDIYOR, FaultStatus.COZULDU, FaultStatus.IPTAL]),
  aciklama: z
    .string()
    .min(5, "Durum değişikliği açıklaması en az 5 karakter olmalıdır")
    .max(200, "Açıklama en fazla 200 karakter olabilir")
    .optional(),
})

// Type exports
export type CreateFaultInput = z.infer<typeof createFaultSchema>
export type CreateFaultFormData = z.infer<typeof createFaultSchema>
export type UpdateFaultInput = z.infer<typeof updateFaultSchema>
export type FaultFiltersInput = z.infer<typeof faultFiltersSchema>
export type AddCommentInput = z.infer<typeof addCommentSchema>
export type ChangeStatusInput = z.infer<typeof changeStatusSchema>

// Legacy compatibility - use enums.ts instead
export const statusLabels = FAULT_STATUS_LABELS
export const priorityLabels = PRIORITY_LABELS  
export const statusColors = FAULT_STATUS_COLORS
export const priorityColors = PRIORITY_COLORS
