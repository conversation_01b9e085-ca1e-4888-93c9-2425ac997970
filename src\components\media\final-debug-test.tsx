"use client"

import React, { useState } from 'react';
import { NewMediaSelector, MediaFile } from './NewMediaSelector';

export default function FinalDebugTest() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile | undefined>(undefined);
  const [apiTestResults, setApiTestResults] = useState<any>(null);
  const [testLog, setTestLog] = useState<string[]>([]);

  const addLog = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    setTestLog(prev => [...prev, `[${timestamp}] ${emoji} ${message}`]);
    console.log(`[FINAL DEBUG] ${message}`);
  };

  const testApiDirectly = async () => {
    addLog('🔍 Testing API directly for duplicates...', 'info');
    
    try {
      const res = await fetch('/api/media?customFolder=projeler');
      const data = await res.json();
      const mediaList = data.data || [];
      
      addLog(`📥 API returned ${mediaList.length} items`, 'info');
      
      // Check for duplicates by URL
      const urls = mediaList.map((m: any) => m.url);
      const uniqueUrls = new Set(urls);
      
      // Check for duplicates by filename
      const filenames = mediaList.map((m: any) => m.originalName);
      const uniqueFilenames = new Set(filenames);
      
      const duplicateUrls = urls.filter((url: string, index: number) => urls.indexOf(url) !== index);
      const duplicateFilenames = filenames.filter((name: string, index: number) => filenames.indexOf(name) !== index);
      
      const results = {
        totalItems: mediaList.length,
        uniqueUrls: uniqueUrls.size,
        uniqueFilenames: uniqueFilenames.size,
        hasDuplicateUrls: urls.length !== uniqueUrls.size,
        hasDuplicateFilenames: filenames.length !== uniqueFilenames.size,
        duplicateUrls,
        duplicateFilenames,
        allUrls: urls,
        allFilenames: filenames
      };
      
      setApiTestResults(results);
      
      if (results.hasDuplicateUrls) {
        addLog(`🚨 FOUND DUPLICATE URLs: ${duplicateUrls.join(', ')}`, 'error');
      } else {
        addLog('✅ No duplicate URLs found in API response', 'success');
      }
      
      if (results.hasDuplicateFilenames) {
        addLog(`🚨 FOUND DUPLICATE FILENAMES: ${duplicateFilenames.join(', ')}`, 'error');
      } else {
        addLog('✅ No duplicate filenames found in API response', 'success');
      }
      
    } catch (error) {
      addLog(`❌ API test failed: ${error}`, 'error');
    }
  };

  const handleSelect = (media: MediaFile | undefined) => {
    setSelectedMedia(media);
    addLog(`Media selected: ${media ? media.originalName : 'None'}`, 'success');
  };

  const handleClose = () => {
    setIsOpen(false);
    addLog('Modal closed', 'info');
  };

  const handleOpen = () => {
    setIsOpen(true);
    addLog('Modal opened', 'info');
  };

  const clearLogs = () => {
    setTestLog([]);
    setApiTestResults(null);
  };

  const runComprehensiveTest = () => {
    addLog('🧪 Starting comprehensive duplication test...', 'info');
    addLog('Step 1: Test API directly for server-side duplicates', 'info');
    testApiDirectly();
    addLog('Step 2: Open media selector and check reducer behavior', 'info');
    addLog('Step 3: Upload an image and verify no duplicates', 'info');
    addLog('Step 4: Close and reopen modal multiple times', 'info');
    addLog('Step 5: Verify final state has no duplicates', 'info');
  };

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">🔍 Final Debug Test - Duplicate Investigation</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Controls */}
        <div className="space-y-6">
          <div className="border rounded-lg p-6 bg-red-50 border-red-200">
            <h2 className="text-xl font-semibold mb-4 text-red-800">🚨 Current Status</h2>
            <p className="text-sm text-red-700 mb-4">
              Duplicates are still occurring despite implementing the new NewMediaSelector component.
              This test will identify the exact source of the problem.
            </p>
            <div className="space-y-3">
              <button
                onClick={testApiDirectly}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition w-full text-sm"
              >
                Test API for Duplicates
              </button>
              
              <button
                onClick={handleOpen}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition w-full text-sm"
              >
                Open New Media Selector
              </button>
              
              <button
                onClick={runComprehensiveTest}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition w-full text-sm"
              >
                Run Comprehensive Test
              </button>
              
              <button
                onClick={clearLogs}
                className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition w-full text-sm"
              >
                Clear Results
              </button>
            </div>
          </div>

          <div className="border rounded-lg p-6 bg-yellow-50">
            <h3 className="text-lg font-semibold mb-3">🔍 Investigation Areas:</h3>
            <ul className="space-y-2 text-sm">
              <li>🌐 <strong>API Response:</strong> Check if server returns duplicates</li>
              <li>🔄 <strong>Reducer Logic:</strong> Verify deduplication in reducer</li>
              <li>📁 <strong>File System:</strong> Check for actual duplicate files</li>
              <li>🎯 <strong>Component Usage:</strong> Ensure new component is being used</li>
              <li>⚡ <strong>Race Conditions:</strong> Check for async state conflicts</li>
            </ul>
          </div>
        </div>

        {/* API Test Results */}
        <div className="space-y-6">
          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">API Test Results</h2>
            {apiTestResults ? (
              <div className="space-y-3">
                <div className={`p-3 rounded ${apiTestResults.hasDuplicateUrls || apiTestResults.hasDuplicateFilenames ? 'bg-red-100 border border-red-300' : 'bg-green-100 border border-green-300'}`}>
                  <div className="text-sm font-medium mb-2">Summary:</div>
                  <div className="text-xs space-y-1">
                    <div>Total Items: {apiTestResults.totalItems}</div>
                    <div>Unique URLs: {apiTestResults.uniqueUrls}</div>
                    <div>Unique Filenames: {apiTestResults.uniqueFilenames}</div>
                    <div>Has Duplicate URLs: {apiTestResults.hasDuplicateUrls ? '❌ YES' : '✅ NO'}</div>
                    <div>Has Duplicate Filenames: {apiTestResults.hasDuplicateFilenames ? '❌ YES' : '✅ NO'}</div>
                  </div>
                </div>
                
                {apiTestResults.duplicateUrls.length > 0 && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded">
                    <div className="text-sm font-medium text-red-800 mb-2">Duplicate URLs:</div>
                    <div className="text-xs text-red-700">
                      {apiTestResults.duplicateUrls.map((url: string, i: number) => (
                        <div key={i}>{url}</div>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="p-3 bg-gray-100 rounded">
                  <div className="text-sm font-medium mb-2">All URLs:</div>
                  <div className="text-xs max-h-32 overflow-y-auto">
                    {apiTestResults.allUrls.map((url: string, i: number) => (
                      <div key={i}>{url}</div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-sm">No API test results yet. Click "Test API for Duplicates" to run the test.</div>
            )}
          </div>

          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Selected Media</h2>
            {selectedMedia ? (
              <div className="flex items-center gap-4">
                <img 
                  src={selectedMedia.url} 
                  alt={selectedMedia.originalName} 
                  className="w-16 h-16 object-cover rounded border"
                />
                <div>
                  <div className="font-medium text-sm">{selectedMedia.originalName}</div>
                  <div className="text-xs text-gray-500">{(selectedMedia.size / 1024).toFixed(2)} KB</div>
                  <div className="text-xs text-blue-600 break-all">{selectedMedia.url}</div>
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-sm">No media selected</div>
            )}
          </div>
        </div>

        {/* Test Log */}
        <div className="space-y-6">
          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Test Log</h2>
            <div className="bg-black text-green-400 p-4 rounded font-mono text-xs max-h-96 overflow-y-auto">
              {testLog.length === 0 ? (
                <div className="text-gray-500">No logs yet...</div>
              ) : (
                testLog.map((log, index) => (
                  <div key={index}>{log}</div>
                ))
              )}
            </div>
          </div>

          <div className="border rounded-lg p-6 bg-blue-50">
            <h3 className="text-lg font-semibold mb-3">📊 Debug Console:</h3>
            <p className="text-sm mb-2">
              Open browser console (F12) to see detailed logging:
            </p>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>🔄 REDUCER: Action dispatches</li>
              <li>🚨 API RETURNING DUPLICATES alerts</li>
              <li>📊 Deduplication statistics</li>
              <li>✅ State updates and validations</li>
            </ul>
          </div>
        </div>
      </div>

      <NewMediaSelector
        open={isOpen}
        onClose={handleClose}
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        title="Final Debug Test - New Media Selector"
        description="Testing the new reducer-based component for duplicates"
        folder="projeler"
        acceptedTypes={["image/jpeg", "image/png", "image/webp"]}
        maxSizeMB={5}
      />
    </div>
  );
}
