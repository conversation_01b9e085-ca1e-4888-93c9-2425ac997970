"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"
import { useEffect, useState } from "react"

interface BreadcrumbItem {
  label: string
  href: string
  isLast?: boolean
}

interface BreadcrumbConfig {
  [key: string]: {
    label: string
    requiresData?: boolean
    dataFetcher?: (id: string) => Promise<string>
  }
}

// URL path'lerini Türkçe etiketlere çeviren mapping
const breadcrumbConfig: BreadcrumbConfig = {
  "dashboard": { label: "Dashboard" },
  "arizalar": { label: "Arıza Yönetimi" },
  "arizalar/yeni": { label: "Yeni Arıza" },
  "arizalar/raporlar": { label: "Arıza Raporları" },
  "arizalar/[id]": { 
    label: "Arıza Detayı",
    requiresData: true,
    dataFetcher: async (id: string) => {
      try {
        const response = await fetch(`/api/faults/${id}`)
        if (response.ok) {
          const fault = await response.json()
          return `Arıza #${fault.numara || id.slice(0, 8)}`
        }
      } catch (error) {
        console.error("Error fetching fault:", error)
      }
      return `Arıza #${id.slice(0, 8)}`
    }
  },
  "arizalar/[id]/duzenle": { label: "Arıza Düzenle" },
  "projeler": { label: "Proje Yönetimi" },
  "projeler/[id]": {
    label: "Proje Detayı",
    requiresData: true,
    dataFetcher: async (id: string) => {
      try {
        const response = await fetch(`/api/projects/${id}`)
        if (response.ok) {
          const project = await response.json()
          return project.ad
        }
      } catch (error) {
        console.error("Error fetching project:", error)
      }
      return "Proje Detayı"
    }
  },
  "bloklar": { label: "Blok Yönetimi" },
  "daireler": { label: "Daire Yönetimi" },
  "randevu": { label: "Randevular" },
  "randevu/yeni": { label: "Yeni Randevu" },
  "randevu/[id]": { 
    label: "Randevu Detayı",
    requiresData: true,
    dataFetcher: async (id: string) => {
      try {
        const response = await fetch(`/api/appointments/${id}`)
        if (response.ok) {
          const appointment = await response.json()
          const faultNumber = appointment.ariza?.numara || "N/A"
          const location = appointment.ariza?.daire?.blok?.proje?.ad || ""
          const blockName = appointment.ariza?.daire?.blok?.ad || ""
          const apartmentNumber = appointment.ariza?.daire?.numara || ""
          
          if (location && blockName && apartmentNumber) {
            return `${faultNumber} - ${location} ${blockName} D.${apartmentNumber}`
          } else {
            return `Randevu - Arıza #${faultNumber}`
          }
        }
      } catch (error) {
        console.error("Error fetching appointment:", error)
      }
      return "Randevu Detayı"
    }
  },
  "randevu/[id]/duzenle": { label: "Randevu Düzenle" },
  "teknisyenler": { label: "Teknisyenler" },
  "malzemeler": { label: "Malzemeler" },
  "kullanici": { label: "Kullanıcı Yönetimi" },
  "ayarlar": { label: "Ayarlar" },
}

export function Breadcrumb() {
  const pathname = usePathname()
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const generateBreadcrumbs = async () => {
      setLoading(true)
      
      // Ana sayfa breadcrumb'ı her zaman var
      const items: BreadcrumbItem[] = [
        { label: "Ana Sayfa", href: "/dashboard" }
      ]

      // Pathname'i parçalara ayır
      const pathSegments = pathname.split("/").filter(segment => segment !== "")
      
      // Daire detay sayfası için özel breadcrumb (/projeler/[projeSlug]/bloklar/[blokSlug]/[daireSlug]/arizalar)
      if (pathSegments.length >= 6 && pathSegments[0] === 'projeler' && pathSegments[2] === 'bloklar' && pathSegments[5] === 'arizalar') {
        const projeSlug = pathSegments[1]
        const blokSlug = pathSegments[3]
        const daireSlug = pathSegments[4]
        
        try {
          // Daire bilgisini çek (slug tabanlı)
          const daireResponse = await fetch(`/api/projects/${projeSlug}/blocks/${blokSlug}/apartments/${daireSlug}`)
          if (daireResponse.ok) {
            const daire = await daireResponse.json()
            
            // Hiyerarşik breadcrumb oluştur
            items.push({
              label: daire.blok.proje.ad,
              href: "/projeler"
            })
            
            items.push({
              label: daire.blok.ad,
              href: `/projeler/${projeSlug}/bloklar/${blokSlug}`
            })
            
            items.push({
              label: `Daire ${daire.numara}`,
              href: `/projeler/${projeSlug}/bloklar/${blokSlug}/${daireSlug}`
            })
            
            items.push({
              label: "Arızalar",
              href: pathname,
              isLast: true
            })
          }
        } catch (error) {
          console.error("Error fetching data for apartment breadcrumb:", error)
          // Fallback breadcrumb
          items.push({
            label: "Proje Yönetimi",
            href: "/projeler"
          })
          items.push({
            label: "Daire Detayı",
            href: pathname,
            isLast: true
          })
        }
        
        setBreadcrumbs(items)
        setLoading(false)
        return
      }
      
      // Her segment için breadcrumb oluştur
      for (let i = 0; i < pathSegments.length; i++) {
        const currentPath = pathSegments.slice(0, i + 1).join("/")
        const segment = pathSegments[i]
        
        // UUID pattern kontrolü (dinamik ID'ler için)
        const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(segment)
        
        let config
        let label = segment
        
        if (isUuid) {
          // Dinamik ID için parent path'i kontrol et
          const parentPath = pathSegments.slice(0, i).join("/")
          const dynamicKey = `${parentPath}/[id]`
          config = breadcrumbConfig[dynamicKey]
          
          if (config?.requiresData && config.dataFetcher) {
            try {
              label = await config.dataFetcher(segment)
            } catch (error) {
              console.error("Error fetching breadcrumb data:", error)
              label = config.label
            }
          } else if (config) {
            label = config.label
          }
        } else {
          // Statik path için config'i kontrol et
          config = breadcrumbConfig[currentPath]
          if (config) {
            if (config.requiresData && config.dataFetcher) {
              try {
                label = await config.dataFetcher(segment)
              } catch (error) {
                console.error("Error fetching breadcrumb data:", error)
                label = config.label
              }
            } else {
              label = config.label
            }
          } else {
            // Fallback: segment'i capitalize et
            label = segment.charAt(0).toUpperCase() + segment.slice(1)
          }
        }

        items.push({
          label,
          href: `/${currentPath}`,
          isLast: i === pathSegments.length - 1
        })
      }

      setBreadcrumbs(items)
      setLoading(false)
    }

    generateBreadcrumbs()
  }, [pathname])

  if (loading) {
    return (
      <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
        <div className="animate-pulse flex items-center space-x-1">
          <div className="h-4 w-16 bg-muted rounded"></div>
          <ChevronRight className="h-4 w-4" />
          <div className="h-4 w-20 bg-muted rounded"></div>
        </div>
      </nav>
    )
  }

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
      {breadcrumbs.map((item, index) => (
        <div key={`${item.href}-${index}`} className="flex items-center">
          {index > 0 && <ChevronRight className="h-4 w-4 mx-1" />}
          
          {index === 0 && (
            <Home className="h-4 w-4 mr-1" />
          )}
          
          {item.isLast ? (
            <span className="font-medium text-foreground">{item.label}</span>
          ) : (
            <Link 
              href={item.href}
              className="hover:text-foreground transition-colors"
            >
              {item.label}
            </Link>
          )}
        </div>
      ))}
    </nav>
  )
}
