import { notFound } from "next/navigation";
import Link from "next/link";
import { ChevronRight, Home, Users } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { headers } from 'next/headers';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import DairelerTable from "./DairelerTable";
import { BreadcrumbWithLoading } from "@/components/ui/breadcrumb-with-loading";
import { getInternalApiBaseUrl } from "@/lib/utils";

async function getProjectBySlug(projeSlug: string) {
  const baseUrl = await getInternalApiBaseUrl();
  const res = await fetch(`${baseUrl}/api/projects/${projeSlug}`, { cache: 'no-store' });
  if (!res.ok) return null;
  return res.json();
}

async function getBlockBySlug(projeSlug: string, blokSlug: string) {
  const baseUrl = await getInternalApiBaseUrl();
  const res = await fetch(`${baseUrl}/api/projects/${projeSlug}/blocks/${blokSlug}`, { cache: 'no-store' });
  if (!res.ok) return null;
  return res.json();
}

async function getApartmentsByBlockSlug(projeSlug: string, blokSlug: string) {
  const baseUrl = await getInternalApiBaseUrl();
  const res = await fetch(`${baseUrl}/api/projects/${projeSlug}/blocks/${blokSlug}/apartments`, { cache: 'no-store' });
  if (!res.ok) return [];
  const data = await res.json();
  return data.apartments || [];
}

export default async function BlockDetailPage({ 
  params 
}: { 
  params: Promise<{ projeSlug: string; blokSlug: string }> 
}) {
  const resolvedParams = await params;
  const { projeSlug, blokSlug } = resolvedParams;

  // Önce projeyi bul
  const project = await getProjectBySlug(projeSlug);
  if (!project) return notFound();

  // Sonra bloku bul
  const block = await getBlockBySlug(projeSlug, blokSlug);
  if (!block) return notFound();

  const apartments = await getApartmentsByBlockSlug(projeSlug, blokSlug);

  const breadcrumbItems = [
    { label: "Ana Sayfa", href: "/dashboard" },
    { label: "Projeler", href: "/projeler" },
    { label: project.ad, href: `/projeler/${project.slug}` },
    { label: block.ad, isActive: true }
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <BreadcrumbWithLoading items={breadcrumbItems} />

      {/* Block Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">
          {project.ad} Projesi {block.ad} Bloğuna Ait Daireler
        </h1>
        <p className="text-muted-foreground mt-2">
          {project.ad} projesinin {block.ad} bloğuna ait daireleri ekleyin, düzenleyin ve yönetin.
        </p>
      </div>

      {/* Block Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daire Sayısı</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{block._count?.daireler || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Arıza Sayısı</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{block._count?.arizalar || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Apartments Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold tracking-tight">Daireler</h2>
        </div>
        {apartments.length === 0 ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <Home className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Bu blokta henüz daire bulunmuyor</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <DairelerTable apartments={apartments} projeSlug={projeSlug} blokSlug={blokSlug} />
          </Card>
        )}
      </div>
    </div>
  );
} 