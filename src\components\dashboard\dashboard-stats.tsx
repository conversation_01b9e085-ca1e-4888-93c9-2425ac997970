import React, { useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { ArrowUpIcon, ArrowDownIcon, Wrench, Loader, CheckCircle, AlertTriangle } from "lucide-react"

export const DashboardStats = React.memo(() => {
  const stats = useMemo(() => [
    {
      title: "Toplam Arıza",
      value: "124",
      change: "+12%",
      trend: "up",
      icon: Wrench,
      iconColor: "text-blue-600",
      iconBg: "bg-blue-100"
    },
    {
      title: "Devam Eden",
      value: "32",
      change: "-5%",
      trend: "down",
      icon: Loader,
      iconColor: "text-yellow-600",
      iconBg: "bg-yellow-100"
    },
    {
      title: "Tamamlanan",
      value: "87",
      change: "+18%",
      trend: "up",
      icon: CheckCircle,
      iconColor: "text-green-600",
      iconBg: "bg-green-100"
    },
    {
      title: "Kritik Arızalar",
      value: "9",
      change: "+25%",
      trend: "up",
      icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
      iconColor: "text-red-600",
      iconBg: "bg-red-100"
    }
  ], [])

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className={`p-3 rounded-full ${stat.iconBg}`}>
                <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                <p className="text-2xl font-semibold text-gray-800">{stat.value}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className={`flex items-center text-sm ${
                stat.trend === "up" ? "text-green-500" : "text-red-500"
              }`}>
                {stat.trend === "up" ? (
                  <ArrowUpIcon className="w-4 h-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="w-4 h-4 mr-1" />
                )}
                <span>{stat.change} geçen aya göre</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
})

DashboardStats.displayName = "DashboardStats"
