import { Metadata } from "next"
import { Suspense } from "react"
import DashboardOverview from "@/components/dashboard/dashboard-overview"
import DashboardCharts from "@/components/dashboard/dashboard-charts"
import DashboardRecentActivity from "@/components/dashboard/dashboard-recent-activity"
import DashboardQuickActions from "@/components/dashboard/dashboard-quick-actions"

export const metadata: Metadata = {
  title: "Dashboard | Konut Arıza Takip Sistemi",
  description: "Ana kontrol paneli - Arıza, bakım ve onarım süreçlerini yönetin",
}

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-6 sm:py-8 space-y-6 sm:space-y-8">
        {/* Overview Stats */}
        <Suspense fallback={
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-white/50 rounded-xl animate-pulse"></div>
            ))}
          </div>
        }>
          <DashboardOverview />
        </Suspense>

        {/* Quick Actions */}
        <DashboardQuickActions />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Charts Section */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            <Suspense fallback={<div className="h-96 bg-white/50 rounded-xl animate-pulse"></div>}>
              <DashboardCharts />
            </Suspense>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 sm:space-y-8">
            <Suspense fallback={<div className="h-64 bg-white/50 rounded-xl animate-pulse"></div>}>
              <DashboardRecentActivity />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  )
}
