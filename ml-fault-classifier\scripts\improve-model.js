const fs = require('fs');
const path = require('path');
const FaultClassifier = require('../models/simple-classifier');

// Training data'yı yükle
const trainingDataPath = path.join(__dirname, '../data/training-data.json');
const trainingData = JSON.parse(fs.readFileSync(trainingDataPath, 'utf8'));

console.log('🔧 Model İyileştirme Başlıyor...\n');

// Veriyi eğitim ve test olarak böl
const shuffleArray = (array) => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

const shuffledData = shuffleArray(trainingData);
const splitIndex = Math.floor(shuffledData.length * 0.8);
const trainData = shuffledData.slice(0, splitIndex);
const testData = shuffledData.slice(splitIndex);

console.log(`📊 Veri Dağılımı: ${trainData.length} eğitim, ${testData.length} test\n`);

// Farklı güven eşikleri test et
const confidenceThresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9];
const results = [];

confidenceThresholds.forEach(threshold => {
  console.log(`🎯 Güven eşiği ${threshold} test ediliyor...`);
  
  const classifier = new FaultClassifier();
  classifier.setConfidenceThreshold(threshold);
  
  try {
    // Modeli eğit
    classifier.train(trainData);
    
    // Test et
    const testResults = classifier.test(testData);
    
    results.push({
      threshold: threshold,
      accuracy: testResults.accuracy,
      correct: testResults.correct,
      total: testResults.total,
      successRate: (testResults.correct / testResults.total) * 100
    });
    
    console.log(`   📊 Doğruluk: ${testResults.accuracy.toFixed(2)}%`);
    console.log(`   ✅ Başarılı: ${testResults.correct}/${testResults.total}`);
  } catch (error) {
    console.log(`   ❌ Hata: ${error.message}`);
  }
});

// En iyi sonucu bul
const bestResult = results.reduce((best, current) => {
  return current.accuracy > best.accuracy ? current : best;
});

console.log('\n' + '='.repeat(60));
console.log('🏆 En İyi Sonuç:');
console.log(`   🎯 Güven Eşiği: ${bestResult.threshold}`);
console.log(`   📊 Doğruluk: ${bestResult.accuracy.toFixed(2)}%`);
console.log(`   ✅ Başarılı: ${bestResult.correct}/${bestResult.total}`);

// Tüm sonuçları göster
console.log('\n📊 Tüm Sonuçlar:');
results.forEach(result => {
  const isBest = result.threshold === bestResult.threshold;
  console.log(`   ${isBest ? '🏆' : '  '} ${result.threshold}: ${result.accuracy.toFixed(2)}% (${result.correct}/${result.total})`);
});

// En iyi model ile örnek tahminler
console.log('\n' + '='.repeat(60));
console.log('🎯 En İyi Model ile Örnek Tahminler:');

const bestClassifier = new FaultClassifier();
bestClassifier.setConfidenceThreshold(bestResult.threshold);
bestClassifier.train(trainData);

const testCases = [
  {
    title: "Banyo musluğu damlatıyor",
    description: "Banyo musluğunda sürekli damlama var, su israfı oluyor."
  },
  {
    title: "Elektrik kesintisi yaşanıyor",
    description: "Dairemizde sürekli elektrik kesintisi oluyor, prizler çalışmıyor."
  },
  {
    title: "Duvar boyası dökülüyor",
    description: "Duvar boyası dökülüyor, duvar çıplak kalıyor."
  },
  {
    title: "Asansör çalışmıyor",
    description: "Asansör çalışmıyor, hiç hareket etmiyor."
  },
  {
    title: "Kapı kilidi bozuk",
    description: "Kapı kilidi bozuk, kapı açılamıyor."
  },
  {
    title: "Kalorifer peteği çalışmıyor",
    description: "Kalorifer peteği çalışmıyor, ev soğuk."
  },
  {
    title: "Bahçe bakımı gerekiyor",
    description: "Bahçe bakımı gerekiyor, çimler uzamış."
  },
  {
    title: "Güvenlik kamerası çalışmıyor",
    description: "Güvenlik kamerası çalışmıyor, görüntü yok."
  }
];

testCases.forEach((testCase, index) => {
  try {
    const prediction = bestClassifier.predict(testCase.title, testCase.description);
    console.log(`\n${index + 1}. "${testCase.title}"`);
    
    if (prediction.success) {
      console.log(`   🎯 Tahmin: ${prediction.category}`);
      console.log(`   🏷️  Kategori ID: ${prediction.category_id}`);
      console.log(`   📊 Güven: ${(prediction.confidence * 100).toFixed(1)}%`);
      
      if (prediction.alternatives && prediction.alternatives.length > 0) {
        console.log(`   🔄 Alternatifler:`);
        prediction.alternatives.forEach((alt, i) => {
          console.log(`      ${i + 1}. ${alt.category} (${(alt.confidence * 100).toFixed(1)}%)`);
        });
      }
    } else {
      console.log(`   ❌ Tahmin başarısız: ${prediction.message || prediction.error}`);
      console.log(`   📊 Güven: ${(prediction.confidence * 100).toFixed(1)}%`);
      
      if (prediction.alternatives && prediction.alternatives.length > 0) {
        console.log(`   🔄 Alternatifler:`);
        prediction.alternatives.forEach((alt, i) => {
          console.log(`      ${i + 1}. ${alt.category} (${(alt.confidence * 100).toFixed(1)}%)`);
        });
      }
    }
  } catch (error) {
    console.log(`   ❌ Hata: ${error.message}`);
  }
});

// Model performansını değerlendir
console.log('\n' + '='.repeat(60));
console.log('📈 Model Performans Değerlendirmesi:');

const accuracy = bestResult.accuracy;
if (accuracy >= 90) {
  console.log('   🏆 Mükemmel! Model çok iyi performans gösteriyor.');
} else if (accuracy >= 80) {
  console.log('   ✅ İyi! Model iyi performans gösteriyor.');
} else if (accuracy >= 70) {
  console.log('   ⚠️  Orta! Model orta performans gösteriyor, iyileştirme gerekebilir.');
} else if (accuracy >= 50) {
  console.log('   🔧 Kabul edilebilir! Model temel işlevselliği sağlıyor.');
} else {
  console.log('   ❌ Düşük! Model performansı düşük, iyileştirme gerekli.');
}

console.log('\n🎯 Öneriler:');
if (accuracy < 70) {
  console.log('   📚 Daha fazla eğitim verisi ekleyin');
  console.log('   🔧 Farklı kategori anahtar kelimeleri deneyin');
  console.log('   🏷️  Kategori sayısını azaltmayı düşünün');
  console.log('   📝 Daha detaylı açıklamalar ekleyin');
}

// En iyi modeli kaydet
const bestModelConfig = {
  confidenceThreshold: bestResult.threshold,
  accuracy: bestResult.accuracy,
  trainingDataSize: trainData.length,
  testDataSize: testData.length,
  categories: Object.keys(bestClassifier.categories),
  createdAt: new Date().toISOString()
};

const configPath = path.join(__dirname, '../data/best-model-config.json');
fs.writeFileSync(configPath, JSON.stringify(bestModelConfig, null, 2), 'utf8');

console.log(`\n💾 En iyi model konfigürasyonu kaydedildi: ${configPath}`);
console.log('\n✅ Model iyileştirme tamamlandı!');
console.log('🚀 API servisi başlatılabilir.'); 