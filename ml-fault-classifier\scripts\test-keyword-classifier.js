const fs = require('fs');
const path = require('path');
const KeywordClassifier = require('../models/keyword-classifier');

// Training data'yı yükle
const trainingDataPath = path.join(__dirname, '../data/training-data.json');
const trainingData = JSON.parse(fs.readFileSync(trainingDataPath, 'utf8'));

console.log('🔍 Keyword Classifier Testi Başlıyor...\n');

// Veriyi eğitim ve test olarak böl
const shuffleArray = (array) => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

const shuffledData = shuffleArray(trainingData);
const splitIndex = Math.floor(shuffledData.length * 0.8);
const trainData = shuffledData.slice(0, splitIndex);
const testData = shuffledData.slice(splitIndex);

console.log(`📊 Veri Dağılımı: ${trainData.length} eğitim, ${testData.length} test\n`);

// Keyword Classifier oluştur ve eğit
const classifier = new KeywordClassifier();
classifier.setConfidenceThreshold(0.1); // Düşük eşik

try {
  classifier.train(trainData);
  console.log('\n' + '='.repeat(50) + '\n');
} catch (error) {
  console.error('❌ Model eğitimi başarısız:', error.message);
  process.exit(1);
}

// Model durumunu göster
const status = classifier.getStatus();
console.log('📋 Model Durumu:');
console.log(`   🤖 Eğitildi: ${status.isTrained ? '✅' : '❌'}`);
console.log(`   📊 Toplam örnek: ${status.totalSamples}`);
console.log(`   ⏱️  Eğitim süresi: ${status.trainingTime}ms`);
console.log(`   🎯 Güven eşiği: ${status.confidenceThreshold}`);
console.log(`   🔑 Toplam keyword: ${status.totalKeywords}`);

// Kategori bazında keyword'leri göster
console.log('\n🔑 Kategori Bazında Önemli Keyword\'ler:');
Object.keys(status.categories).forEach(category => {
  const topKeywords = classifier.getTopKeywords(category, 5);
  console.log(`\n   ${category}:`);
  topKeywords.forEach((item, index) => {
    console.log(`     ${index + 1}. "${item.word}" (${item.count} kez)`);
  });
});

// Modeli test et
console.log('\n' + '='.repeat(50));
try {
  const testResults = classifier.test(testData);
  console.log('\n📋 Test Sonuçları:');
  console.log(`   📊 Genel Doğruluk: ${testResults.accuracy.toFixed(2)}%`);
  console.log(`   ✅ Doğru Tahmin: ${testResults.correct}/${testResults.total}`);
  console.log(`   ❌ Yanlış Tahmin: ${testResults.total - testResults.correct}/${testResults.total}`);

  // Kategori bazında sonuçları göster
  console.log('\n📊 Kategori Bazında Sonuçlar:');
  Object.entries(testResults.categoryResults).forEach(([category, result]) => {
    const accuracy = (result.correct / result.total) * 100;
    console.log(`   ${category}: ${result.correct}/${result.total} (${accuracy.toFixed(1)}%)`);
  });
} catch (error) {
  console.error('❌ Model testi başarısız:', error.message);
}

// Örnek tahminler yap
console.log('\n' + '='.repeat(50));
console.log('🎯 Örnek Tahminler:');

const testCases = [
  {
    title: "Banyo musluğu damlatıyor",
    description: "Banyo musluğunda sürekli damlama var, su israfı oluyor."
  },
  {
    title: "Elektrik kesintisi yaşanıyor",
    description: "Dairemizde sürekli elektrik kesintisi oluyor, prizler çalışmıyor."
  },
  {
    title: "Duvar boyası dökülüyor",
    description: "Duvar boyası dökülüyor, duvar çıplak kalıyor."
  },
  {
    title: "Asansör çalışmıyor",
    description: "Asansör çalışmıyor, hiç hareket etmiyor."
  },
  {
    title: "Kapı kilidi bozuk",
    description: "Kapı kilidi bozuk, kapı açılamıyor."
  },
  {
    title: "Kalorifer peteği çalışmıyor",
    description: "Kalorifer peteği çalışmıyor, ev soğuk."
  },
  {
    title: "Bahçe bakımı gerekiyor",
    description: "Bahçe bakımı gerekiyor, çimler uzamış."
  },
  {
    title: "Güvenlik kamerası çalışmıyor",
    description: "Güvenlik kamerası çalışmıyor, görüntü yok."
  }
];

testCases.forEach((testCase, index) => {
  try {
    const prediction = classifier.predict(testCase.title, testCase.description);
    console.log(`\n${index + 1}. "${testCase.title}"`);
    
    if (prediction.success) {
      console.log(`   🎯 Tahmin: ${prediction.category}`);
      console.log(`   🏷️  Kategori ID: ${prediction.category_id}`);
      console.log(`   📊 Güven: ${(prediction.confidence * 100).toFixed(1)}%`);
      console.log(`   🔑 Eşleşen kelime: ${prediction.matchedWords}/${prediction.totalWords}`);
      
      if (prediction.alternatives && prediction.alternatives.length > 0) {
        console.log(`   🔄 Alternatifler:`);
        prediction.alternatives.forEach((alt, i) => {
          console.log(`      ${i + 1}. ${alt.category} (${(alt.confidence * 100).toFixed(1)}%) - ${alt.matchedWords}/${alt.totalWords} kelime`);
        });
      }
    } else {
      console.log(`   ❌ Tahmin başarısız: ${prediction.message || prediction.error}`);
      console.log(`   📊 Güven: ${(prediction.confidence * 100).toFixed(1)}%`);
      
      if (prediction.alternatives && prediction.alternatives.length > 0) {
        console.log(`   🔄 Alternatifler:`);
        prediction.alternatives.forEach((alt, i) => {
          console.log(`      ${i + 1}. ${alt.category} (${(alt.confidence * 100).toFixed(1)}%) - ${alt.matchedWords}/${alt.totalWords} kelime`);
        });
      }
    }
  } catch (error) {
    console.log(`   ❌ Hata: ${error.message}`);
  }
});

// Model performansını değerlendir
console.log('\n' + '='.repeat(50));
console.log('📈 Model Performans Değerlendirmesi:');

const accuracy = status.accuracy;
if (accuracy >= 90) {
  console.log('   🏆 Mükemmel! Model çok iyi performans gösteriyor.');
} else if (accuracy >= 80) {
  console.log('   ✅ İyi! Model iyi performans gösteriyor.');
} else if (accuracy >= 70) {
  console.log('   ⚠️  Orta! Model orta performans gösteriyor, iyileştirme gerekebilir.');
} else if (accuracy >= 50) {
  console.log('   🔧 Kabul edilebilir! Model temel işlevselliği sağlıyor.');
} else {
  console.log('   ❌ Düşük! Model performansı düşük, iyileştirme gerekli.');
}

console.log('\n🎯 Öneriler:');
if (accuracy < 70) {
  console.log('   📚 Daha fazla eğitim verisi ekleyin');
  console.log('   🔧 Farklı kategori anahtar kelimeleri deneyin');
  console.log('   🏷️  Kategori sayısını azaltmayı düşünün');
  console.log('   📝 Daha detaylı açıklamalar ekleyin');
  console.log('   🎯 Güven eşiğini ayarlayın');
}

console.log('\n✅ Keyword Classifier testi tamamlandı!');
console.log('🚀 API servisi başlatılabilir.'); 