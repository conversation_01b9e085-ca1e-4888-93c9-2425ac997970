services:
  postgres:
    image: postgres:15-alpine
    container_name: baki<PERSON>rim_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: bakimonarim
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bakimonarim_network

  redis:
    image: redis:7-alpine
    container_name: bakimonarim_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: >
      redis-server 
      --appendonly yes 
      --maxmemory 128mb 
      --maxmemory-policy allkeys-lru
      --timeout 0
      --tcp-keepalive 60
      --save 60 1000
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - bakimonarim_network

  app:
    build: .
    container_name: bakimonarim_app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*****************************************/bakimonarim
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - NEXTAUTH_SECRET=your-secret-key-change-in-production
      - NEXTAUTH_URL=http://localhost:3000
    volumes:
      - app_uploads:/app/public/media
    depends_on:
      postgres:
        condition: service_started
      redis:
        condition: service_healthy
    networks:
      - bakimonarim_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health/redis"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  ml-fault-classifier:
    build: ./ml-fault-classifier
    container_name: bakimonarim_ml_service
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3050
      - MAIN_APP_URL=http://app:3000
    ports:
      - "3050:3050"
    volumes:
      - ml_data:/app/data
      - ml_models:/app/models
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3050/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - bakimonarim_network
    depends_on:
      - postgres
      - app

volumes:
  postgres_data:
  redis_data:
  ml_data:
  ml_models:
  app_uploads:

networks:
  bakimonarim_network:
    driver: bridge
