import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug } = await params;

    // Projeyi bul
    const project = await prisma.proje.findFirst({
      where: { slug: projeSlug, silindi_mi: false },
    });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });

    // Bloku bul
    const block = await prisma.blok.findFirst({
      where: { slug: blokSlug, proje_id: project.id, silindi_mi: false },
    });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });

    // Daireyi bul
    const daire = await prisma.daire.findFirst({
      where: { slug: daireSlug, blok_id: block.id, silindi_mi: false },
      select: {
        id: true,
        numara: true,
        slug: true,
        kat: true,
        olusturulma_tarihi: true,
        blok: {
          select: {
            ad: true,
            slug: true,
            proje: {
              select: {
                ad: true,
                slug: true,
              },
            },
          },
        },
      },
    });
    if (!daire) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    return NextResponse.json({
      ...daire,
      blok_ad: daire.blok.ad,
      blok_slug: daire.blok.slug,
      proje_ad: daire.blok.proje.ad,
      proje_slug: daire.blok.proje.slug,
    });
  } catch (error) {
    console.error("Daire detayı getirilirken hata:", error);
    return NextResponse.json({ error: "Daire detayı getirilemedi" }, { status: 500 });
  }
} 