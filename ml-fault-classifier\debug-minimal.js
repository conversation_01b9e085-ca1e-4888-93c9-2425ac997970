console.log('🔍 Minimal Debug Başlıyor...');
console.log('Node.js versiyon:', process.version);
console.log('Platform:', process.platform);

try {
  console.log('1. process.cwd():', process.cwd());
  console.log('2. __dirname:', __dirname);
  console.log('3. require.resolve kontrolleri...');
  
  // package.json kontrol
  const packageJson = require('./package.json');
  console.log('4. package.json dependencies:', packageJson.dependencies);
  
  // fs ve path modülleri
  const fs = require('fs');
  const path = require('path');
  console.log('5. fs ve path modülleri yüklendi');
  
  // Keyword classifier
  console.log('6. KeywordClassifier yükleniyor...');
  const KeywordClassifier = require('./models/keyword-classifier');
  console.log('7. KeywordClassifier yüklendi');
  
  console.log('✅ Tüm modüller başar<PERSON><PERSON> y<PERSON>!');
  
} catch (error) {
  console.error('❌ Hata:', error.message);
  console.error('Stack:', error.stack);
} 