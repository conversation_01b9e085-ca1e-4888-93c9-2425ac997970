# 📁 Simple Media Selector Kullanım Kılavuzu

B<PERSON>ı<PERSON>, kate<PERSON><PERSON> siste<PERSON>, sadece **klasör**, **boyut**, **yükleme** ve **kırpma** odaklı basit medya seçici sisteminin nasıl kullanılacağını açıklar.

## 🎯 Temel Felsefe

Bu sistem **3 temel konfigürasyon** üzerine kurulmuştur:

1. **📁 Upload Config**: Dosya yükleme ayarları (klasör, boyut limiti, dosya tipleri)
2. **📏 Dimension Config**: He<PERSON><PERSON> boy<PERSON> (genişlik, yükseklik, oran korunması)
3. **✂️ Crop Config**: Kırpma ayarları (opsiyonel)

---

## 🚀 Hızlı Başlangıç

### Basit Kullanım

```tsx
import { 
  SimpleMediaSelector, 
  useSimpleMediaSelector,
  createUploadConfig,
  createDimensionConfig 
} from '@/components/media/SimpleMediaSelector';

function MyComponent() {
  const { selectedMedia, handleSelect } = useSimpleMediaSelector();

  const uploadConfig = createUploadConfig("my-folder");
  const dimensionConfig = createDimensionConfig(800, 600);

  return (
    <SimpleMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      uploadConfig={uploadConfig}
      dimensionConfig={dimensionConfig}
      buttonText="Resim Seç"
      showPreview={true}
    />
  );
}
```

---

## ⚙️ Konfigürasyon Detayları

### 1. 📁 Upload Configuration

```tsx
const uploadConfig = createUploadConfig("klasor-adi", {
  maxFileSize: 5 * 1024 * 1024,        // 5MB
  acceptedTypes: ['image/jpeg', 'image/png'],
  generateThumbnails: true,              // Thumbnail'ler oluştur
  quality: 85                           // JPEG kalitesi (0-100)
});
```

**Parametreler:**
- `folder` (zorunlu): Yükleme klasörü
- `maxFileSize`: Maksimum dosya boyutu (byte)
- `acceptedTypes`: Kabul edilen dosya tipleri
- `generateThumbnails`: Thumbnail oluşturulsun mu?
- `quality`: Resim kalitesi

### 2. 📏 Dimension Configuration

```tsx
const dimensionConfig = createDimensionConfig(1200, 800, {
  maintainAspectRatio: true,     // Oran korunsun mu?
  allowUpscaling: false          // Büyütmeye izin var mı?
});
```

**Parametreler:**
- `targetWidth` (zorunlu): Hedef genişlik
- `targetHeight` (zorunlu): Hedef yükseklik
- `maintainAspectRatio`: Oran korunması
- `allowUpscaling`: Küçük resimlerin büyütülmesi

### 3. ✂️ Crop Configuration (Opsiyonel)

```tsx
const cropConfig = createCropConfig(16/9, {  // 16:9 aspect ratio
  enabled: true,
  cropShape: 'rect',           // 'rect' veya 'round'
  minWidth: 400,
  minHeight: 300
});
```

**Parametreler:**
- `aspectRatio`: Kırpma oranı (width/height)
- `enabled`: Kırpma aktif mi?
- `cropShape`: Kırpma şekli
- `minWidth/minHeight`: Minimum boyutlar

---

## 📚 Kullanım Örnekleri

### 🧑‍💼 Avatar Upload (Square + Round Crop)

```tsx
function AvatarUpload() {
  const { selectedMedia, handleSelect } = useSimpleMediaSelector();

  const uploadConfig = createUploadConfig("avatars", {
    maxFileSize: 1024 * 1024, // 1MB
    acceptedTypes: ['image/jpeg', 'image/png']
  });

  const dimensionConfig = createDimensionConfig(200, 200);

  const cropConfig = createCropConfig(1, { // 1:1 square
    cropShape: 'round'
  });

  return (
    <SimpleMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      uploadConfig={uploadConfig}
      dimensionConfig={dimensionConfig}
      cropConfig={cropConfig}
      buttonText="Avatar Yükle"
      showPreview={true}
    />
  );
}
```

### 📰 Blog Featured Image (Wide Format)

```tsx
function BlogFeaturedImage() {
  const { selectedMedia, handleSelect } = useSimpleMediaSelector();

  const uploadConfig = createUploadConfig("blog/featured", {
    maxFileSize: 3 * 1024 * 1024, // 3MB
    quality: 90
  });

  const dimensionConfig = createDimensionConfig(1200, 630);

  const cropConfig = createCropConfig(1200/630); // ~16:9

  return (
    <SimpleMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      uploadConfig={uploadConfig}
      dimensionConfig={dimensionConfig}
      cropConfig={cropConfig}
      buttonText="Öne Çıkan Resim"
      showPreview={true}
    />
  );
}
```

### 🛍️ Product Gallery (Multi-select)

```tsx
function ProductGallery() {
  const { selectedItems, handleMultiSelect } = useSimpleMediaSelector();

  const uploadConfig = createUploadConfig("products", {
    maxFileSize: 5 * 1024 * 1024,
    quality: 85
  });

  const dimensionConfig = createDimensionConfig(800, 800); // Square

  const cropConfig = createCropConfig(1); // 1:1

  return (
    <SimpleMediaSelector
      onMultiSelect={handleMultiSelect}
      selectedItems={selectedItems}
      uploadConfig={uploadConfig}
      dimensionConfig={dimensionConfig}
      cropConfig={cropConfig}
      multiSelect={true}
      buttonText="Ürün Resimleri Ekle"
      showPreview={true}
    />
  );
}
```

### 🏢 Banner Image (Wide + No Aspect Ratio Lock)

```tsx
function BannerUpload() {
  const { selectedMedia, handleSelect } = useSimpleMediaSelector();

  const uploadConfig = createUploadConfig("banners", {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    quality: 95
  });

  const dimensionConfig = createDimensionConfig(1920, 400, {
    maintainAspectRatio: false, // Farklı oranlar kabul et
    allowUpscaling: true        // Büyütmeye izin ver
  });

  const cropConfig = createCropConfig(1920/400);

  return (
    <SimpleMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      uploadConfig={uploadConfig}
      dimensionConfig={dimensionConfig}
      cropConfig={cropConfig}
      buttonText="Banner Yükle"
      showPreview={true}
    />
  );
}
```

### 📄 Document Upload (No Crop)

```tsx
function DocumentUpload() {
  const { selectedMedia, handleSelect } = useSimpleMediaSelector();

  const uploadConfig = createUploadConfig("documents", {
    maxFileSize: 20 * 1024 * 1024, // 20MB
    acceptedTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    generateThumbnails: false
  });

  const dimensionConfig = createDimensionConfig(0, 0); // Boyut önemli değil

  return (
    <SimpleMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      uploadConfig={uploadConfig}
      dimensionConfig={dimensionConfig}
      // Kırpma yok
      buttonText="Belge Yükle"
      showPreview={false}
    />
  );
}
```

### 📸 Free Gallery (No Crop, Any Size)

```tsx
function FreeGallery() {
  const { selectedItems, handleMultiSelect } = useSimpleMediaSelector();

  const uploadConfig = createUploadConfig("gallery/free", {
    maxFileSize: 8 * 1024 * 1024,
    acceptedTypes: ['image/*'] // Tüm resim tipleri
  });

  const dimensionConfig = createDimensionConfig(1200, 800, {
    maintainAspectRatio: true
  });

  // Kırpma config'i yok = serbest yükleme

  return (
    <SimpleMediaSelector
      onMultiSelect={handleMultiSelect}
      selectedItems={selectedItems}
      uploadConfig={uploadConfig}
      dimensionConfig={dimensionConfig}
      multiSelect={true}
      buttonText="Serbest Galeri"
      showPreview={true}
    />
  );
}
```

---

## 🎛️ Advanced Kullanım

### Progress Tracking

```tsx
function AdvancedUpload() {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  return (
    <div>
      <SimpleMediaSelector
        // ... diğer props
        onUploadStart={() => setIsUploading(true)}
        onUploadProgress={setUploadProgress}
        onUploadComplete={() => setIsUploading(false)}
        onError={(error) => console.error("Upload error:", error)}
      />

      {isUploading && (
        <div className="mt-4">
          <div className="flex justify-between text-sm mb-2">
            <span>Yükleniyor...</span>
            <span>{uploadProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
```

### Error Handling

```tsx
function SafeUpload() {
  const { selectedMedia, handleSelect, error, handleError } = useSimpleMediaSelector();

  return (
    <div>
      <SimpleMediaSelector
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        uploadConfig={uploadConfig}
        dimensionConfig={dimensionConfig}
        onError={handleError}
      />

      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
          <p className="text-red-800 text-sm">
            Hata: {error.message}
          </p>
        </div>
      )}
    </div>
  );
}
```

### Custom Trigger

```tsx
function CustomTriggerUpload() {
  const customTrigger = (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 cursor-pointer">
      <Upload className="mx-auto mb-2 text-gray-400" size={48} />
      <p className="text-gray-600">Tıklayın veya dosyaları sürükleyin</p>
    </div>
  );

  return (
    <SimpleMediaSelector
      trigger={customTrigger}
      // ... diğer props
    />
  );
}
```

### Dynamic Configuration

```tsx
function DynamicUpload() {
  const [uploadType, setUploadType] = useState<'avatar' | 'banner' | 'product'>('avatar');

  const configs = {
    avatar: {
      upload: createUploadConfig("avatars", { maxFileSize: 1024 * 1024 }),
      dimension: createDimensionConfig(200, 200),
      crop: createCropConfig(1, { cropShape: 'round' })
    },
    banner: {
      upload: createUploadConfig("banners", { maxFileSize: 5 * 1024 * 1024 }),
      dimension: createDimensionConfig(1200, 300),
      crop: createCropConfig(4)
    },
    product: {
      upload: createUploadConfig("products", { maxFileSize: 3 * 1024 * 1024 }),
      dimension: createDimensionConfig(800, 800),
      crop: createCropConfig(1)
    }
  };

  const currentConfig = configs[uploadType];

  return (
    <div>
      {/* Type Selector */}
      <div className="flex gap-2 mb-4">
        {Object.keys(configs).map(type => (
          <Button
            key={type}
            variant={uploadType === type ? "default" : "outline"}
            onClick={() => setUploadType(type as any)}
          >
            {type.toUpperCase()}
          </Button>
        ))}
      </div>

      {/* Media Selector */}
      <SimpleMediaSelector
        uploadConfig={currentConfig.upload}
        dimensionConfig={currentConfig.dimension}
        cropConfig={currentConfig.crop}
        key={uploadType} // Re-render when type changes
        // ... diğer props
      />
    </div>
  );
}
```

---

## 🏗️ Sistem Entegrasyonu

### Backend Entegrasyonu

```tsx
// Mock upload function'ı gerçek API ile değiştirin
async function uploadFile(
  file: File, 
  uploadConfig: UploadConfig, 
  dimensionConfig: DimensionConfig,
  onProgress: (progress: number) => void
): Promise<MediaFile> {
  
  const formData = new FormData();
  formData.append('file', file);
  formData.append('folder', uploadConfig.folder);
  formData.append('targetWidth', dimensionConfig.targetWidth.toString());
  formData.append('targetHeight', dimensionConfig.targetHeight.toString());

  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
    // Upload progress tracking
    onUploadProgress: (progressEvent) => {
      const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      onProgress(progress);
    }
  });

  if (!response.ok) {
    throw new Error('Upload failed');
  }

  return await response.json();
}
```

### Database Schema

```sql
CREATE TABLE media_files (
  id SERIAL PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  size INTEGER NOT NULL,
  url VARCHAR(500) NOT NULL,
  folder VARCHAR(255) NOT NULL,
  thumbnail_small VARCHAR(500),
  thumbnail_medium VARCHAR(500),
  thumbnail_large VARCHAR(500),
  alt_text TEXT,
  caption TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

---

## 📊 Comparison: Simple vs Generic

| Özellik | Simple Media Selector | Generic Media Selector |
|---------|----------------------|----------------------|
| **Kategori Sistemi** | ❌ Yok | ✅ Var |
| **Klasör Bazlı** | ✅ Direct folder | ✅ Kategori → Klasör |
| **Konfigürasyon** | 🎯 Basit (3 config) | 🔧 Kompleks (Config object) |
| **Boyut Kontrolü** | ✅ Direct dimension | ✅ Kategori bazlı |
| **Kırpma** | ✅ Aspect ratio based | ✅ Kategori bazlı |
| **Provider Pattern** | ❌ Yok | ✅ Var |
| **Çoklu Proje** | 🔄 Manuel config | ✅ Otomatik |
| **Learning Curve** | 📈 Düşük | 📈 Orta |
| **Esneklik** | 🎯 Focused | 🚀 Unlimited |

### Ne Zaman Hangisini Kullanmalı?

**Simple Media Selector Kullan:**
- ✅ Kategorilere ihtiyacın yok
- ✅ Sadece klasör/boyut önemli
- ✅ Basit proje yapısı
- ✅ Hızlı başlangıç istiyorsun

**Generic Media Selector Kullan:**
- ✅ Kompleks kategori sistemi gerekiyor
- ✅ Çoklu proje/departman var
- ✅ Provider pattern ile merkezi yönetim
- ✅ Enterprise level çözüm

---

## 🔧 Troubleshooting

### Yaygın Sorunlar

#### 1. Dosya Yüklenmiyor
```tsx
// Kontrol edilecekler:
const uploadConfig = createUploadConfig("folder", {
  maxFileSize: 5 * 1024 * 1024, // Yeterli mi?
  acceptedTypes: ['image/*'],    // Doğru tipler var mı?
});
```

#### 2. Boyutlar Yanlış
```tsx
// Boyut ayarlarını kontrol et:
const dimensionConfig = createDimensionConfig(800, 600, {
  maintainAspectRatio: false, // Zorla boyut
  allowUpscaling: true        // Büyütmeye izin ver
});
```

#### 3. Kırpma Çalışmıyor
```tsx
// Kırpma ayarlarını kontrol et:
const cropConfig = createCropConfig(16/9, {
  enabled: true,    // Aktif mi?
  minWidth: 400,    // Minimum boyutlar uygun mu?
  minHeight: 225
});
```

---

## 📈 Performans İpuçları

### 1. File Size Optimization
```tsx
const uploadConfig = createUploadConfig("folder", {
  quality: 85,              // Kalite/boyut dengesi
  generateThumbnails: true, // Thumbnail'ler oluştur
  maxFileSize: 2 * 1024 * 1024 // Makul limit
});
```

### 2. Progressive Loading
```tsx
function ProgressiveUpload() {
  return (
    <SimpleMediaSelector
      onUploadStart={() => console.log("Upload started")}
      onUploadProgress={(progress) => console.log(`Progress: ${progress}%`)}
      onUploadComplete={(media) => console.log("Upload completed:", media)}
      // ...
    />
  );
}
```

### 3. Error Boundaries
```tsx
function SafeMediaSelector() {
  try {
    return <SimpleMediaSelector {...props} />;
  } catch (error) {
    return <div>Media selector error: {error.message}</div>;
  }
}
```

---

**Bu kılavuz ile Simple Media Selector sistemini etkili bir şekilde kullanabilirsiniz! 🚀** 