"use client"

import { useState, useEffect } from "react"
import { Plus, Edit2, Trash2, <PERSON>ertCircle, ArrowUpDown } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "sonner"
import { StatusDialog } from "@/components/settings/status-dialog"
import { DeleteStatusDialog } from "@/components/settings/delete-status-dialog"

interface Status {
  id: string
  ad: string
  sira: number
  renk: string
  aciklama?: string
  olusturulma_tarihi: string
  guncelleme_tarihi: string
}

export default function StatusManagementPage() {
  const [statuses, setStatuses] = useState<Status[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedStatus, setSelectedStatus] = useState<Status | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  useEffect(() => {
    fetchStatuses()
  }, [])

  const fetchStatuses = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/statuses")
      
      if (!response.ok) {
        throw new Error("Arıza durumları yüklenemedi")
      }

      const data = await response.json()
      setStatuses(data.statuses || [])
    } catch (error) {
      console.error("Error fetching statuses:", error)
      toast.error("Arıza durumları yüklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (status: Status) => {
    setSelectedStatus(status)
    setIsDialogOpen(true)
  }

  const handleDelete = (status: Status) => {
    setSelectedStatus(status)
    setIsDeleteDialogOpen(true)
  }

  const handleAddNew = () => {
    setSelectedStatus(null)
    setIsDialogOpen(true)
  }

  const handleDialogClose = () => {
    setIsDialogOpen(false)
    setSelectedStatus(null)
  }

  const handleDeleteDialogClose = () => {
    setIsDeleteDialogOpen(false)
    setSelectedStatus(null)
  }

  const handleSuccess = () => {
    fetchStatuses()
    handleDialogClose()
    handleDeleteDialogClose()
  }

  const getStatusColor = (color: string) => {
    return {
      backgroundColor: `${color}20`,
      borderColor: color,
      color: color
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64 mt-2" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Durum Tanımları
          </h2>
          <p className="text-muted-foreground">
            Arıza durumlarını yönetin. Sıra numarası arızaların iş akışındaki yerini belirler.
          </p>
        </div>
        <Button onClick={handleAddNew}>
          <Plus className="mr-2 h-4 w-4" />
          Yeni Durum
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Arıza Durumları ({statuses.length})</CardTitle>
          <CardDescription>
            Sistemde tanımlı tüm arıza durumları. Arızalar bu durumlar arasında geçiş yapar.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {statuses.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Durum tanımı bulunamadı</h3>
              <p className="mt-2 text-muted-foreground">
                Henüz hiç arıza durumu tanımlanmamış. İlk durumu oluşturun.
              </p>
              <Button className="mt-4" onClick={handleAddNew}>
                <Plus className="mr-2 h-4 w-4" />
                İlk Durumu Ekle
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-20">
                      <div className="flex items-center gap-1">
                        Sıra
                        <ArrowUpDown className="h-3 w-3" />
                      </div>
                    </TableHead>
                    <TableHead>Ad</TableHead>
                    <TableHead>Renk</TableHead>
                    <TableHead>Açıklama</TableHead>
                    <TableHead>Son Güncelleme</TableHead>
                    <TableHead className="text-right">İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {statuses.map((status) => (
                    <TableRow key={status.id}>
                      <TableCell className="font-mono font-bold">
                        {status.sira}
                      </TableCell>
                      <TableCell className="font-medium">
                        {status.ad}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          style={getStatusColor(status.renk)}
                          className="border-2"
                        >
                          {status.renk}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {status.aciklama || "-"}
                      </TableCell>
                      <TableCell className="text-muted-foreground text-sm">
                        {new Date(status.guncelleme_tarihi).toLocaleDateString('tr-TR')}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(status)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(status)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <StatusDialog
        status={selectedStatus}
        isOpen={isDialogOpen}
        onClose={handleDialogClose}
        onSuccess={handleSuccess}
      />

      <DeleteStatusDialog
        status={selectedStatus}
        isOpen={isDeleteDialogOpen}
        onClose={handleDeleteDialogClose}
        onSuccess={handleSuccess}
      />
    </div>
  )
} 