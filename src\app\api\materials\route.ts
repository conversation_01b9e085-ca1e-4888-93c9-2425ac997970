import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CacheManager } from "@/lib/cache"

export async function GET() {
  try {
    const cacheKey = CacheManager.generateKey("materials", { all: "true" })

    // Try to get from cache first
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for materials: ${cacheKey}`)
      return NextResponse.json(cached)
    }

    console.log(`Cache miss for materials: ${cacheKey}`)

    const malzemeler = await prisma.malzeme.findMany({
      where: {
        silindi_mi: false
      },
      orderBy: {
        ad: "asc"
      }
    })

    // Cache the result for 10 minutes
    await CacheManager.set(cacheKey, malzemeler, 600)

    return NextResponse.json(malzemeler)
  } catch (error) {
    console.error("Error fetching malzemeler:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const malzeme = await prisma.malzeme.create({
      data: {
        ad: body.ad,
        aciklama: body.aciklama,
        birim: body.birim,
        silindi_mi: false,
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date()
      }
    })

    // Invalidate materials cache after creating new material
    await CacheManager.deletePattern("materials:*")

    return NextResponse.json(malzeme)
  } catch (error) {
    console.error("Error creating malzeme:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}


