"use client"

import { useState } from "react"
import { <PERSON>ert<PERSON>riangle } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Apartment {
  id: string
  numara: string
  blok: {
    ad: string
    proje: {
      ad: string
    }
  }
  _count?: {
    arizalar: number
  }
}

interface DeleteApartmentDialogProps {
  open: boolean
  onClose: () => void
  apartment?: Apartment | null
}

export function DeleteApartmentDialog({ open, onClose, apartment }: DeleteApartmentDialogProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!apartment) return null

  const hasFaults = apartment._count && apartment._count.arizalar > 0

  const handleDelete = async () => {
    try {      setLoading(true)
      setError(null)

      const response = await fetch(`/api/apartments/${apartment.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Silme işlemi başarısız")
      }

      // Başarı bildirimi
      toast.success(`"${apartment.numara}" numaralı daire başarıyla silindi!`, {
        title: "Silindi",
        duration: 4000
      })

      onClose()
    } catch (error) {
      console.error("Error deleting apartment:", error)
      const errorMessage = error instanceof Error ? error.message : "Silme işlemi başarısız"
      
      // Hata bildirimi
      toast.error(errorMessage, {
        title: "Silme Hatası",
        duration: 6000
      })
      
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Daireyi Sil
          </DialogTitle>
          <DialogDescription>
            Bu işlem geri alınamaz. Daire ve tüm ilişkili veriler silinecek.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Silinecek Daire:</h4>
            <p className="text-sm font-medium">Daire {apartment.numara}</p>
            <p className="text-sm text-muted-foreground">Blok: {apartment.blok.ad}</p>
            <p className="text-sm text-muted-foreground">Proje: {apartment.blok.proje.ad}</p>
            {apartment._count && (
              <div className="mt-2 text-sm text-muted-foreground">
                <p>• {apartment._count.arizalar} arıza kaydı</p>
              </div>
            )}
          </div>

          {hasFaults && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Bu daire silinirse:
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li>{apartment._count?.arizalar} arıza kaydı silinecek</li>
                  <li>Arızalara ait tüm belgeler ve fotoğraflar silinecek</li>
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? "Siliniyor..." : "Sil"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
