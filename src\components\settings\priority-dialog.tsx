"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"

const prioritySchema = z.object({
  ad: z.string().min(1, "Ad alanı gereklidir"),
  seviye: z.coerce.number().min(1, "Seviye 1'den büyük olmalıdır").max(10, "Seviye 10'dan kü<PERSON>ü<PERSON> olmalıdır"),
  renk: z.string().regex(/^#[0-9A-F]{6}$/i, "Geçerli bir hex renk kodu giriniz"),
  aciklama: z.string().optional(),
})

type PriorityFormValues = z.infer<typeof prioritySchema>

interface Priority {
  id: string
  ad: string
  seviye: number
  renk: string
  aciklama?: string
}

interface PriorityDialogProps {
  priority?: Priority | null
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export function PriorityDialog({ priority, isOpen, onClose, onSuccess }: PriorityDialogProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!priority

  const form = useForm<PriorityFormValues>({
    resolver: zodResolver(prioritySchema),
    defaultValues: {
      ad: "",
      seviye: 1,
      renk: "#10B981",
      aciklama: "",
    },
  })

  useEffect(() => {
    if (priority) {
      form.reset({
        ad: priority.ad,
        seviye: priority.seviye,
        renk: priority.renk,
        aciklama: priority.aciklama || "",
      })
    } else {
      form.reset({
        ad: "",
        seviye: 1,
        renk: "#10B981",
        aciklama: "",
      })
    }
  }, [priority, form])

  const onSubmit = async (values: PriorityFormValues) => {
    try {
      setLoading(true)

      const url = isEditing ? `/api/priorities/${priority.id}` : "/api/priorities"
      const method = isEditing ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Bir hata oluştu")
      }

      toast.success(
        isEditing 
          ? "Öncelik seviyesi başarıyla güncellendi"
          : "Öncelik seviyesi başarıyla oluşturuldu"
      )

      onSuccess()
    } catch (error) {
      console.error("Priority save error:", error)
      toast.error(error instanceof Error ? error.message : "Bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Öncelik Seviyesi Düzenle" : "Yeni Öncelik Seviyesi"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Öncelik seviyesi bilgilerini güncelleyin." 
              : "Yeni bir öncelik seviyesi tanımlayın."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="ad"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ad</FormLabel>
                    <FormControl>
                      <Input placeholder="Örn: Yüksek" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="seviye"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Seviye</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1" 
                        max="10" 
                        placeholder="1-10" 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      1 (en düşük) - 10 (en yüksek)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="renk"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Renk</FormLabel>
                  <FormControl>
                    <div className="flex gap-2">
                      <Input 
                        placeholder="#10B981" 
                        {...field} 
                        className="flex-1"
                      />
                      <div 
                        className="w-10 h-10 rounded border border-input" 
                        style={{ backgroundColor: field.value }}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Hex renk kodu (örn: #10B981)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="aciklama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama (Opsiyonel)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Bu öncelik seviyesi hakkında açıklama..."
                      className="resize-none"
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                İptal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Kaydediliyor..." : isEditing ? "Güncelle" : "Oluştur"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
} 