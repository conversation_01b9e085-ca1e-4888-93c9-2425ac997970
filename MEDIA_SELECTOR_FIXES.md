# Advanced Media Selector Fixes

## Overview
Fixed critical issues in the `AdvancedMediaSelector` component used for project image selection, implementing the required workflow improvements and bug fixes.

## Issues Fixed

### 1. ✅ Image Duplication Bug
**Problem**: Cropped images appeared twice in the gallery when reopened.

**Root Cause**: The component didn't properly manage the relationship between original and cropped images, leading to duplicates being added to the media list.

**Solution**:
- Added `originalImageBeingCropped` state to track which image is being cropped
- Improved media list update logic to replace existing images instead of always adding new ones
- Added duplicate detection based on `originalName` to prevent duplicates

**Key Changes**:
```typescript
// Track original image being cropped
const [originalImageBeingCropped, setOriginalImageBeingCropped] = useState<MediaFile | null>(null);

// Smart media list update logic
setMediaList(prev => {
  if (originalImageBeingCropped) {
    return prev.map(item => 
      item.url === originalImageBeingCropped.url ? data.media : item
    );
  }
  // Check for duplicates by name
  const exists = prev.some(item => item.originalName === data.media.originalName);
  if (exists) {
    return prev.map(item => 
      item.originalName === data.media.originalName ? data.media : item
    );
  }
  return [data.media, ...prev];
});
```

### 2. ✅ Auto-Crop for Upload Tab
**Problem**: Upload tab required manual steps - user had to click crop icon after file selection.

**Required**: Automatic cropping form opening when image is selected.

**Solution**:
- Modified `handlePendingFileAdd` to automatically open crop modal when file is selected
- Updated crop completion handler to automatically upload cropped image
- Removed manual upload button since upload now happens automatically after cropping

**Key Changes**:
```typescript
const handlePendingFileAdd = (file: File) => {
  setCropImageUrl(URL.createObjectURL(file));
  setCropFileIndex(0);
  setPendingFiles([file]);
  setCropModalOpen(true); // Auto-open crop modal
};
```

### 3. ✅ Gallery Display After Cropping
**Problem**: Cropped images didn't properly appear in gallery tab or refresh correctly.

**Solution**:
- Added `refreshGallery()` function for explicit gallery refresh
- Automatic tab switching to gallery after successful crop/upload
- Added visual indicators for recently uploaded images
- Improved state management for gallery updates

**Key Changes**:
```typescript
const refreshGallery = async () => {
  const res = await fetch(`/api/media?customFolder=${folder}`);
  const data = await res.json();
  setMediaList(data.data || []);
};

// Auto-switch to gallery and refresh
setTab('gallery');
setTimeout(() => refreshGallery(), 100);
```

### 4. ✅ Workflow Integration
**Problem**: Complex state management and inconsistent workflow between tabs.

**Solution**:
- Streamlined state management with proper cleanup
- Added success messages and visual feedback
- Implemented "Yeni" (New) badges for recently uploaded images
- Improved error handling and user feedback

**Key Changes**:
```typescript
// Visual feedback for new uploads
const [recentlyUploadedUrl, setRecentlyUploadedUrl] = useState<string | null>(null);
const [successMessage, setSuccessMessage] = useState<string | null>(null);

// Auto-clear indicators after 3 seconds
setTimeout(() => {
  setRecentlyUploadedUrl(null);
  setSuccessMessage(null);
}, 3000);
```

## New Workflow

### Upload Tab → Auto-Crop → Gallery → Assignment
1. **Upload Tab**: User clicks to select image file
2. **Auto-Crop**: Cropping modal opens automatically
3. **Crop & Save**: User adjusts crop area and clicks "Kırp ve Kaydet"
4. **Auto-Upload**: Image is automatically uploaded to server
5. **Gallery Switch**: Automatically switches to Gallery tab
6. **Visual Feedback**: Shows "Yeni" badge and success message
7. **Assignment**: User can click "Proje görselini ata" to assign to project

### Gallery Tab → Crop → Assignment
1. **Gallery Tab**: User selects existing image from gallery
2. **Manual Crop**: User clicks crop icon if needed
3. **Crop & Replace**: Cropped version replaces original
4. **Assignment**: User can assign to project

## Testing

Created `test-advanced-media-selector.tsx` component to verify:
- Auto-crop functionality
- Gallery refresh after upload
- No image duplication
- Complete workflow integration
- Visual feedback and success messages

## Files Modified

1. `src/components/media/AdvancedMediaSelector.tsx` - Main component fixes
2. `src/components/media/test-advanced-media-selector.tsx` - Test component (new)
3. `MEDIA_SELECTOR_FIXES.md` - This documentation (new)

## Usage

The component is used in `src/components/projects/project-dialog.tsx` for project image selection:

```typescript
<AdvancedMediaSelector
  open={mediaSelectorOpen}
  onClose={() => setMediaSelectorOpen(false)}
  onSelect={media => {
    setSelectedMedia(media);
    form.setValue('project_image_url', media?.url || '');
  }}
  selectedMedia={selectedMedia}
  title="Proje Görseli Seç"
  description="Projeye ait görseli seçin veya yükleyin."
  folder="projeler"
  acceptedTypes={["image/jpeg", "image/png", "image/webp"]}
  maxSizeMB={5}
/>
```

All requested features have been implemented and tested. The component now provides a smooth, intuitive workflow for image selection and cropping.
