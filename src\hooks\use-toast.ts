import { create } from 'zustand'

export type ToastType = 'success' | 'error' | 'warning' | 'info'

export interface Toast {
  id: string
  title?: string
  message: string
  type: ToastType
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface ToastStore {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, 'id'>) => void
  removeToast: (id: string) => void
  clearAllToasts: () => void
}

export const useToast = create<ToastStore>((set, get) => ({
  toasts: [],
  
  addToast: (toast) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: Toast = {
      id,
      duration: 5000, // 5 saniye varsayılan
      ...toast,
    }
    
    set((state) => ({
      toasts: [...state.toasts, newToast]
    }))
    
    // Otomatik silme
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        get().removeToast(id)
      }, newToast.duration)
    }
  },
  
  removeToast: (id) => {
    set((state) => ({
      toasts: state.toasts.filter((toast) => toast.id !== id)
    }))
  },
  
  clearAllToasts: () => {
    set({ toasts: [] })
  },
}))

// Helper functions for common toast types
export const toast = {
  success: (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
    useToast.getState().addToast({
      message,
      type: 'success',
      ...options,
    })
  },
  
  error: (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
    useToast.getState().addToast({
      message,
      type: 'error',
      duration: 7000, // Error mesajları daha uzun gösterilir
      ...options,
    })
  },
  
  warning: (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
    useToast.getState().addToast({
      message,
      type: 'warning',
      ...options,
    })
  },
  
  info: (message: string, options?: Partial<Omit<Toast, 'id' | 'type' | 'message'>>) => {
    useToast.getState().addToast({
      message,
      type: 'info',
      ...options,
    })
  },
  promise: <T>(
    promise: Promise<T>,
    options: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: Error) => string)
    }
  ) => {
    const { addToast, removeToast } = useToast.getState()
    
    // Loading toast ekle
    addToast({
      message: options.loading,
      type: 'info',
      duration: 0, // Loading toast'ı manuel olarak kaldıracağız
    })
    
    const loadingId = useToast.getState().toasts[useToast.getState().toasts.length - 1]?.id
    
    promise
      .then((data) => {
        if (loadingId) removeToast(loadingId)
        const successMessage = typeof options.success === 'function' 
          ? options.success(data) 
          : options.success
        toast.success(successMessage)
      })
      .catch((error) => {
        if (loadingId) removeToast(loadingId)
        const errorMessage = typeof options.error === 'function' 
          ? options.error(error) 
          : options.error
        toast.error(errorMessage)
      })
  },
}
