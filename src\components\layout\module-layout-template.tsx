import { auth } from "@/lib/auth"
import { DashboardLayout } from "@/components/layout/dashboard-layout"

export default async function ModuleLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await auth()
  
  const user = session?.user ? {
    name: session.user.name,
    email: session.user.email,
    image: session.user.image,
    role: session.user.role
  } : undefined

  return (
    <DashboardLayout user={user}>
      {children}
    </DashboardLayout>
  )
}
