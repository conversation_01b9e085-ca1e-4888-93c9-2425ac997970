"use client";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Building2 } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useLoading } from "@/contexts/loading-context";

export default function BlocksTable({ blocks, projectSlug }: { blocks: any[]; projectSlug: string }) {
  const router = useRouter();
  const { startLoading } = useLoading();

  const handleBlockClick = (blockSlug: string) => {
    startLoading();
    router.push(`/projeler/${projectSlug}/bloklar/${blockSlug}`);
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[80px]">Resim</TableHead>
          <TableHead>Blok Adı</TableHead>
          <TableHead>Daire Sayısı</TableHead>
          <TableHead>Oluşturulma</TableHead>
          <TableHead><PERSON><PERSON><PERSON>mamış Arıza</TableHead>
          <TableHead className="w-[50px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {blocks.map((block) => (
          <TableRow
            key={block.id}
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={e => {
              const target = e.target as HTMLElement;
              if (
                target.closest('a') ||
                target.closest('button') ||
                target.closest('[role="menuitem"]')
              ) {
                return;
              }
              handleBlockClick(block.slug);
            }}
          >
            <TableCell>
              <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted relative flex items-center justify-center">
                {block.block_image_url ? (
                  <Image
                    src={block.block_image_url}
                    alt={block.ad}
                    fill
                    className="object-cover"
                    sizes="48px"
                  />
                ) : (
                  <Building2 className="h-8 w-8 text-muted-foreground" />
                )}
              </div>
            </TableCell>
            <TableCell>
              <div>
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBlockClick(block.slug);
                  }}
                  className="font-medium text-blue-600 hover:underline"
                >
                  {block.ad}
                </button>
                {block.aciklama && (
                  <div className="text-sm text-muted-foreground mt-1">
                    {block.aciklama}
                  </div>
                )}
              </div>
            </TableCell>
            <TableCell>{block._count?.daireler || 0}</TableCell>
            <TableCell>{block.olusturulma_tarihi ? new Date(block.olusturulma_tarihi).toLocaleDateString("tr-TR") : "-"}</TableCell>
            <TableCell>{block.tamamlanmamis_ariza_sayisi ?? 0}</TableCell>
            <TableCell></TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
} 