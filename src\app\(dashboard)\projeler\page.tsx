import { Suspense } from "react"
import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { ProjectManagement } from "@/components/projects/project-management"

export default function ProjectsPage() {
  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Ana Say<PERSON>
        </Link>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground">Projeler</span>
      </div>

      <div>
        <h1 className="text-3xl font-bold tracking-tight"><PERSON><PERSON></h1>
        <p className="text-muted-foreground">
          <PERSON><PERSON><PERSON><PERSON> e<PERSON>in, düzenleyin ve yönetin.
        </p>
      </div>
      <Suspense fallback={<div>Projeler yükleniyor...</div>}>
        <ProjectManagement />
      </Suspense>
    </div>
  )
}
