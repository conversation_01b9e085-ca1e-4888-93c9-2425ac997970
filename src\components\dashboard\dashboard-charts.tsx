"use client"

import React, { useMemo } from "react"
import { useQuery } from "@tanstack/react-query"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from "recharts"

const faultsFetcher = async () => {
  const res = await fetch("/api/faults?limit=100")
  return res.json()
}

const faultTypesFetcher = async () => {
  const res = await fetch("/api/categories")
  return res.json()
}

const monthlyStatsFetcher = async () => {
  const res = await fetch("/api/faults/monthly-stats")
  return res.json()
}

const DashboardCharts = React.memo(() => {
  const { data: faultsData, isLoading: faultsLoading } = useQuery({
    queryKey: ["faults", "list"],
    queryFn: faultsFetcher
  })

  const { data: faultTypesData, isLoading: typesLoading } = useQuery({
    queryKey: ["fault-types"],
    queryFn: faultTypesFetcher
  })

  const { data: monthlyStats, isLoading: monthlyLoading } = useQuery({
    queryKey: ["faults", "monthly-stats"],
    queryFn: monthlyStatsFetcher
  })

  // Memoize chart data transformations
  const chartData = useMemo(() => {
    if (faultsLoading || typesLoading || monthlyLoading || !faultsData) {
      return {
        statusData: [],
        priorityData: [],
        typeData: [],
        monthlyData: []
      }
    }

    const faults = faultsData?.faults || []
    
    const statusData = [
      { name: 'Beklemede', value: faults.filter((f: any) => f.durum?.ad === 'Beklemede').length, color: '#F59E0B' },
      { name: 'Devam Ediyor', value: faults.filter((f: any) => f.durum?.ad === 'Devam Ediyor').length, color: '#8B5CF6' },
      { name: 'Tamamlandı', value: faults.filter((f: any) => f.durum?.ad === 'Tamamlandı').length, color: '#10B981' },
      { name: 'İptal', value: faults.filter((f: any) => f.durum?.ad === 'İptal').length, color: '#6B7280' },
    ]

    const priorityData = [
      { name: 'Düşük', value: faults.filter((f: any) => f.aciliyet?.ad === 'Düşük').length, color: '#10B981' },
      { name: 'Orta', value: faults.filter((f: any) => f.aciliyet?.ad === 'Orta').length, color: '#F59E0B' },
      { name: 'Yüksek', value: faults.filter((f: any) => f.aciliyet?.ad === 'Yüksek').length, color: '#F97316' },
      { name: 'Kritik', value: faults.filter((f: any) => f.aciliyet?.ad === 'Kritik').length, color: '#EF4444' },
    ]

    const typeData = faultTypesData?.categories?.map((type: any) => ({
      name: type.ad,
      value: faults.filter((f: any) => f.tip?.id === type.id).length,
      color: type.renk || '#6B7280'
    })) || []

    const monthlyData = monthlyStats?.data || [
      { month: 'Oca', faults: 12 },
      { month: 'Şub', faults: 19 },
      { month: 'Mar', faults: 15 },
      { month: 'Nis', faults: 22 },
      { month: 'May', faults: 18 },
      { month: 'Haz', faults: 25 },
    ]

    return {
      statusData,
      priorityData,
      typeData,
      monthlyData
    }
  }, [faultsData, faultTypesData, monthlyStats, faultsLoading, typesLoading, monthlyLoading])

  if (faultsLoading || typesLoading || monthlyLoading || !faultsData) {
    return (
      <div className="space-y-6">
        <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
        </div>
      </div>
    )
  }

  const { statusData, priorityData, typeData, monthlyData } = chartData

  return (
    <div className="space-y-6">
      {/* Monthly Trend Chart */}
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-gray-800">
            Aylık Arıza Trendi
          </CardTitle>
          <p className="text-sm text-gray-600">
            Son 6 ay boyunca bildirilen arıza sayıları
          </p>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis 
                  dataKey="month" 
                  stroke="#6B7280"
                  fontSize={12}
                />
                <YAxis 
                  stroke="#6B7280"
                  fontSize={12}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="faults" 
                  stroke="#3B82F6" 
                  strokeWidth={3}
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, stroke: '#3B82F6', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Status and Priority Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Distribution */}
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">
              Arıza Durumları
            </CardTitle>
            <p className="text-sm text-gray-600">
              Arızaların mevcut durumlarına göre dağılımı
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Priority Distribution */}
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">
              Aciliyet Seviyeleri
            </CardTitle>
            <p className="text-sm text-gray-600">
              Arızaların aciliyet seviyelerine göre dağılımı
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={priorityData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis 
                    dataKey="name" 
                    stroke="#6B7280"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="#6B7280"
                    fontSize={12}
                  />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                  <Bar dataKey="value" fill="#3B82F6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Arıza Tipleri Chart */}
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-800">
            Arıza Tipleri
          </CardTitle>
          <p className="text-sm text-gray-600">
            Arıza kategorilerine göre dağılım
          </p>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={typeData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis 
                  type="number"
                  stroke="#6B7280"
                  fontSize={12}
                />
                <YAxis 
                  type="category"
                  dataKey="name"
                  stroke="#6B7280"
                  fontSize={12}
                  width={100}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #E5E7EB',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Bar dataKey="value" fill="#3B82F6" radius={[0, 4, 4, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

DashboardCharts.displayName = "DashboardCharts"

export default DashboardCharts
