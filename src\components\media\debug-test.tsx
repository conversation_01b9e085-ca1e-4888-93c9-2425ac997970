"use client"

import React, { useState } from 'react';
import { AdvancedMediaSelector, MediaFile } from './AdvancedMediaSelector';

export default function DebugTest() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile | undefined>(undefined);
  const [testLog, setTestLog] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestLog(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[DEBUG TEST] ${message}`);
  };

  const handleSelect = (media: MediaFile | undefined) => {
    setSelectedMedia(media);
    addLog(`Media selected: ${media ? media.originalName : 'None'}`);
  };

  const handleClose = () => {
    setIsOpen(false);
    addLog('Modal closed');
  };

  const handleOpen = () => {
    setIsOpen(true);
    addLog('Modal opened');
  };

  const clearLogs = () => {
    setTestLog([]);
  };

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">🔍 Debug Test - Image Duplication Issue</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Test Controls */}
        <div className="space-y-6">
          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            <div className="space-y-4">
              <button
                onClick={handleOpen}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition w-full"
              >
                Open Media Selector
              </button>
              
              <button
                onClick={clearLogs}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition w-full"
              >
                Clear Logs
              </button>
            </div>
          </div>

          <div className="border rounded-lg p-6 bg-yellow-50">
            <h3 className="text-lg font-semibold mb-3">🧪 Test Procedure:</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Click "Open Media Selector"</li>
              <li>Go to Upload tab</li>
              <li>Select an image file (auto-crop should open)</li>
              <li>Complete cropping process</li>
              <li>Verify image appears in Gallery tab</li>
              <li>Close modal</li>
              <li>Reopen modal</li>
              <li><strong>Check for duplicates in Gallery tab</strong></li>
              <li>Repeat steps 6-8 multiple times</li>
            </ol>
          </div>

          <div className="border rounded-lg p-6 bg-red-50">
            <h3 className="text-lg font-semibold mb-3">🚨 Expected Issue:</h3>
            <p className="text-sm">
              Despite all our fixes, duplicate images should still appear in the gallery 
              when the modal is reopened. This proves the current architecture is 
              fundamentally flawed.
            </p>
          </div>
        </div>

        {/* Test Results */}
        <div className="space-y-6">
          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Selected Media</h2>
            {selectedMedia ? (
              <div className="flex items-center gap-4">
                <img 
                  src={selectedMedia.url} 
                  alt={selectedMedia.originalName} 
                  className="w-24 h-24 object-cover rounded border"
                />
                <div>
                  <div className="font-medium">{selectedMedia.originalName}</div>
                  <div className="text-sm text-gray-500">{(selectedMedia.size / 1024).toFixed(2)} KB</div>
                  <div className="text-xs text-blue-600 break-all">{selectedMedia.url}</div>
                </div>
              </div>
            ) : (
              <div className="text-gray-500">No media selected</div>
            )}
          </div>

          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Test Log</h2>
            <div className="bg-black text-green-400 p-4 rounded font-mono text-xs max-h-96 overflow-y-auto">
              {testLog.length === 0 ? (
                <div className="text-gray-500">No logs yet...</div>
              ) : (
                testLog.map((log, index) => (
                  <div key={index}>{log}</div>
                ))
              )}
            </div>
          </div>

          <div className="border rounded-lg p-6 bg-blue-50">
            <h3 className="text-lg font-semibold mb-3">📊 Console Debug:</h3>
            <p className="text-sm mb-2">
              Open browser console (F12) to see detailed logging:
            </p>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>🎯 Upload source tracking</li>
              <li>📋 Media list updates</li>
              <li>🚨 Duplicate detection alerts</li>
              <li>📊 Deduplication statistics</li>
            </ul>
          </div>
        </div>
      </div>

      <AdvancedMediaSelector
        open={isOpen}
        onClose={handleClose}
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        title="Debug Test - Media Selector"
        description="Testing for image duplication issues"
        folder="projeler"
        acceptedTypes={["image/jpeg", "image/png", "image/webp"]}
        maxSizeMB={5}
      />
    </div>
  );
}
