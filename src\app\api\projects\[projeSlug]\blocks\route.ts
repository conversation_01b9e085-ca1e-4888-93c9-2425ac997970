import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string }> }
) {
  try {
    const { projeSlug } = await params;
    const project = await prisma.proje.findFirst({
      where: { slug: projeSlug, silindi_mi: false },
      select: { id: true },
    });
    if (!project) {
      return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    }
    // Tamamlanmamış arıza durumlarını bul
    const openStatuses = await prisma.arizaDurum.findMany({
      where: {
        ad: { in: ["Açık", "Beklemede"] },
        silindi_mi: false,
      },
      select: { id: true },
    });
    const openStatusIds = openStatuses.map((s) => s.id);

    // Blokları getirirken tamamlanmamış arıza sayılarını da ekle
    const blocks = await prisma.blok.findMany({
      where: { proje_id: project.id, silindi_mi: false },
      select: {
        id: true,
        ad: true,
        slug: true,
        olusturulma_tarihi: true,
        _count: { select: { daireler: { where: { silindi_mi: false } } } },
        daireler: { select: { id: true, silindi_mi: true } },
      },
      orderBy: { ad: "asc" },
    });

    // Her blok için tamamlanmamış arıza sayısını hesapla
    const blocksWithFaults = await Promise.all(blocks.map(async (block) => {
      const apartmentIds = block.daireler.filter(d => !d.silindi_mi).map(d => d.id);
      let incompleteFaultCount = 0;
      if (apartmentIds.length > 0) {
        incompleteFaultCount = await prisma.ariza.count({
          where: {
            daire_id: { in: apartmentIds },
            durum_id: { in: openStatusIds },
            silindi_mi: false,
          },
        });
      }
      return {
        ...block,
        tamamlanmamis_ariza_sayisi: incompleteFaultCount,
        daireler: undefined, // gereksiz veri göndermemek için
      };
    }));
    return NextResponse.json({ blocks: blocksWithFaults });
  } catch (error) {
    console.error("Bloklar getirilirken hata:", error);
    return NextResponse.json({ error: "Bloklar getirilemedi" }, { status: 500 });
  }
}
