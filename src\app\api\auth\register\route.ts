import { NextRequest, NextResponse } from "next/server"
import bcrypt from "bcryptjs"
import { prisma } from "@/lib/prisma"
import { registerSchema } from "@/lib/validations/auth"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate the request body
    const validatedData = registerSchema.parse(body)    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        email: validatedData.email,
      },
    })

    if (existingUser) {
      return NextResponse.json(
        { message: "Bu email adresi ile kayıtlı bir kullanıcı zaten var." },
        { status: 400 }
      )
    }

    // Check if apartment is already taken
    const existingApartmentUser = await prisma.user.findFirst({
      where: {
        daire_id: validatedData.apartmentId,
        silindi_mi: false,
      },
    })

    if (existingApartmentUser) {
      return NextResponse.json(
        { message: "Bu daire için zaten bir kullanıcı kayıtlı." },
        { status: 400 }
      )
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create the user
    const user = await prisma.user.create({
      data: {        ad: validatedData.firstName,
        soyad: validatedData.lastName,
        email: validatedData.email,
        telefon: validatedData.phone,
        sifre: hashedPassword,
        // daireId: validatedData.apartmentId, // This field doesn't exist in schema
        rol: "USER", // Default role for registered users
        durum: "PENDING", // Pending approval
        // emailDogrulandiMi: false, // This field name is different
        olusturan_id: null, // Self-registered
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date(),
      },
    })

    // TODO: Send email verification
    // TODO: Notify admins of new registration

    return NextResponse.json(
      {
        message: "Kayıt başarılı. Email doğrulama ve yönetici onayı bekleniyor.",
        userId: user.id,
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Registration error:", error)
    
    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json(
        { message: "Geçersiz veri formatı." },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Kayıt işlemi sırasında hata oluştu." },
      { status: 500 }
    )
  }
}
