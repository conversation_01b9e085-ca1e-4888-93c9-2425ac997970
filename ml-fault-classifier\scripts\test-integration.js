const fetch = require('node-fetch');

const ML_SERVICE_URL = 'http://localhost:3050';
const MAIN_APP_URL = 'http://localhost:3001';

async function testIntegration() {
  console.log('🧪 ML Servisi Entegrasyon Testi Başlıyor...\n');

  try {
    // 1. ML Servisinin durumunu kontrol et
    console.log('1️⃣ ML Servis Durumu Kontrolü');
    const healthResponse = await fetch(`${ML_SERVICE_URL}/health`);
    const health = await healthResponse.json();
    console.log('   ✅ ML Servisi:', health.status);
    console.log('   📊 Mevcut Kategoriler:', health.availableCategories);
    console.log('');

    // 2. Ana projeden kategorileri al
    console.log('2️⃣ Ana Proje <PERSON>');
    const categoriesResponse = await fetch(`${MAIN_APP_URL}/api/categories`);
    const categoriesData = await categoriesResponse.json();
    console.log('   📋 Toplam Kategori:', categoriesData.categories?.length || 0);
    categoriesData.categories?.forEach(cat => {
      console.log(`   - ${cat.ad} (${cat.id})`);
    });
    console.log('');

    // 3. ML servisinden kategorileri al
    console.log('3️⃣ ML Servisi Kategorileri');
    const mlCategoriesResponse = await fetch(`${ML_SERVICE_URL}/categories`);
    const mlCategoriesData = await mlCategoriesResponse.json();
    console.log('   🤖 ML Kategorileri:', mlCategoriesData.total);
    console.log('   🔗 Kategori Mapping:', mlCategoriesData.ml_categories);
    console.log('');

    // 4. Test tahminleri yap
    console.log('4️⃣ Test Tahminleri');
    const testCases = [
      {
        title: "Banyo musluğu damlatıyor",
        description: "Banyo musluğundan sürekli su damlıyor, tamir edilmesi gerekiyor"
      },
      {
        title: "Elektrik kesintisi yaşanıyor",
        description: "Sürekli elektrik kesintisi oluyor, sigorta atıyor"
      },
      {
        title: "Duvar boyası dökülüyor",
        description: "Salon duvarındaki boya kabarıp dökülüyor"
      },
      {
        title: "Asansör çalışmıyor",
        description: "Asansör düğmesine basıldığında çalışmıyor"
      }
    ];

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`   Test ${i + 1}: ${testCase.title}`);
      
      const predictionResponse = await fetch(`${ML_SERVICE_URL}/predict`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testCase)
      });
      
      const prediction = await predictionResponse.json();
      
      if (prediction.success && prediction.prediction.success) {
        console.log(`   ✅ Tahmin: ${prediction.prediction.db_category?.ad || prediction.prediction.category}`);
        console.log(`   📊 Güven: ${(prediction.prediction.confidence * 100).toFixed(1)}%`);
      } else {
        console.log(`   ❌ Tahmin başarısız: ${prediction.prediction.message}`);
      }
      console.log('');
    }

    // 5. Ana proje API'si üzerinden test
    console.log('5️⃣ Ana Proje API Testi');
    const mainApiResponse = await fetch(`${MAIN_APP_URL}/api/ml-predict`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: "Klima çalışmıyor",
        description: "Klima açıldığında soğuk hava gelmiyor, arızalı görünüyor"
      })
    });
    
    const mainApiResult = await mainApiResponse.json();
    if (mainApiResult.success) {
      console.log('   ✅ Ana proje API çalışıyor');
      console.log(`   🤖 Tahmin: ${mainApiResult.prediction.db_category?.ad || 'Kategori bulunamadı'}`);
    } else {
      console.log('   ❌ Ana proje API hatası:', mainApiResult.error);
    }
    console.log('');

    console.log('🎉 Entegrasyon testi tamamlandı!');

  } catch (error) {
    console.error('❌ Test hatası:', error.message);
  }
}

// Test'i çalıştır
testIntegration(); 