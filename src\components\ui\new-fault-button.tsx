"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import { useLoading } from "@/contexts/loading-context"

interface NewFaultButtonProps {
  projeSlug: string
  blokSlug: string
  daireSlug: string
}

export function NewFaultButton({ projeSlug, blokSlug, daireSlug }: NewFaultButtonProps) {
  const router = useRouter()
  const { startLoading } = useLoading()

  const handleClick = () => {
    startLoading()
    router.push(`/projeler/${projeSlug}/bloklar/${blokSlug}/${daireSlug}/arizalar/yeni`)
  }

  return (
    <Button onClick={handleClick} className="inline-flex items-center px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
      <PlusCircle className="mr-2 h-4 w-4" />
      <PERSON><PERSON>
    </Button>
  )
} 