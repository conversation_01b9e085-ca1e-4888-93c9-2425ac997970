# Next.js/Node.js Projeleri i<PERSON><PERSON> Best Practices Rehberi

Bu rehber, modern bir Next.js/Node.js projesinde kod kalitesi, güvenlik, sürdürülebilirlik ve ekip verimliliği için uygulanması gereken en iyi uygulamaları (best practices) özetler. Her başlık altında açıklamalar ve örnekler bulabilirsiniz.

---

## 1. Kod <PERSON><PERSON><PERSON> ve Linting

- **ESLint ve Prettier kullanın.**
- **Otomatik formatlama ve linting scriptleri ekleyin:**

```json
// package.json
"scripts": {
  "lint": "eslint .",
  "format": "prettier --write ."
}
```

- **CI/CD'de lint ve format kontrolü zorunlu olsun.**

---

## 2. Git Akışı ve Sürüm Kontrolü

- **Feature branch ile geliştirme:**
  ```bash
  git checkout -b feature/ozellik-adi
  ```
- **Merge son<PERSON> branch silin:**
  ```bash
  git branch -d feature/ozellik-adi
  git push origin --delete feature/ozellik-adi
  ```
- **Rebase ile temiz geçmiş:**
  ```bash
  git checkout feature/ozellik-adi
  git rebase main
  ```
- **Sürüm etiketi ekleyin:**
  ```bash
  git tag -a v1.0.0 -m "First release"
  git push --tags
  ```

---

## 3. Çevresel Değişkenler ve Konfigürasyon

- **.env dosyası kullanın, .env.example ile örnek sağlayın.**
- **Konfigürasyonları koddan ayırın:**
  ```js
  // config.js
  export const API_URL = process.env.NEXT_PUBLIC_API_URL;
  ```
- **Gizli anahtarları asla repoya koymayın!**

---

## 4. Güvenlik

- **Helmet, rate limiter, CORS gibi güvenlik middleware’leri kullanın.**
- **Kullanıcı girişlerinde ve API endpointlerinde input validation yapın.**
- **JWT veya session tabanlı kimlik doğrulama kullanın.**
- **Yetkilendirme kontrollerini merkezi bir policy/fonksiyon ile yönetin.**
- **XSS, CSRF, SQL Injection gibi saldırılara karşı koruma sağlayın.**

---

## 5. Test ve Otomasyon

- **Jest, Testing Library veya Cypress ile testler yazın.**
- **Her yeni özellik için test ekleyin, coverage’i takip edin.**
- **CI/CD pipeline’ında testler otomatik çalışsın.**
- **Test ortamı için ayrı .env.test dosyası kullanın.**

---

## 6. CI/CD Pipeline

- **Her push/PR’da otomatik olarak lint, test ve build adımlarını çalıştırın.**
- **Başarısız olan adımda deploy’u engelleyin.**
- **Örnek GitHub Actions workflow:**

```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - run: npm run lint
      - run: npm test
      - run: npm run build
```

---

## 7. Loglama ve İzleme

- **Logları merkezi bir servise (Datadog, Papertrail, ELK, Sentry) yönlendirin.**
- **Hata ve performans izleme için Sentry, NewRelic, Datadog gibi araçlar entegre edin.**
- **Kritik hatalarda Slack/email bildirimi kurun.**

---

## 8. Modülerlik ve Kodda Tekrarı Azaltma

- **DRY (Don’t Repeat Yourself) prensibini uygulayın.**
- **Her component/fonksiyon tek bir iş yapsın (Single Responsibility).**
- **Reusable component ve helper fonksiyonlar oluşturun.**
- **Büyük dosyaları küçük, anlamlı parçalara bölün.**

---

## 9. Performans ve Optimizasyon

- **Resimleri next/image ile optimize edin, WebP kullanın.**
- **API response’larını cache’leyin (Redis, SWR, React Query).**
- **Lazily load edin, code splitting uygulayın.**
- **Memoization (React.memo, useMemo, useCallback) ile gereksiz render’ı önleyin.**

---

## 10. Dokümantasyon ve Onboarding

- **Güncel ve anlaşılır bir README.md dosyası bulundurun.**
- **Kurulum, geliştirme, deploy ve test adımlarını açıkça yazın.**
- **CHANGELOG.md ile sürüm değişikliklerini takip edin.**
- **Her yeni fonksiyon/endpoint için JSDoc/TSDoc ile açıklama ekleyin.**

---

## 11. Otomasyon ve Güvenlik Araçları

- **Dependabot, npm audit, snyk gibi araçlarla bağımlılık güvenliğini otomatik takip edin.**
- **Hassas veri ve anahtarlar için secret management kullanın (ör. GitHub Secrets, AWS Secrets Manager).**

---

## 12. Ekstra: Sık Kullanılan Komutlar ve Scriptler

- **Lint ve test scriptleri:**
  ```bash
  npm run lint
  npm test
  ```
- **Branch oluşturma ve silme:**
  ```bash
  git checkout -b feature/ozellik
  git branch -d feature/ozellik
  git push origin --delete feature/ozellik
  ```
- **Versiyon etiketi ekleme:**
  ```bash
  git tag -a v1.0.0 -m "First release"
  git push --tags
  ```

---

> Bu rehber, modern Next.js/Node.js projelerinde sürdürülebilir, güvenli ve yüksek kaliteli yazılım geliştirmek için temel ve ileri düzey en iyi uygulamaları kapsar. Ekip ihtiyaçlarına göre özelleştirilebilir ve genişletilebilir. 