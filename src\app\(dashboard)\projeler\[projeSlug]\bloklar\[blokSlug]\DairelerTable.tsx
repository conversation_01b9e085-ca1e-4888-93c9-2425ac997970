"use client";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { Home } from "lucide-react";
import { useRouter } from "next/navigation";
import { useLoading } from "@/contexts/loading-context";

export default function DairelerTable({ apartments, projeSlug, blokSlug }: { apartments: any[], projeSlug: string, blokSlug: string }) {
  const router = useRouter();
  const { startLoading } = useLoading();

  const handleApartmentClick = (apartmentSlug: string) => {
    startLoading();
    router.push(`/projeler/${projeSlug}/bloklar/${blokSlug}/${apartmentSlug}`);
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[80px]">İkon</TableHead>
          <TableHead>Daire No</TableHead>
          <TableHead>Kat</TableHead>
          <TableHead>O<PERSON>şturulma</TableHead>
          <TableHead className="w-[50px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {apartments.map((apartment: any) => (
          <TableRow
            key={apartment.id}
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => handleApartmentClick(apartment.slug)}
          >
            <TableCell>
              <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center">
                <Home className="h-8 w-8 text-muted-foreground" />
              </div>
            </TableCell>
            <TableCell className="font-medium text-blue-600 underline">
              {apartment.numara}
            </TableCell>
            <TableCell>{apartment.kat}</TableCell>
            <TableCell>{apartment.olusturulma_tarihi ? new Date(apartment.olusturulma_tarihi).toLocaleDateString("tr-TR") : "-"}</TableCell>
            <TableCell></TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
} 