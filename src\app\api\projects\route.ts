import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { CacheManager } from "@/lib/cache"

// Validation schema
const projectSchema = z.object({
  ad: z.string().min(1, "Proje adı gereklidir").max(100, "Proje adı çok uzun"),
  slug: z.string().min(1, "Slug gereklidir").max(100, "Slug çok uzun"),
  aciklama: z.string().optional(),
  adres: z.string().optional(),
  baslangic_tarihi: z.string().transform((str) => new Date(str)),
  bitis_tarihi: z.string().transform((str) => new Date(str)).optional().nullable(),
  project_image_url: z.string().optional().nullable(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Check if this is a simple dropdown request
    const simple = searchParams.get("simple") === "true"
    
    if (simple) {
      // Generate cache key for simple projects
      const cacheKey = CacheManager.generateKey("projects", { simple: "true" })
      
      // Try to get from cache first
      const cached = await CacheManager.get(cacheKey)
      if (cached) {
        console.log(`Cache hit for simple projects: ${cacheKey}`)
        return NextResponse.json(cached)
      }

      console.log(`Cache miss for simple projects: ${cacheKey}`)

      // Simple format for dropdowns
      const projects = await prisma.proje.findMany({
        where: {
          silindi_mi: false,
        },
        select: {
          id: true,
          ad: true,
        },
        orderBy: {
          ad: "asc",
        },
      })

      // Cache the result for 10 minutes
      await CacheManager.set(cacheKey, projects, 600)

      return NextResponse.json(projects)
    }

    // Generate cache key for full projects
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Filters
    const arama = searchParams.get("search")

    const cacheKey = CacheManager.generateKey("projects", {
      page,
      limit,
      search: arama,
    })

    // Try to get from cache first
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for projects: ${cacheKey}`)
      return NextResponse.json(cached)
    }

    console.log(`Cache miss for projects: ${cacheKey}`)

    // Build where clause
    const where: Record<string, unknown> = {
      silindi_mi: false,
    }

    // Search filter
    if (arama) {
      where.OR = [
        { ad: { contains: arama, mode: "insensitive" } },
        { aciklama: { contains: arama, mode: "insensitive" } },
        { adres: { contains: arama, mode: "insensitive" } },
      ]
    }

    // Get total count
    const total = await prisma.proje.count({ where })

    // Get projects with counts (include all fields including project_image_url)
    const projects = await prisma.proje.findMany({
      where,
      select: {
        id: true,
        ad: true,
        slug: true,
        aciklama: true,
        adres: true,
        baslangic_tarihi: true,
        bitis_tarihi: true,
        project_image_url: true,
        olusturulma_tarihi: true,
        _count: {
          select: {
            bloklar: {
              where: { silindi_mi: false }
            }
          }
        }
      },
      orderBy: {
        olusturulma_tarihi: "desc",
      },
      skip,
      take: limit,
    })    // Tamamlanmamış arıza durumlarını bul
    const openStatuses = await prisma.arizaDurum.findMany({
      where: {
        ad: { in: ["Açık", "Beklemede"] },
        silindi_mi: false,
      },
      select: { id: true },
    });
    const openStatusIds = openStatuses.map((s) => s.id);
    // Add fault count for each project
    const projectsWithCounts = await Promise.all(
      projects.map(async (project: { id: string; _count: { bloklar: number } }) => {
        const faultCount = await prisma.ariza.count({
          where: {
            daire: {
              blok: {
                proje_id: project.id,
                silindi_mi: false
              },
              silindi_mi: false
            },
            durum_id: { in: openStatusIds },
            silindi_mi: false
          }
        })

        return {
          ...project,
          _count: {
            bloklar: project._count.bloklar,
            arizalar: faultCount
          }
        }
      })
    )

    const result = {
      projects: projectsWithCounts,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalCount: total,
        limit: limit
      }
    }

    // Cache the result for 5 minutes
    await CacheManager.set(cacheKey, result, 300)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching projects:", error)
    return NextResponse.json(
      { message: "Projeler yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = projectSchema.parse(body)
    
    // Check if slug already exists
    const existingProject = await prisma.proje.findFirst({
      where: {
        slug: validatedData.slug,
        silindi_mi: false,
      },
    })

    if (existingProject) {
      return NextResponse.json(
        { message: "Bu slug zaten kullanılıyor. Lütfen farklı bir slug kullanın." },
        { status: 400 }
      )
    }
    
    // TODO: Get user from session
    const userId = "placeholder-user-id"

    const project = await prisma.proje.create({
      data: {
        ...validatedData,
        olusturan_id: userId,
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date(),
      },
      include: {
        _count: {
          select: {
            bloklar: {
              where: { silindi_mi: false }
            }
          }
        }
      }
    })

    // Invalidate projects cache after creating new project
    await CacheManager.deletePattern("projects:*")

    return NextResponse.json(project, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Geçersiz veri", errors: error.errors },
        { status: 400 }
      )
    }
    
    console.error("Error creating project:", error)
    return NextResponse.json(
      { message: "Proje oluşturulurken hata oluştu" },
      { status: 500 }
    )
  }
}
