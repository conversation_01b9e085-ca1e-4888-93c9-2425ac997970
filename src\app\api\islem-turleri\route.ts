import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CacheManager } from "@/lib/cache"

export async function GET() {
  try {
    const cacheKey = CacheManager.generateKey("islem-turleri", { all: "true" })

    // Try to get from cache first
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for islem-turleri: ${cacheKey}`)
      return NextResponse.json(cached)
    }

    console.log(`Cache miss for islem-turleri: ${cacheKey}`)

    const islemTurleri = await prisma.islemTuru.findMany({
      where: {
        silindi_mi: false
      },
      orderBy: [
        { kategori: "asc" },
        { ad: "asc" }
      ]
    })

    // Cache the result for 15 minutes
    await CacheManager.set(cacheKey, islemTurleri, 900)

    return NextResponse.json(islemTurleri)
  } catch (error) {
    console.error("Error fetching i<PERSON><PERSON> türleri:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
