import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const category = await prisma.arizaTip.findUnique({
      where: {
        id: id,
        silindi_mi: false,
      },
    })

    if (!category) {
      return NextResponse.json(
        { message: "Kategori bulunamadı" },
        { status: 404 }
      )
    }

    return NextResponse.json({ category })
  } catch (error) {
    console.error("Error fetching category:", error)
    return NextResponse.json(
      { message: "Kategori yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { ad, aciklama, renk, ikon } = body

    if (!ad) {
      return NextResponse.json(
        { message: "Kategori adı gereklidir" },
        { status: 400 }
      )
    }

    const category = await prisma.arizaTip.update({
      where: {
        id: id,
        silindi_mi: false,
      },
      data: {
        ad,
        aciklama,
        renk: renk || "#6B7280",
        ikon,
        guncelleyen_id: "system", // TODO: Get from session
      },
    })

    return NextResponse.json({
      message: "Kategori başarıyla güncellendi",
      category,
    })
  } catch (error) {
    console.error("Error updating category:", error)
    return NextResponse.json(
      { message: "Kategori güncellenirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if category is being used
    const faultCount = await prisma.ariza.count({
      where: {
        ariza_tip_id: id,
        silindi_mi: false,
      },
    })

    if (faultCount > 0) {
      return NextResponse.json(
        { message: `Bu kategori ${faultCount} arızada kullanılıyor. Önce arızaları başka kategoriye taşıyın.` },
        { status: 400 }
      )
    }

    await prisma.arizaTip.update({
      where: {
        id: id,
      },
      data: {
        silindi_mi: true,
        silinme_tarihi: new Date(),
        silen_id: "system", // TODO: Get from session
      },
    })

    return NextResponse.json({
      message: "Kategori başarıyla silindi",
    })
  } catch (error) {
    console.error("Error deleting category:", error)
    return NextResponse.json(
      { message: "Kategori silinirken hata oluştu" },
      { status: 500 }
    )
  }
} 