"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"

interface CloseAppointmentModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  appointmentId: string
  appointmentDate: string
  faultNumber: string
  faultTitle: string
  onConfirm: (closeFaultToo: boolean) => Promise<void>
  loading?: boolean
}

export function CloseAppointmentModal({
  open,
  onOpenChange,
  appointmentId,
  appointmentDate,
  faultNumber,
  faultTitle,
  onConfirm,
  loading = false
}: CloseAppointmentModalProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleConfirm = async (closeFaultToo: boolean) => {
    setIsLoading(true)
    try {
      await onConfirm(closeFaultToo)
      onOpenChange(false)
    } catch (error) {
      console.error("Error closing appointment:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (!isLoading) {
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleCancel}>
      <DialogContent className="sm:max-w-md max-w-full w-full overflow-x-hidden box-border max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Randevu Kapatma Onayı
          </DialogTitle>
          <DialogDescription className="text-left">
            Bu arıza altında başka aktif randevu bulunmuyor. Randevu kaydı kapatılacak, 
            randevu kaydı ile birlikte arıza kaydı da kapatılsın mı?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 break-words w-full max-w-full overflow-x-hidden">
          {/* Randevu Bilgileri */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-full w-full overflow-x-hidden break-words box-border">
            <h4 className="font-medium text-blue-900 mb-2">Randevu Bilgileri</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 flex-wrap w-full max-w-full overflow-x-hidden">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-blue-800 break-words">{appointmentDate}</span>
              </div>
              <div className="flex items-center gap-2 flex-wrap w-full max-w-full overflow-x-hidden">
                <Badge variant="outline" className="text-blue-700 border-blue-300 break-words max-w-full w-full">
                  Arıza #{faultNumber}
                </Badge>
                <span className="text-blue-800 break-words max-w-full w-full">{faultTitle}</span>
              </div>
            </div>
          </div>

          {/* Seçenekler */}
          <div className="space-y-3 w-full max-w-full overflow-x-hidden">
            <div className="bg-green-50 border border-green-200 rounded-lg p-3 w-full max-w-full overflow-x-hidden">
              <h5 className="font-medium text-green-900 mb-1 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Evet, İkisini de Kapat
              </h5>
              <p className="text-sm text-green-700 break-words w-full max-w-full">
                Randevu tamamlandı olarak işaretlenir ve arıza çözüldü olarak kapatılır.
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 w-full max-w-full overflow-x-hidden">
              <h5 className="font-medium text-blue-900 mb-1 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Hayır, Sadece Randevuyu Kapat
              </h5>
              <p className="text-sm text-blue-700 break-words w-full max-w-full">
                Randevu tamamlandı olarak işaretlenir ama arıza beklemede durumunda kalır.
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2 flex-wrap w-full max-w-full overflow-x-hidden">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading || loading}
            className="w-full sm:w-auto"
          >
            İptal
          </Button>
          
          <Button
            variant="outline"
            onClick={() => handleConfirm(false)}
            disabled={isLoading || loading}
            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? "İşleniyor..." : "Hayır, Sadece Randevuyu Kapat"}
          </Button>
          
          <Button
            onClick={() => handleConfirm(true)}
            disabled={isLoading || loading}
            className="w-full sm:w-auto bg-green-600 hover:bg-green-700"
          >
            {isLoading ? "İşleniyor..." : "Evet, İkisini de Kapat"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 