"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { useLoading } from "@/contexts/loading-context"
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Calendar,
  MapPin,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface Fault {
  id: string
  slug: string
  numara: string
  baslik: string
  aciklama: string
  olusturulma_tarihi: string
  guncelleme_tarihi: string
  tip: {
    id: string
    ad: string
    renk: string
  }
  durum: {
    id: string
    ad: string
    renk: string
  }
  aciliyet: {
    id: string
    ad: string
    renk: string
    seviye: number
  }
  daire: {
    id: string
    numara: string
    blok: {
      id: string
      ad: string
      proje: {
        id: string
        ad: string
        slug: string
      }
      slug: string
    }
    slug: string
  }
  bildiren: {
    id: string
    ad: string
    soyad: string
    resim?: string
  }
  atanan?: {
    id: string
    ad: string
    soyad: string
    resim?: string
  }
}

interface FaultsListProps {
  searchParams: Record<string, string | string[] | undefined>
}

export function FaultsList({ searchParams }: FaultsListProps) {
  const router = useRouter()
  const { startLoading } = useLoading()
  const [faults, setFaults] = useState<Fault[]>([])
  const [loading, setLoading] = useState(true)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)

  const handleRowClick = (fault: Fault) => {
    startLoading()
    router.push(`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.slug}`)
  }

  const fetchFaults = useCallback(async () => {
    try {
      setLoading(true)

      // URL parametrelerini oluştur
      const params = new URLSearchParams()
      Object.entries(searchParams).forEach(([key, value]) => {
        if (value) {
          if (Array.isArray(value)) {
            value.forEach((v) => params.append(key, v as string))
          } else {
            params.set(key, value as string)
          }
        }
      })

      // Daire ID'si veya slug'ları varsa slug tabanlı endpointi kullan
      const { projeSlug, blokSlug, daireSlug, daire } = searchParams;
      let endpoint = '/api/faults';
      if (projeSlug && blokSlug && daireSlug) {
        endpoint = `/api/projects/${projeSlug}/blocks/${blokSlug}/apartments/${daireSlug}/arizalar`;
      } else if (daire) {
        // Eğer sadece daire ID'si varsa, slug tabanlı endpoint kullan
        endpoint = `/api/apartments/${daire}/arizalar`;
      }

      const response = await fetch(`${endpoint}?${params.toString()}`)
      if (!response.ok) {
        throw new Error("Arızalar yüklenemedi")
      }

      const data = await response.json()
      setFaults(data.faults || [])
      setTotal(data.total || 0)
      setCurrentPage(data.page || 1)
    } catch (error) {
      console.error("Arızalar yüklenirken hata:", error)
    } finally {
      setLoading(false)
    }
  }, [searchParams])

  useEffect(() => {
    fetchFaults()
  }, [fetchFaults])
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Açık":
      case "Beklemede":
        return <Clock className="h-4 w-4" />
      case "Devam Ediyor":
        return <AlertTriangle className="h-4 w-4" />
      case "Çözüldü":
      case "Tamamlandı":
        return <CheckCircle className="h-4 w-4" />
      case "İptal":
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">Arızalar yükleniyor...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (faults.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold">Henüz arıza bulunmuyor</h3>
            <p className="text-muted-foreground mb-4">
              Yeni arıza eklemek için butona tıklayın
            </p>
            <Button asChild>
              <Link href={searchParams.projeSlug && searchParams.blokSlug && searchParams.daireSlug ? `/projeler/${searchParams.projeSlug}/bloklar/${searchParams.blokSlug}/${searchParams.daireSlug}/arizalar/yeni` : "#"}>Yeni Arıza Ekle</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Arıza Listesi ({total})</span>
          <div className="text-sm text-muted-foreground">
            Sayfa {currentPage}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Arıza</TableHead>
                <TableHead className="w-[120px]">Durum</TableHead>
                <TableHead className="w-[100px]">Öncelik</TableHead>
                <TableHead className="w-[250px]">Konum</TableHead>
                <TableHead className="w-[150px]">Oluşturan</TableHead>
                <TableHead className="w-[150px]">Tarih</TableHead>
                <TableHead className="w-[80px]">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {faults.map((fault) => (
                <TableRow
                  key={fault.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleRowClick(fault)}
                >
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <Link
                          href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.slug}`}
                          className="font-medium hover:underline"
                          onClick={(e) => e.stopPropagation()}
                        >
                          #{fault.numara}
                        </Link>
                        <Badge
                          variant="outline"
                          style={{ backgroundColor: fault.tip.renk + "20" }}
                        >
                          {fault.tip.ad}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-1">
                        {fault.baslik}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      style={{ backgroundColor: fault.durum.renk + "20", borderColor: fault.durum.renk }}
                    >
                      {getStatusIcon(fault.durum.ad)}
                      <span className="ml-1">{fault.durum.ad}</span>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      style={{ backgroundColor: fault.aciliyet.renk + "20", borderColor: fault.aciliyet.renk }}
                    >
                      {fault.aciliyet.ad}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1 text-sm">
                      <MapPin className="h-3 w-3" />
                      <span>
                        {fault.daire.blok.proje.ad} - {fault.daire.blok.ad} - D.
                        {fault.daire.numara}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      {fault.bildiren ? (
                        <span>{fault.bildiren.ad} {fault.bildiren.soyad}</span>
                      ) : (
                        <span>-</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(fault.olusturulma_tarihi)}</span>
                    </div>
                  </TableCell>
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Menüyü aç</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.slug}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            Görüntüle
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.slug}/duzenle`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Düzenle
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Sil
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
