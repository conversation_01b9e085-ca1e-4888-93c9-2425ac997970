import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Clock } from "lucide-react"

export function DashboardScheduled() {
  const scheduledFaults = [
    {
      id: "ARZ-2023-045",
      title: "Blok A, Daire 12 - <PERSON>",
      status: "in-progress",
      statusLabel: "Devam Ediyor",
      time: "14:00 - 15:00"
    },
    {
      id: "ARZ-2023-052",
      title: "Blok B, Daire 5 - Elektrik Arızası",
      status: "assigned",
      statusLabel: "Atandı",
      time: "10:00 - 11:30"
    },
    {
      id: "ARZ-2023-058",
      title: "Blok C, Daire 8 - Kapı Tamiri",
      status: "new",
      statusLabel: "Yeni",
      time: "16:00 - 17:00"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800"
      case "assigned":
        return "bg-cyan-100 text-cyan-800"
      case "in-progress":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-semibold text-gray-800">
            Bugün Planlanan Arızalar
          </CardTitle>
          <span className="text-sm text-gray-500">5 arıza</span>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {scheduledFaults.map((fault) => (
          <div 
            key={fault.id}
            className="p-3 rounded-md border border-gray-200 hover:bg-gray-50 cursor-pointer"
          >
            <div className="flex justify-between items-start">
              <div>
                <p className="font-medium">{fault.id}</p>
                <p className="text-sm text-gray-500">{fault.title}</p>
              </div>
              <Badge className={getStatusColor(fault.status)}>
                {fault.statusLabel}
              </Badge>
            </div>
            <div className="flex items-center mt-2 text-sm text-gray-500">
              <Clock className="w-4 h-4 mr-1" />
              <span>{fault.time}</span>
            </div>
          </div>
        ))}
        
        <div className="mt-4 text-center">
          <Button variant="link" className="text-sm text-blue-600 hover:text-blue-800">
            Tümünü Görüntüle
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
