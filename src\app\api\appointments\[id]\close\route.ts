import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { auth } from "@/lib/auth"

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const appointmentId = resolvedParams.id
    const { closeFaultToo, checkOnly } = await request.json()

    // 1. Randevuyu bul ve kontrol et
    const appointment = await prisma.randevu.findFirst({
      where: { 
        id: appointmentId,
        silindi_mi: false
      },
      include: {
        ariza: {
          include: {
            durum: true
          }
        }
      }
    })

    if (!appointment) {
      return NextResponse.json(
        { error: "Randevu bulunamadı" },
        { status: 404 }
      )
    }

    // 2. <PERSON>r<PERSON>za altındaki aktif randevuları kontrol et (PLANLI ve DEVAM_EDIYOR)
    const activeAppointments = await prisma.randevu.count({
      where: {
        ariza_id: appointment.ariza_id,
        id: { not: appointmentId },
        durum: { in: ['PLANLI', 'DEVAM_EDIYOR'] },
        silindi_mi: false
      }
    })

    // Eğer sadece kontrol yapılıyorsa, aktif randevu sayısını döndür
    if (checkOnly) {
      return NextResponse.json({
        activeAppointments,
        hasActiveAppointments: activeAppointments > 0
      })
    }

    // 3. Transaction ile işlemleri gerçekleştir
    const result = await prisma.$transaction(async (tx) => {
      // Randevuyu TAMAMLANDI yap
      const updatedAppointment = await tx.randevu.update({
        where: { id: appointmentId },
        data: {
          durum: "TAMAMLANDI",
          guncelleme_tarihi: new Date(),
          guncelleyen_id: session.user.id
        }
      })

      let faultStatusUpdated = false
      let faultStatusName = ""

      // Eğer başka aktif randevu yoksa ve kullanıcı arızayı da kapatmak istiyorsa
      let cozulduDurumu = await tx.arizaDurum.findFirst({
        where: { ad: "Çözüldü" }
      });
      if (!cozulduDurumu) {
        // Fallback: sadece 'Tamamlandı'
        cozulduDurumu = await tx.arizaDurum.findFirst({
          where: { ad: "Tamamlandı" }
        });
      }
      if (cozulduDurumu) {
        await tx.ariza.update({
          where: { id: appointment.ariza_id },
          data: {
            durum_id: cozulduDurumu.id,
            gercek_bitis_tarihi: new Date(),
            guncelleme_tarihi: new Date(),
            guncelleyen_id: session.user.id
          }
        });

        // Arıza geçmişine kayıt ekle
        await tx.arizaGecmis.create({
          data: {
            ariza_id: appointment.ariza_id,
            durum_id: cozulduDurumu.id,
            kullanici_id: session.user.id,
            yorum: "Randevu tamamlandı, arıza çözüldü/tamamlandı olarak kapatıldı"
          }
        });

        faultStatusUpdated = true;
        faultStatusName = cozulduDurumu.ad;
      }

      return {
        appointment: updatedAppointment,
        faultStatusUpdated,
        faultStatusName,
        activeAppointments
      }
    })

    // 4. Başarı mesajı hazırla
    let message = "Randevu başarıyla tamamlandı"
    let details = ""

    if (result.faultStatusUpdated) {
      if (closeFaultToo) {
        message = "Randevu ve arıza başarıyla kapatıldı"
        details = `Randevu tamamlandı ve arıza "${result.faultStatusName}" durumuna alındı.`
      } else {
        message = "Randevu tamamlandı, arıza beklemede"
        details = `Randevu tamamlandı ve arıza "${result.faultStatusName}" durumuna alındı.`
      }
    } else if (result.activeAppointments > 0) {
      details = `Arıza altında ${result.activeAppointments} aktif randevu bulunduğu için arıza durumu değiştirilmedi.`
    }

    return NextResponse.json({
      success: true,
      message,
      details,
      data: result
    })

  } catch (error) {
    console.error("Error closing appointment:", error)
    return NextResponse.json(
      { 
        error: "Randevu kapatılırken hata oluştu",
        details: "Randevu kapatıldı ancak arıza durumu güncellenemedi."
      },
      { status: 500 }
    )
  }
} 