const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');
const KeywordClassifier = require('../models/keyword-classifier');

const PORT = process.env.PORT || 3050;
const MAIN_APP_URL = process.env.MAIN_APP_URL || 'http://localhost:3001';
const CONFIDENCE_THRESHOLD = parseFloat(process.env.CONFIDENCE_THRESHOLD) || 0.1;

// Global classifier instance
let classifier = null;
let isModelLoaded = false;
let availableCategories = new Map();
let categoryMapping = new Map();

// CORS headers
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || 'http://localhost:3001');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', process.env.CORS_CREDENTIALS === 'true');
}

// JSON response helper
function sendJSON(res, statusCode, data) {
  setCORSHeaders(res);
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data));
}

// Model yükleme fonksiyonu
async function loadModel() {
  try {
    console.log('🤖 ML Model yükleniyor...');
    
    const trainingDataPath = path.join(__dirname, '../data/training-data.json');
    const trainingData = JSON.parse(fs.readFileSync(trainingDataPath, 'utf8'));
    
    classifier = new KeywordClassifier();
    classifier.setConfidenceThreshold(CONFIDENCE_THRESHOLD);
    classifier.train(trainingData);
    
    isModelLoaded = true;
    console.log('✅ ML Model başarıyla yüklendi!');
    
    return true;
  } catch (error) {
    console.error('❌ Model yükleme hatası:', error.message);
    return false;
  }
}

// Mevcut kategorileri güncelle
async function updateAvailableCategories() {
  try {
    const response = await fetch(`${MAIN_APP_URL}/api/categories`);
    if (response.ok) {
      const data = await response.json();
      availableCategories.clear();
      categoryMapping.clear();
      
      data.categories.forEach(cat => {
        availableCategories.set(cat.id, {
          id: cat.id,
          ad: cat.ad,
          aciklama: cat.aciklama,
          renk: cat.renk,
          ikon: cat.ikon
        });
        
        const mlCategory = findBestMatchingCategory(cat.ad);
        if (mlCategory) {
          categoryMapping.set(mlCategory, cat.id);
        }
      });
      
      console.log(`📊 ${availableCategories.size} kategori güncellendi`);
      return true;
    }
  } catch (error) {
    console.error('❌ Kategori güncelleme hatası:', error.message);
  }
  return false;
}

// ML kategorisini DB kategorisiyle eşleştir
function findBestMatchingCategory(dbCategoryName) {
  const mlCategories = [
    'Su Tesisatı', 'Elektrik', 'Boyama', 'Yapı', 'Güvenlik', 
    'Isıtma/Soğutma', 'Asansör', 'Çevre Düzenleme'
  ];
  
  const normalizedDbName = dbCategoryName.toLowerCase();
  
  for (const mlCat of mlCategories) {
    const normalizedMlName = mlCat.toLowerCase();
    
    if (normalizedDbName === normalizedMlName) {
      return mlCat;
    }
    
    if (normalizedDbName.includes(normalizedMlName) || normalizedMlName.includes(normalizedDbName)) {
      return mlCat;
    }
  }
  
  return null;
}

// Request body parser
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
    req.on('error', reject);
  });
}

// Route handler
async function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // CORS preflight
  if (method === 'OPTIONS') {
    setCORSHeaders(res);
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    // Health check
    if (method === 'GET' && pathname === '/health') {
      sendJSON(res, 200, {
        status: 'ok',
        timestamp: new Date().toISOString(),
        modelLoaded: isModelLoaded,
        availableCategories: availableCategories.size,
        service: 'ML Fault Classifier API',
        version: '3.0.0 (HTTP Native)'
      });
      return;
    }

    // Status
    if (method === 'GET' && pathname === '/status') {
      if (!isModelLoaded || !classifier) {
        sendJSON(res, 503, {
          error: 'Model henüz yüklenmedi',
          status: 'loading'
        });
        return;
      }
      
      const status = classifier.getStatus();
      sendJSON(res, 200, {
        status: 'ready',
        model: status,
        availableCategories: availableCategories.size,
        categoryMapping: Object.fromEntries(categoryMapping),
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Predict
    if (method === 'POST' && pathname === '/predict') {
      if (!isModelLoaded || !classifier) {
        sendJSON(res, 503, {
          error: 'Model henüz yüklenmedi',
          status: 'loading'
        });
        return;
      }

      const body = await parseBody(req);
      const { title, description, categories } = body;
      
      if (!title || !description) {
        sendJSON(res, 400, {
          error: 'Başlık ve açıklama gereklidir',
          required: ['title', 'description']
        });
        return;
      }
      
      if (!categories || !Array.isArray(categories) || categories.length === 0) {
        sendJSON(res, 400, {
          error: 'Kategori listesi gereklidir',
          required: ['title', 'description', 'categories']
        });
        return;
      }
      
      // ML tahmini yap
      const prediction = classifier.predict(title, description);
      
      // Gönderilen kategoriler arasından en uygun olanı bul
      let bestMatch = null;
      let bestScore = 0;
      
      categories.forEach(cat => {
        const categoryName = cat.ad || cat.name || cat.title;
        if (!categoryName) return;
        
        // ML kategorisi ile DB kategorisini karşılaştır
        const mlCategory = findBestMatchingCategory(categoryName);
        if (mlCategory && prediction.all_scores) {
          const score = prediction.all_scores.find(s => s.category === mlCategory);
          if (score && score.confidence > bestScore) {
            bestScore = score.confidence;
            bestMatch = {
              id: cat.id,
              ad: cat.ad,
              aciklama: cat.aciklama,
              renk: cat.renk,
              ikon: cat.ikon,
              confidence: score.confidence,
              matchedWords: score.matchedWords,
              totalWords: score.totalWords
            };
          }
        }
      });
      
      // Sonuç hazırla
      const result = {
        success: bestMatch !== null && bestMatch.confidence >= classifier.confidenceThreshold,
        selectedCategory: bestMatch,
        originalPrediction: prediction,
        availableCategories: categories.length,
        timestamp: new Date().toISOString()
      };
      
      if (!result.success) {
        result.message = 'Gönderilen kategoriler arasında uygun bir eşleşme bulunamadı';
      }
      
      sendJSON(res, 200, result);
      return;
    }

    // Categories
    if (method === 'GET' && pathname === '/categories') {
      if (!isModelLoaded || !classifier) {
        sendJSON(res, 503, {
          error: 'Model henüz yüklenmedi',
          status: 'loading'
        });
        return;
      }
      
      const categories = Array.from(availableCategories.values());
      
      sendJSON(res, 200, {
        success: true,
        categories: categories,
        total: categories.length,
        ml_categories: Object.fromEntries(categoryMapping),
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Categories refresh
    if (method === 'POST' && pathname === '/categories/refresh') {
      console.log('🔄 Kategoriler güncelleniyor...');
      
      const success = await updateAvailableCategories();
      
      if (success) {
        sendJSON(res, 200, {
          success: true,
          message: 'Kategoriler başarıyla güncellendi',
          availableCategories: availableCategories.size,
          categoryMapping: Object.fromEntries(categoryMapping),
          timestamp: new Date().toISOString()
        });
      } else {
        sendJSON(res, 500, {
          error: 'Kategoriler güncellenemedi',
          timestamp: new Date().toISOString()
        });
      }
      return;
    }

    // Online Learning - Yeni örnek öğret
    if (method === 'POST' && pathname === '/learn') {
      if (!isModelLoaded || !classifier) {
        sendJSON(res, 503, {
          error: 'Model henüz yüklenmedi',
          status: 'loading'
        });
        return;
      }

      const body = await parseBody(req);
      const { title, description, correctCategory } = body;
      
      if (!title || !description || !correctCategory) {
        sendJSON(res, 400, {
          error: 'Başlık, açıklama ve doğru kategori gereklidir',
          required: ['title', 'description', 'correctCategory']
        });
        return;
      }
      
      try {
        const result = classifier.learnFromExample(title, description, correctCategory);
        
        sendJSON(res, 200, {
          success: true,
          message: 'Örnek başarıyla öğrenildi',
          learningResult: result,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        sendJSON(res, 500, {
          error: 'Öğrenme sırasında hata oluştu',
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
      return;
    }

    // Model reload
    if (method === 'POST' && pathname === '/reload') {
      console.log('🔄 Model yeniden yükleniyor...');
      
      const success = await loadModel();
      
      if (success) {
        sendJSON(res, 200, {
          success: true,
          message: 'Model başarıyla yeniden yüklendi',
          timestamp: new Date().toISOString()
        });
      } else {
        sendJSON(res, 500, {
          error: 'Model yeniden yüklenemedi',
          timestamp: new Date().toISOString()
        });
      }
      return;
    }

    // 404 Not Found
    sendJSON(res, 404, {
      error: 'Endpoint bulunamadı',
      availableEndpoints: [
        'GET /health',
        'GET /status',
        'POST /predict',
        'GET /categories',
        'POST /categories/refresh',
        'POST /learn',
        'POST /reload'
      ]
    });

  } catch (error) {
    console.error('Request hatası:', error.message);
    sendJSON(res, 500, {
      error: 'Sunucu hatası',
      message: error.message
    });
  }
}

// Server başlatma
async function startServer() {
  try {
    const modelLoaded = await loadModel();
    
    if (!modelLoaded) {
      console.error('❌ Model yüklenemedi, sunucu başlatılamıyor');
      process.exit(1);
    }
    
    console.log('📊 Kategoriler güncelleniyor...');
    await updateAvailableCategories();
    
    const server = http.createServer(handleRequest);
    
    server.listen(PORT, () => {
      console.log(`🚀 ML Fault Classifier HTTP Server başlatıldı!`);
      console.log(`   📍 Port: ${PORT}`);
      console.log(`   🔗 URL: http://localhost:${PORT}`);
      console.log(`   📊 Health Check: http://localhost:${PORT}/health`);
      console.log(`   🤖 Model Durumu: http://localhost:${PORT}/status`);
      console.log(`   🎯 Tahmin API: http://localhost:${PORT}/predict`);
      console.log(`   📋 Kategoriler: http://localhost:${PORT}/categories`);
      console.log(`   🔄 Kategori Güncelleme: http://localhost:${PORT}/categories/refresh`);
      console.log('');
      console.log(`✅ Native HTTP Server hazır! ${availableCategories.size} kategori ile entegre edildi.`);
    });
    
    server.on('error', (error) => {
      console.error('❌ Server hatası:', error.message);
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ Server başlatma hatası:', error.message);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Server kapatılıyor...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Server kapatılıyor...');
  process.exit(0);
});

// Server'ı başlat
startServer(); 