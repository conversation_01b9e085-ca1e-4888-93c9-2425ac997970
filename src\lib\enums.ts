/**
 * Merkezi Enum Yönetim Sistemi
 * Tüm enum değerleri ve etiketleri bu dosyadan yönetilir
 */

// ===== ARIZA DURUMLARI =====
export enum FaultStatus {
  ACIK = "ACIK",
  DEVAM_EDIYOR = "DEVAM_EDIYOR", 
  BEKLEMEDE = "BEKLEMEDE",
  COZULDU = "COZULDU",
  IPTAL = "IPTAL",
}

export const FAULT_STATUS_LABELS = {
  [FaultStatus.ACIK]: "Açık",
  [FaultStatus.DEVAM_EDIYOR]: "Devam Ediyor",
  [FaultStatus.BEKLEMEDE]: "Beklemede", 
  [FaultStatus.COZULDU]: "Ç<PERSON><PERSON>üldü",
  [FaultStatus.IPTAL]: "İptal",
} as const

export const FAULT_STATUS_COLORS = {
  [FaultStatus.ACIK]: "bg-red-100 text-red-800 border-red-200",
  [FaultStatus.DEVAM_EDIYOR]: "bg-blue-100 text-blue-800 border-blue-200",
  [FaultStatus.BEKLEMEDE]: "bg-yellow-100 text-yellow-800 border-yellow-200",
  [FaultStatus.COZULDU]: "bg-green-100 text-green-800 border-green-200", 
  [FaultStatus.IPTAL]: "bg-gray-100 text-gray-800 border-gray-200",
} as const

export const FAULT_STATUS_DESCRIPTIONS = {
  [FaultStatus.ACIK]: "Yeni bildirilen, henüz işleme alınmamış arızalar",
  [FaultStatus.DEVAM_EDIYOR]: "Üzerinde aktif olarak çalışılan arızalar",
  [FaultStatus.BEKLEMEDE]: "Malzeme, onay veya dış faktör bekleyen arızalar",
  [FaultStatus.COZULDU]: "Tamamen çözülmüş ve kapatılmış arızalar",
  [FaultStatus.IPTAL]: "İptal edilmiş veya geçersiz arızalar",
} as const

// ===== ACİLİYET SEVİYELERİ =====
export enum Priority {
  DUSUK = "DUSUK",
  ORTA = "ORTA", 
  YUKSEK = "YUKSEK",
  KRITIK = "KRITIK",
}

export const PRIORITY_LABELS = {
  [Priority.DUSUK]: "Düşük",
  [Priority.ORTA]: "Orta",
  [Priority.YUKSEK]: "Yüksek", 
  [Priority.KRITIK]: "Kritik",
} as const

export const PRIORITY_COLORS = {
  [Priority.DUSUK]: "bg-green-100 text-green-800 border-green-200",
  [Priority.ORTA]: "bg-yellow-100 text-yellow-800 border-yellow-200",
  [Priority.YUKSEK]: "bg-orange-100 text-orange-800 border-orange-200",
  [Priority.KRITIK]: "bg-red-100 text-red-800 border-red-200",
} as const

export const PRIORITY_LEVELS = {
  [Priority.DUSUK]: 1,
  [Priority.ORTA]: 2,
  [Priority.YUKSEK]: 3,
  [Priority.KRITIK]: 4,
} as const

// ===== KULLANICI ROLLERİ =====
export enum UserRole {
  ADMIN = "ADMIN",
  MANAGER = "MANAGER",
  TECHNICIAN = "TECHNICIAN", 
  USER = "USER",
}

export const USER_ROLE_LABELS = {
  [UserRole.ADMIN]: "Yönetici",
  [UserRole.MANAGER]: "Yönetici",
  [UserRole.TECHNICIAN]: "Teknisyen",
  [UserRole.USER]: "Kullanıcı",
} as const

// ===== KULLANICI DURUMLARI =====
export enum UserStatus {
  PENDING = "PENDING",
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  BLOCKED = "BLOCKED",
}

export const USER_STATUS_LABELS = {
  [UserStatus.PENDING]: "Bekliyor",
  [UserStatus.ACTIVE]: "Aktif",
  [UserStatus.INACTIVE]: "Pasif", 
  [UserStatus.BLOCKED]: "Bloklu",
} as const

// ===== DAİRE TİPLERİ =====
export enum ApartmentType {
  BIR_ARTI_SIFIR = "BIR_ARTI_SIFIR",
  BIR_ARTI_BIR = "BIR_ARTI_BIR",
  IKI_ARTI_BIR = "IKI_ARTI_BIR", 
  UC_ARTI_BIR = "UC_ARTI_BIR",
  DORT_ARTI_BIR = "DORT_ARTI_BIR",
  DUBLEKS = "DUBLEKS",
  PENTHOUSE = "PENTHOUSE",
}

export const APARTMENT_TYPE_LABELS = {
  [ApartmentType.BIR_ARTI_SIFIR]: "1+0",
  [ApartmentType.BIR_ARTI_BIR]: "1+1",
  [ApartmentType.IKI_ARTI_BIR]: "2+1",
  [ApartmentType.UC_ARTI_BIR]: "3+1", 
  [ApartmentType.DORT_ARTI_BIR]: "4+1",
  [ApartmentType.DUBLEKS]: "Dubleks",
  [ApartmentType.PENTHOUSE]: "Penthouse",
} as const

// ===== PROJE DURUMLARI =====
export enum ProjectStatus {
  AKTIF = "AKTIF",
  TAMAMLANDI = "TAMAMLANDI",
  IPTAL = "IPTAL",
  BEKLEMEDE = "BEKLEMEDE",
}

export const PROJECT_STATUS_LABELS = {
  [ProjectStatus.AKTIF]: "Aktif",
  [ProjectStatus.TAMAMLANDI]: "Tamamlandı",
  [ProjectStatus.IPTAL]: "İptal",
  [ProjectStatus.BEKLEMEDE]: "Beklemede",
} as const

// ===== RANDEVU DURUMLARI =====
export enum AppointmentStatus {
  PLANLI = "PLANLI",
  DEVAM_EDIYOR = "DEVAM_EDIYOR",
  TAMAMLANDI = "TAMAMLANDI",
  IPTAL = "IPTAL",
}

export const APPOINTMENT_STATUS_LABELS = {
  [AppointmentStatus.PLANLI]: "Planlı",
  [AppointmentStatus.DEVAM_EDIYOR]: "Devam Ediyor",
  [AppointmentStatus.TAMAMLANDI]: "Tamamlandı",
  [AppointmentStatus.IPTAL]: "İptal",
} as const

export const APPOINTMENT_STATUS_COLORS = {
  [AppointmentStatus.PLANLI]: "bg-blue-100 text-blue-800 border-blue-200",
  [AppointmentStatus.DEVAM_EDIYOR]: "bg-orange-100 text-orange-800 border-orange-200",
  [AppointmentStatus.TAMAMLANDI]: "bg-green-100 text-green-800 border-green-200",
  [AppointmentStatus.IPTAL]: "bg-gray-100 text-gray-800 border-gray-200",
} as const

export const APPOINTMENT_STATUS_DESCRIPTIONS = {
  [AppointmentStatus.PLANLI]: "Planlanmış, henüz başlamamış randevular",
  [AppointmentStatus.DEVAM_EDIYOR]: "Devam eden, aktif randevular", 
  [AppointmentStatus.TAMAMLANDI]: "Başarıyla tamamlanmış randevular",  [AppointmentStatus.IPTAL]: "İptal edilmiş randevular",
} as const

// ===== RANDEVU SONUÇ AKSİYONLARI =====
export enum AppointmentResultAction {
  FAULT_RESOLVED = "FAULT_RESOLVED",        // Arıza çözüldü - hem randevu hem arıza kapatılır
  CANCELLED = "CANCELLED",                  // İptal - hem randevu hem arıza kapatılır  
  NEXT_APPOINTMENT_REQUIRED = "NEXT_APPOINTMENT_REQUIRED", // Sonraki randevu - sadece mevcut randevu kapatılır
}

export const APPOINTMENT_RESULT_ACTION_LABELS = {
  [AppointmentResultAction.FAULT_RESOLVED]: "Arıza Çözüldü (Kapatılsın)",
  [AppointmentResultAction.CANCELLED]: "İptal Edildi", 
  [AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED]: "Sonraki Randevu Gerekli",
} as const

export const APPOINTMENT_RESULT_ACTION_COLORS = {
  [AppointmentResultAction.FAULT_RESOLVED]: "#10B981",      // Yeşil
  [AppointmentResultAction.CANCELLED]: "#6B7280",           // Gri
  [AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED]: "#3B82F6", // Mavi
} as const

export const APPOINTMENT_RESULT_ACTION_DESCRIPTIONS = {
  [AppointmentResultAction.FAULT_RESOLVED]: "Arıza tamamen çözüldü. Hem randevu hem arıza kapatılacak.",
  [AppointmentResultAction.CANCELLED]: "Randevu iptal edildi. Arıza da kapatılacak.",
  [AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED]: "Bu randevu tamamlandı ama arıza devam ediyor. Yeni randevu gerekli.",
} as const

// ===== UZMANLIK SEVİYELERİ =====
export enum ExpertiseLevel {
  BASLANGIC = "BASLANGIC",
  ORTA = "ORTA",
  ILERI = "ILERI",
  UZMAN = "UZMAN",
}

export const EXPERTISE_LEVEL_LABELS = {
  [ExpertiseLevel.BASLANGIC]: "Başlangıç",
  [ExpertiseLevel.ORTA]: "Orta",
  [ExpertiseLevel.ILERI]: "İleri",
  [ExpertiseLevel.UZMAN]: "Uzman",
} as const

export const EXPERTISE_LEVEL_COLORS = {
  [ExpertiseLevel.BASLANGIC]: "#10B981", // Yeşil
  [ExpertiseLevel.ORTA]: "#3B82F6", // Mavi
  [ExpertiseLevel.ILERI]: "#F59E0B", // Turuncu
  [ExpertiseLevel.UZMAN]: "#DC2626", // Kırmızı
} as const

export const EXPERTISE_LEVEL_DESCRIPTIONS = {
  [ExpertiseLevel.BASLANGIC]: "Temel seviye bilgi ve deneyim",
  [ExpertiseLevel.ORTA]: "Orta seviye bilgi ve deneyim",
  [ExpertiseLevel.ILERI]: "İleri seviye bilgi ve deneyim",
  [ExpertiseLevel.UZMAN]: "Uzman seviye bilgi ve deneyim",
} as const

// ===== HELPER FONKSİYONLARI =====

/**
 * Enum değerini etiketine çevirir
 */
export function getEnumLabel<T extends Record<string, string>>(
  enumValue: string,
  labelMap: T
): string {
  return labelMap[enumValue as keyof T] || enumValue
}

/**
 * Etiketi enum değerine çevirir
 */
export function getLabelEnum<T extends Record<string, string>>(
  label: string,
  labelMap: T
): string | undefined {
  return Object.keys(labelMap).find(
    key => labelMap[key as keyof T] === label
  )
}

/**
 * Enum değerlerinin listesini döndürür
 */
export function getEnumValues<T extends Record<string, string>>(enumObj: T): string[] {
  return Object.values(enumObj)
}

/**
 * Enum etiketlerinin listesini döndürür
 */
export function getEnumLabels<T extends Record<string, string>>(labelMap: T): string[] {
  return Object.values(labelMap)
}

/**
 * Select options için enum'ları çevirir
 */
export function enumToSelectOptions<T extends Record<string, string>>(
  enumObj: T,
  labelMap: Record<keyof T, string>
): Array<{ value: string; label: string }> {
  return Object.values(enumObj).map(value => ({
    value,
    label: labelMap[value as keyof T] || value
  }))
}

// ===== EXPORT ALL =====
export {
  // Legacy compatibility - deprecated, use new enums above
  FAULT_STATUS_LABELS as statusLabels,
  PRIORITY_LABELS as priorityLabels,
  FAULT_STATUS_COLORS as statusColors,
  PRIORITY_COLORS as priorityColors,
}
