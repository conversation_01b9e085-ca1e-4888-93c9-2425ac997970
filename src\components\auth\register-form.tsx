"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import Link from "next/link"
import { Eye, EyeOff, Loader2, ChevronRight } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { registerSchema, type RegisterInput } from "@/lib/validations/auth"

interface Project {
  id: string
  name: string
}

interface Block {
  id: string
  name: string
  projectId: string
}

interface Apartment {
  id: string
  number: string
  blockId: string
}

export function RegisterForm() {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)

  // Data states
  const [projects, setProjects] = useState<Project[]>([])
  const [blocks, setBlocks] = useState<Block[]>([])
  const [apartments, setApartments] = useState<Apartment[]>([])
  const [loadingProjects, setLoadingProjects] = useState(true)
  const [loadingBlocks, setLoadingBlocks] = useState(false)
  const [loadingApartments, setLoadingApartments] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    trigger,
  } = useForm<RegisterInput>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
      projectId: "",
      blockId: "",
      apartmentId: "",
      acceptTerms: false,
    },
  })

  const selectedProject = watch("projectId")
  const selectedBlock = watch("blockId")
  const acceptTerms = watch("acceptTerms")

  // Load projects on component mount
  useEffect(() => {
    const loadProjects = async () => {
      try {
        const response = await fetch("/api/projects")
        const data = await response.json()
        setProjects(data)
      } catch (error) {
        console.error("Error loading projects:", error)
      } finally {
        setLoadingProjects(false)
      }
    }

    loadProjects()
  }, [])

  // Load blocks when project changes
  useEffect(() => {
    if (selectedProject) {
      setLoadingBlocks(true)
      const loadBlocks = async () => {
        try {
          const response = await fetch(`/api/blocks?proje_id=${selectedProject}`)
          if (!response.ok) {
            throw new Error("Failed to fetch blocks")
          }
          const data = await response.json()
          setBlocks(data.blocks || [])
          setValue("blockId", "")
          setValue("apartmentId", "")
        } catch (error) {
          console.error("Error loading blocks:", error)
        } finally {
          setLoadingBlocks(false)
        }
      }

      loadBlocks()
    } else {
      setBlocks([])
      setApartments([])
      setValue("blockId", "")
      setValue("apartmentId", "")
    }
  }, [selectedProject, setValue])

  // Load apartments when block changes
  useEffect(() => {
    if (selectedBlock) {
      setLoadingApartments(true)
      const loadApartments = async () => {
        try {
          const response = await fetch(`/api/apartments?blokId=${selectedBlock}`)
          if (!response.ok) {
            throw new Error("Failed to fetch apartments")
          }
          const data = await response.json()
          setApartments(data.apartments || [])
          setValue("apartmentId", "")
        } catch (error) {
          console.error("Error loading apartments:", error)
        } finally {
          setLoadingApartments(false)
        }
      }

      loadApartments()
    } else {
      setApartments([])
      setValue("apartmentId", "")
    }
  }, [selectedBlock, setValue])

  const onSubmit = async (data: RegisterInput) => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        setError(result.message || "Kayıt işlemi başarısız oldu.")
      } else {
        setSuccess(
          "Kayıt işleminiz başarıyla tamamlandı! Email adresinizi doğrulayın ve yönetici onayını bekleyin."
        )
        setCurrentStep(4) // Success step
      }    } catch (error) {
      console.error("Registration error:", error)
      setError("Bir hata oluştu. Lütfen tekrar deneyin.")
    } finally {
      setIsLoading(false)
    }
  }

  const nextStep = async () => {
    let fieldsToValidate: (keyof RegisterInput)[] = []

    switch (currentStep) {
      case 1:
        fieldsToValidate = ["firstName", "lastName", "email", "phone"]
        break
      case 2:
        fieldsToValidate = ["password", "confirmPassword"]
        break
      case 3:
        fieldsToValidate = ["projectId", "blockId", "apartmentId", "acceptTerms"]
        break
    }

    const isValid = await trigger(fieldsToValidate)
    if (isValid) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    setCurrentStep(currentStep - 1)
  }

  if (success) {
    return (
      <div className="w-full max-w-md mx-auto text-center">
        <div className="mb-8">
          <div className="mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="h-8 w-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Kayıt Başarılı!
          </h2>
          <p className="text-gray-600">{success}</p>
        </div>

        <div className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/auth/signin">Giriş Sayfasına Dön</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Kayıt Ol</h1>
        <p className="text-gray-600 mt-2">Yeni hesap oluşturun</p>
      </div>

      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3].map((step) => (
            <div
              key={step}
              className={`flex items-center ${step < 3 ? "flex-1" : ""}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep >= step
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                {step}
              </div>
              {step < 3 && (
                <div
                  className={`flex-1 h-1 mx-2 ${
                    currentStep > step ? "bg-blue-600" : "bg-gray-200"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2 text-xs text-gray-500">
          <span>Kişisel Bilgiler</span>
          <span>Şifre</span>
          <span>Konum & Onay</span>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Step 1: Personal Information */}
        {currentStep === 1 && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">Ad</Label>
                <Input
                  id="firstName"
                  placeholder="Adınız"
                  {...register("firstName")}
                  className={errors.firstName ? "border-red-500" : ""}
                />
                {errors.firstName && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.firstName.message}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="lastName">Soyad</Label>
                <Input
                  id="lastName"
                  placeholder="Soyadınız"
                  {...register("lastName")}
                  className={errors.lastName ? "border-red-500" : ""}
                />
                {errors.lastName && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email Adresi</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email")}
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="phone">Telefon Numarası</Label>
              <Input
                id="phone"
                placeholder="05XXXXXXXXX"
                {...register("phone")}
                className={errors.phone ? "border-red-500" : ""}
              />
              {errors.phone && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.phone.message}
                </p>
              )}
            </div>
          </div>
        )}

        {/* Step 2: Password */}
        {currentStep === 2 && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="password">Şifre</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Şifrenizi giriniz"
                  {...register("password")}
                  className={errors.password ? "border-red-500 pr-10" : "pr-10"}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="confirmPassword">Şifre Tekrarı</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Şifrenizi tekrar giriniz"
                  {...register("confirmPassword")}
                  className={errors.confirmPassword ? "border-red-500 pr-10" : "pr-10"}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
              <p className="font-medium mb-1">Şifre gereksinimleri:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>En az 8 karakter</li>
                <li>En az bir küçük harf (a-z)</li>
                <li>En az bir büyük harf (A-Z)</li>
                <li>En az bir rakam (0-9)</li>
              </ul>
            </div>
          </div>
        )}

        {/* Step 3: Location & Terms */}
        {currentStep === 3 && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="projectId">Proje</Label>
              <Select
                value={watch("projectId")}
                onValueChange={(value) => setValue("projectId", value)}
                disabled={loadingProjects}
              >
                <SelectTrigger className={errors.projectId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Proje seçiniz" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.projectId && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.projectId.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="blockId">Blok</Label>
              <Select
                value={watch("blockId")}
                onValueChange={(value) => setValue("blockId", value)}
                disabled={!selectedProject || loadingBlocks}
              >
                <SelectTrigger className={errors.blockId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Blok seçiniz" />
                </SelectTrigger>
                <SelectContent>
                  {blocks.map((block) => (
                    <SelectItem key={block.id} value={block.id}>
                      {block.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.blockId && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.blockId.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="apartmentId">Daire</Label>
              <Select
                value={watch("apartmentId")}
                onValueChange={(value) => setValue("apartmentId", value)}
                disabled={!selectedBlock || loadingApartments}
              >
                <SelectTrigger className={errors.apartmentId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Daire seçiniz" />
                </SelectTrigger>
                <SelectContent>
                  {apartments.map((apartment) => (
                    <SelectItem key={apartment.id} value={apartment.id}>
                      Daire {apartment.number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.apartmentId && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.apartmentId.message}
                </p>
              )}
            </div>

            <div className="flex items-start space-x-2">
              <Checkbox
                id="acceptTerms"
                checked={acceptTerms}
                onCheckedChange={(checked: boolean) => setValue("acceptTerms", !!checked)}
              />
              <div className="grid gap-1.5 leading-none">
                <label
                  htmlFor="acceptTerms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Kullanım şartlarını kabul ediyorum
                </label>
                <p className="text-xs text-muted-foreground">
                  <Link href="/terms" className="underline">
                    Kullanım şartları
                  </Link>{" "}
                  ve{" "}
                  <Link href="/privacy" className="underline">
                    gizlilik politikası
                  </Link>
                  nı okudum ve kabul ediyorum.
                </p>
              </div>
            </div>
            {errors.acceptTerms && (
              <p className="text-sm text-red-500">
                {errors.acceptTerms.message}
              </p>
            )}
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          {currentStep > 1 && (
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={isLoading}
            >
              Geri
            </Button>
          )}
          
          {currentStep < 3 ? (
            <Button
              type="button"
              onClick={nextStep}
              className={currentStep === 1 ? "w-full" : "ml-auto"}
            >
              İleri
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              type="submit"
              disabled={isLoading}
              className="ml-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Kayıt yapılıyor...
                </>
              ) : (
                "Kayıt Ol"
              )}
            </Button>
          )}
        </div>

        {/* Login Link */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Zaten hesabınız var mı?{" "}
            <Link
              href="/auth/signin"
              className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
            >
              Giriş yapın
            </Link>
          </p>
        </div>
      </form>
    </div>
  )
}
