import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const randevuId = resolvedParams.id    // Randevu malzemelerini getir
    const malzemeler = await prisma.randevuMalzeme.findMany({
      where: { randevu_id: randevuId },
      include: {
        malzeme: true,
        teknisyen: {
          select: {
            id: true,
            ad: true,
            soyad: true,
            email: true,
          }
        }
      },
      orderBy: {
        olusturulma_tarihi: "asc"
      }
    })

    return NextResponse.json(malzemeler)
  } catch (error) {
    console.error("Error fetching randevu malzemeleri:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const randevuId = resolvedParams.id
    const body = await request.json()
    
    const { 
      malzeme_id, 
      teknisyen_id, 
      miktar, 
      kullanim_aciklama 
    } = body

    // Randevunun var olduğunu kontrol et
    const randevu = await prisma.randevu.findUnique({
      where: { id: randevuId }
    })

    if (!randevu) {
      return NextResponse.json(
        { error: "Randevu bulunamadı" },
        { status: 404 }
      )
    }

    // Malzeme bilgilerini al
    const malzeme = await prisma.malzeme.findUnique({
      where: { id: malzeme_id }
    })

    if (!malzeme) {
      return NextResponse.json(
        { error: "Malzeme bulunamadı" },
        { status: 404 }
      )
    }

    // Yeni malzeme kaydı oluştur
    const yeniMalzemeKaydi = await prisma.randevuMalzeme.create({
      data: {
        randevu_id: randevuId,
        malzeme_id,
        teknisyen_id,
        miktar,
        kullanim_aciklama,
      },
      include: {
        malzeme: true,
        teknisyen: {
          select: {
            id: true,
            ad: true,
            soyad: true,
            email: true,
          }
        }
      }
    })

    // TODO: Stok yönetimi gelecekte eklenecek
    // TODO: Fiyat hesaplaması gelecekte eklenecek

    return NextResponse.json(yeniMalzemeKaydi)
  } catch (error) {
    console.error("Error creating randevu malzeme:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
