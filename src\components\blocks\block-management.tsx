"use client"

import { useState, useEffect, use<PERSON>allback } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Plus, Search, MoreHorizontal, Edit, Trash2, Building, ChevronRight } from "lucide-react"
import Link from "next/link"
import { useLoading } from "@/contexts/loading-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { BlockDialog } from "./block-dialog"
import { DeleteBlockDialog } from "./delete-block-dialog"

interface Project {
  id: string
  ad: string
}

interface Block {
  id: string
  ad: string
  aciklama?: string
  proje_id: string
  proje: Project
  _count: {
    daireler: number
  }
  olusturulma_tarihi: string
}

interface ApiResponse {
  blocks: Block[]
  pagination: {
    currentPage: number
    totalPages: number
    totalCount: number
    limit: number
  }
}

export function BlockManagement({ showPageHeader = false }: { showPageHeader?: boolean }) {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { startLoading } = useLoading()
  const preselectedProjectId = searchParams.get('projeId')
  
  const [blocks, setBlocks] = useState<Block[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [showBlockDialog, setShowBlockDialog] = useState(false)
  const [editingBlock, setEditingBlock] = useState<Block | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingBlock, setDeletingBlock] = useState<Block | null>(null)

  const limit = 10

  const fetchProjects = async () => {
    try {
      const response = await fetch("/api/projects?mode=dropdown")
      if (!response.ok) {
        throw new Error("Failed to fetch projects")
      }
      const data = await response.json()
      setProjects(data.projects)
    } catch (error) {
      console.error("Error fetching projects:", error)
    }
  }
  const fetchBlocks = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        mode: "management",
        page: currentPage.toString(),
        limit: limit.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(preselectedProjectId && { proje_id: preselectedProjectId }),
      })

      const response = await fetch(`/api/blocks?${params}`)
      if (!response.ok) {
        throw new Error("Failed to fetch blocks")
      }

      const data: ApiResponse = await response.json()
      setBlocks(data.blocks)
      setTotalPages(data.pagination.totalPages)
      setTotalCount(data.pagination.totalCount)
    } catch (error) {
      console.error("Error fetching blocks:", error)
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchQuery, preselectedProjectId, limit])
  useEffect(() => {
    fetchProjects()
  }, [])
  

  
  useEffect(() => {
    fetchBlocks()
  }, [fetchBlocks])

  const handleSearch = (value: string) => {
    setSearchQuery(value)
    setCurrentPage(1)
  }

  const handleEdit = (block: Block) => {
    setEditingBlock(block)
    setShowBlockDialog(true)
  }

  const handleDelete = (block: Block) => {
    setDeletingBlock(block)
    setShowDeleteDialog(true)
  }
  const handleCreateNew = () => {
    setEditingBlock(null)
    setShowBlockDialog(true)
  }
  const handleBlockClick = (blockId: string) => {
    startLoading()
    router.push(`/daireler?blokId=${blockId}`)
  }

  const handleDialogClose = () => {
    setShowBlockDialog(false)
    setEditingBlock(null)
    fetchBlocks()
  }

  const handleDeleteDialogClose = () => {
    setShowDeleteDialog(false)
    setDeletingBlock(null)
    fetchBlocks()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR")
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Yükleniyor...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Ana Sayfa
        </Link>
        <ChevronRight className="h-4 w-4" />
        {preselectedProjectId ? (
          <>
            <Link href="/projeler" className="hover:text-foreground transition-colors">
              Projeler
            </Link>
            <ChevronRight className="h-4 w-4" />
            {!loading && projects.length > 0 ? (
              <span className="font-medium text-foreground">
                {projects.find(p => p.id === preselectedProjectId)?.ad || "Bilinmeyen Proje"}
              </span>
            ) : (
              <span className="text-muted-foreground">
                {loading ? "Yükleniyor..." : "Proje"}
              </span>
            )}
          </>
        ) : (
          <span className="font-medium text-foreground">Bloklar</span>
        )}
      </div>

      {/* Header */}
      {showPageHeader && (
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {preselectedProjectId
              ? `${projects.find(p => p.id === preselectedProjectId)?.ad || "Proje"} Blokları`
              : "Blok Yönetimi"
            }
          </h1>
          <p className="text-muted-foreground">
            {preselectedProjectId
              ? `${projects.find(p => p.id === preselectedProjectId)?.ad || "Proje"} projesine ait blokları görüntüleyin ve yönetin.`
              : "Blokları ekleyin, düzenleyin ve yönetin."
            }
          </p>
        </div>
      )}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* Arama */}
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Blok ara..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-8 w-80"
            />
          </div>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Yeni Blok
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Blok</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Daire</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {blocks.reduce((sum, b) => sum + b._count.daireler, 0)}
            </div>
          </CardContent>
        </Card>
      </div>      {/* Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Blok Adı</TableHead>
              <TableHead>Açıklama</TableHead>
              <TableHead>Daire Sayısı</TableHead>
              <TableHead>Oluşturulma</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {blocks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                  {searchQuery 
                    ? "Arama kriterlerine uygun blok bulunamadı" 
                    : "Henüz blok eklenmemiş"
                  }
                </TableCell>
              </TableRow>
            ) : (
              blocks.map((block) => (
                <TableRow 
                  key={block.id}
                  className="cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => handleBlockClick(block.id)}
                >
                  <TableCell>
                    <div className="font-medium text-blue-600">
                      {block.ad}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-muted-foreground max-w-xs truncate">
                      {block.aciklama || "-"}
                    </div>
                  </TableCell>
                  <TableCell>{block._count.daireler}</TableCell>
                  <TableCell>{formatDate(block.olusturulma_tarihi)}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEdit(block)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Düzenle
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDelete(block)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Sil
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Toplam {totalCount} bloktan {((currentPage - 1) * limit) + 1}-{Math.min(currentPage * limit, totalCount)} arası gösteriliyor
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Önceki
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter(page => 
                  page === 1 || 
                  page === totalPages || 
                  (page >= currentPage - 1 && page <= currentPage + 1)
                )
                .map((page, index, array) => (
                  <div key={page} className="flex items-center">
                    {index > 0 && array[index - 1] !== page - 1 && (
                      <span className="px-2 text-muted-foreground">...</span>
                    )}
                    <Button
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  </div>
                ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Sonraki
            </Button>
          </div>
        </div>
      )}

      {/* Dialogs */}      <BlockDialog
        open={showBlockDialog}
        onClose={handleDialogClose}
        block={editingBlock}
        projects={projects}
        preselectedProjectId={preselectedProjectId}
      />

      <DeleteBlockDialog
        open={showDeleteDialog}
        onClose={handleDeleteDialogClose}
        block={deletingBlock}
      />
    </div>
  )
}
