# Teknisyenler ve Uzmanlık Alanları Yönetimi - Yapılacaklar Checklist

## 1. Veri Modeli ve Backend
- [ ] Teknisyen modeli oluştur (ad, soyad, telefon, aktif, uzmanliklar[])
- [ ] Uzmanlık alanı modeli oluştur (ad, açıklama, renk)
- [ ] Teknisyen-uzmanlık çoklu ilişkiyi kur
- [ ] API endpointleri oluştur:
    - [ ] GET /api/technicians (listele, filtrele)
    - [ ] POST /api/technicians (ekle)
    - [ ] PUT /api/technicians/:id (güncelle)
    - [ ] DELETE /api/technicians/:id (sil)
    - [ ] GET /api/expertise-areas (listele)
    - [ ] POST /api/expertise-areas (ekle)
    - [ ] PUT /api/expertise-areas/:id (güncelle)
    - [ ] DELETE /api/expertise-areas/:id (sil)
    - [ ] POST /api/technicians/:id/expertise (ata/çıkar)
- [ ] Uzmanlık alanı arıza atama gibi başka modüllerde kullanılabilecek şekilde API'de hazırla

## 2. Frontend - UI/UX
- [ ] Teknisyenler yönetim sayfası oluştur
    - [ ] Başlık ve açıklama
    - [ ] "Yeni Teknisyen Ekle" butonu
    - [ ] Teknisyenler tablosu (ad, soyad, telefon, aktiflik, uzmanlıklar, işlemler)
    - [ ] Arama ve filtreleme (isim, uzmanlık, aktiflik)
    - [ ] Sayfalama
    - [ ] Aktif/pasif toggle
    - [ ] Uzmanlıklar renkli etiket olarak gösterilsin
    - [ ] İşlemler: Düzenle, Sil, Uzmanlık Ata
- [ ] Teknisyen ekle/düzenle modalı (ad, soyad, telefon, aktiflik, uzmanlık çoklu seçim)
- [ ] Uzmanlık ata/çıkar modalı (çoklu seçimli, mevcutlar işaretli)
- [ ] Uzmanlık alanları yönetim paneli/modalı
    - [ ] Uzmanlık alanı ekle/düzenle/sil
    - [ ] Renk seçici ile etiket rengi belirleme
- [ ] Silme işlemleri için onay modalı

## 3. Ekstra/Öneri
- [ ] Filtreleme: Uzmanlık alanına göre filtrele
- [ ] Raporlama: Hangi teknisyen hangi uzmanlıkta kaç arıza çözmüş (ileride)
- [ ] Kullanıcı deneyimi için loading, boş durum, hata mesajları
- [ ] Masaüstü için responsive ve modern tasarım

---

> **Not:** Teknisyenler uygulamayı kullanmayacak, sadece yönetim panelinde yönetilecek. Email/şifre alanı yok. Uzmanlık alanları başka modüllerde de kullanılacak şekilde tasarlanacak. Herkes ekleyip düzenleyebilecek. Mobil öncelik yok, masaüstü odaklı. 