"use client"

import { useState, useEffect } from "react"
import { User, Plus, X, Filter } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useToast } from "@/hooks/use-toast"
import { ExpertiseLevel, EXPERTISE_LEVEL_LABELS } from "@/lib/enums"

interface ExpertiseArea {
  id: string
  ad: string
  aciklama: string | null
  renk: string
  seviye: keyof typeof ExpertiseLevel
}

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
  aktifRandevuSayisi: number
  uzmanlikAlanlari: ExpertiseArea[]
}

interface AddTechnicianToAppointmentDialogProps {
  appointmentId: string
  currentTechnicians: Array<{
    id: string
    ad: string
    soyad: string
    email: string
    telefon: string | null
    resim: string | null
    uzmanlikAlanlari?: ExpertiseArea[]
  }>
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function AddTechnicianToAppointmentDialog({ 
  appointmentId,
  currentTechnicians,
  open, 
  onOpenChange, 
  onSuccess 
}: AddTechnicianToAppointmentDialogProps) {
  const [allTechnicians, setAllTechnicians] = useState<Technician[]>([])
  const [selectedTechnicians, setSelectedTechnicians] = useState<Technician[]>([])
  const [loading, setLoading] = useState(false)
  const [expertiseAreas, setExpertiseAreas] = useState<Array<{id: string, ad: string}>>([])
  const [selectedExpertiseFilter, setSelectedExpertiseFilter] = useState<string>("all")
  const { addToast } = useToast()

  // Available technicians (not already assigned) and filtered by expertise
  const availableTechnicians = allTechnicians
    .filter(tech => !currentTechnicians.find(ct => ct.id === tech.id))
    .filter(tech => {
      if (selectedExpertiseFilter === "all") return true
      return tech.uzmanlikAlanlari.some(ua => ua.id === selectedExpertiseFilter)
    })
  // Load all technicians and expertise areas
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load technicians
        const techResponse = await fetch("/api/technicians")
        if (techResponse.ok) {
          const techData = await techResponse.json()
          setAllTechnicians(techData)
        }

        // Load expertise areas
        const expertiseResponse = await fetch("/api/expertise-areas")
        if (expertiseResponse.ok) {
          const expertiseData = await expertiseResponse.json()
          setExpertiseAreas(expertiseData)
        }
      } catch (error) {
        console.error("Failed to load data:", error)
      }
    }

    if (open) {
      loadData()
      setSelectedTechnicians([])
      setSelectedExpertiseFilter("all")
    }
  }, [open])

  const handleTechnicianSelect = (technician: Technician) => {
    if (!selectedTechnicians.find(t => t.id === technician.id)) {
      setSelectedTechnicians(prev => [...prev, technician])
    }
  }

  const handleTechnicianRemove = (technicianId: string) => {
    setSelectedTechnicians(prev => prev.filter(t => t.id !== technicianId))
  }

  const handleSubmit = async () => {
    if (selectedTechnicians.length === 0) {
      addToast({
        title: "Uyarı",
        message: "En az bir teknisyen seçmelisiniz",
        type: "warning",
      })
      return
    }

    try {
      setLoading(true)

      // Get current technician IDs and add new ones
      const currentTechnicianIds = currentTechnicians.map(t => t.id)
      const newTechnicianIds = selectedTechnicians.map(t => t.id)
      const allTechnicianIds = [...currentTechnicianIds, ...newTechnicianIds]

      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          teknisyen_ids: allTechnicianIds,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to add technicians")
      }

      addToast({
        title: "Başarılı",
        message: "Teknisyenler randevuya eklendi",
        type: "success",
      })

      setSelectedTechnicians([])
      onSuccess()
    } catch (error) {
      console.error("Error adding technicians:", error)
      addToast({
        title: "Hata",
        message: "Teknisyenler eklenirken bir hata oluştu",
        type: "error",
      })
    } finally {
      setLoading(false)
    }
  }
  const handleClose = () => {
    setSelectedTechnicians([])
    setSelectedExpertiseFilter("all")
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Randevuya Teknisyen Ekle
          </DialogTitle>
          <DialogDescription>
            Bu randevuya yeni teknisyenler atayın. Birden fazla teknisyen seçebilirsiniz.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Selected Technicians */}
          {selectedTechnicians.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Seçilen Teknisyenler:</label>
              <div className="flex flex-wrap gap-2 p-3 border rounded-md bg-muted/50">
                {selectedTechnicians.map((technician) => (
                  <Badge 
                    key={technician.id} 
                    variant="secondary" 
                    className="flex items-center gap-2 pr-1"
                  >
                    <Avatar className="h-4 w-4">
                      <AvatarImage src={technician.resim || ""} />
                      <AvatarFallback className="text-xs">
                        {technician.ad.charAt(0)}{technician.soyad.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">
                      {technician.ad} {technician.soyad}
                    </span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => handleTechnicianRemove(technician.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}          {/* Available Technicians */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Mevcut Teknisyenler:</label>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={selectedExpertiseFilter} onValueChange={setSelectedExpertiseFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Uzmanlık alanına göre filtrele" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Teknisyenler</SelectItem>
                    {expertiseAreas.map((area) => (
                      <SelectItem key={area.id} value={area.id}>
                        {area.ad}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="max-h-64 overflow-y-auto border rounded-md">
              {availableTechnicians.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  Eklenebilecek teknisyen bulunmuyor
                </div>
              ) : (                <div className="p-2 space-y-1">
                  {availableTechnicians.map((technician) => (
                    <div 
                      key={technician.id}
                      className="flex items-start gap-3 p-3 rounded-md hover:bg-muted cursor-pointer border transition-colors"
                      onClick={() => handleTechnicianSelect(technician)}
                    >
                      <Avatar className="h-8 w-8 mt-1">
                        <AvatarImage src={technician.resim || ""} />
                        <AvatarFallback>
                          {technician.ad.charAt(0)}{technician.soyad.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm">
                              {technician.ad} {technician.soyad}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {technician.email}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Aktif randevu: {technician.aktifRandevuSayisi}
                            </p>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleTechnicianSelect(technician)
                            }}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Ekle
                          </Button>
                        </div>
                        
                        {/* Uzmanlık Alanları */}
                        {technician.uzmanlikAlanlari && technician.uzmanlikAlanlari.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            <TooltipProvider>
                              {technician.uzmanlikAlanlari.map((expertise) => (
                                <Tooltip key={expertise.id}>
                                  <TooltipTrigger>
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                      style={{
                                        borderColor: expertise.renk,
                                        color: expertise.renk,
                                        backgroundColor: `${expertise.renk}10`,
                                      }}
                                    >
                                      {expertise.ad}
                                      <span className="ml-1 text-[10px] opacity-70">
                                        ({EXPERTISE_LEVEL_LABELS[expertise.seviye as keyof typeof EXPERTISE_LEVEL_LABELS]})
                                      </span>
                                    </Badge>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <div className="text-center">
                                      <p className="font-medium">{expertise.ad}</p>
                                      <p className="text-xs text-muted-foreground">
                                        Seviye: {EXPERTISE_LEVEL_LABELS[expertise.seviye as keyof typeof EXPERTISE_LEVEL_LABELS]}
                                      </p>
                                      {expertise.aciklama && (
                                        <p className="text-xs mt-1">{expertise.aciklama}</p>
                                      )}
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              ))}
                            </TooltipProvider>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose}>
            İptal
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={loading || selectedTechnicians.length === 0}
          >
            {loading ? "Ekleniyor..." : `${selectedTechnicians.length} Teknisyen Ekle`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
