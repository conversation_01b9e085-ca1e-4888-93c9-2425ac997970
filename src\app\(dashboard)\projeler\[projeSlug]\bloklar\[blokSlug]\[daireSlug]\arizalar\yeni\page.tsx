"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, ChevronRight, MapPin } from "lucide-react"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { NewFaultForm } from "@/components/faults/new-fault-form"
import { Skeleton } from "@/components/ui/skeleton"

interface NewFaultPageProps {
  params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string }>
}

interface LocationInfo {
  proje: { ad: string; slug: string }
  blok: { ad: string; slug: string }
  daire: { numara: string; slug: string }
  projeId: string
  blokId: string
  daireId: string
}

export default function NewFaultPage({ params }: NewFaultPageProps) {
  const router = useRouter()
  const [locationInfo, setLocationInfo] = useState<LocationInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [resolvedParams, setResolvedParams] = useState<{ projeSlug: string; blokSlug: string; daireSlug: string } | null>(null)

  useEffect(() => {
    const loadParams = async () => {
      const resolved = await params
      setResolvedParams(resolved)
    }
    loadParams()
  }, [params])

  useEffect(() => {
    async function fetchLocationInfo() {
      if (!resolvedParams) return
      
      try {
        setLoading(true)
        const [projeRes, blokRes, daireRes] = await Promise.all([
          fetch(`/api/projects/${resolvedParams.projeSlug}`),
          fetch(`/api/projects/${resolvedParams.projeSlug}/blocks/${resolvedParams.blokSlug}`),
          fetch(`/api/projects/${resolvedParams.projeSlug}/blocks/${resolvedParams.blokSlug}/apartments/${resolvedParams.daireSlug}`)
        ])
        
        if (!projeRes.ok || !blokRes.ok || !daireRes.ok) {
          throw new Error("Konum bilgileri alınamadı")
        }

        const [projeData, blokData, daireData] = await Promise.all([
          projeRes.json(),
          blokRes.json(),
          daireRes.json()
        ])

        setLocationInfo({
          proje: { ad: projeData.ad, slug: projeData.slug },
          blok: { ad: blokData.ad, slug: blokData.slug },
          daire: { numara: daireData.numara, slug: daireData.slug },
          projeId: projeData.id,
          blokId: blokData.id,
          daireId: daireData.id
        })
      } catch (err) {
        console.error("Konum bilgileri yüklenirken hata:", err)
        setError(err instanceof Error ? err.message : "Bir hata oluştu")
      } finally {
        setLoading(false)
      }
    }

    if (resolvedParams) {
      fetchLocationInfo()
    }
  }, [resolvedParams])

  const handleCancel = () => {
    if (resolvedParams) {
      router.push(`/projeler/${resolvedParams.projeSlug}/bloklar/${resolvedParams.blokSlug}/${resolvedParams.daireSlug}`)
    } else {
      router.back()
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Breadcrumb Skeleton */}
        <div className="flex items-center space-x-2 text-sm">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-16" />
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-9 w-24" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
        </div>
        <div className="mx-auto max-w-5xl">
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    )
  }

  if (error || !locationInfo || !resolvedParams) {
    return (
      <div className="space-y-6">
        {/* Simple Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Link href="/dashboard" className="hover:text-foreground transition-colors">
            Ana Sayfa
          </Link>
          <ChevronRight className="h-4 w-4" />
          <span className="font-medium text-foreground">Yeni Arıza</span>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Geri Dön
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Hata</h2>
            <p className="text-muted-foreground">
              {error || "Konum bilgileri alınamadı"}
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Custom Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Ana Sayfa
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler`} className="hover:text-foreground transition-colors">
          Projeler
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler/${locationInfo.proje.slug}`} className="hover:text-foreground transition-colors">
          {locationInfo.proje.ad}
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler/${locationInfo.proje.slug}/bloklar/${locationInfo.blok.slug}`} className="hover:text-foreground transition-colors">
          {locationInfo.blok.ad}
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler/${locationInfo.proje.slug}/bloklar/${locationInfo.blok.slug}/${locationInfo.daire.slug}`} className="hover:text-foreground transition-colors">
          Daire {locationInfo.daire.numara}
        </Link>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground">Yeni Arıza</span>
      </div>
      
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/projeler/${locationInfo.proje.slug}/bloklar/${locationInfo.blok.slug}/${locationInfo.daire.slug}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Daireye Dön
          </Link>
        </Button>
        <div className="flex-1">
          <h2 className="text-3xl font-bold tracking-tight">Yeni Arıza Oluştur</h2>
          <p className="text-muted-foreground">
            Daire {locationInfo.daire.numara} için yeni arıza kaydı oluşturun
          </p>
          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
            <MapPin className="h-3 w-3" />
            {locationInfo.proje.ad} - {locationInfo.blok.ad} - Daire {locationInfo.daire.numara}
          </div>
        </div>
      </div>

      <div className="mx-auto max-w-5xl">
        <NewFaultForm 
          locationInfo={locationInfo}
        />
      </div>
    </div>
  )
} 