"use client"

import { useState } from "react"
import { Trash2 } from "lucide-react"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { useLoading } from "@/contexts/loading-context"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"

interface DeleteFaultDialogProps {
  faultId: string
  faultNumber: string
  faultTitle: string
  redirectTo?: string // Silme sonrası yönlendirilecek sayfa
}

export function DeleteFaultDialog({
  faultId,
  faultNumber,
  faultTitle,
          redirectTo = "/arizalar"
}: DeleteFaultDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [open, setOpen] = useState(false)
  const router = useRouter()
  const { startLoading } = useLoading()

  const handleDelete = async () => {
    try {
      setIsDeleting(true)

      const response = await fetch(`/api/faults/${faultId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Arıza silinirken hata oluştu")
      }      toast.success("Arıza başarıyla silindi")
      setOpen(false)
      
      // Yönlendirme
      startLoading()
      router.push(redirectTo)
      router.refresh()
    } catch (error) {
      console.error("Arıza silme hatası:", error)
      toast.error(error instanceof Error ? error.message : "Bir hata oluştu")
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" size="sm">
          <Trash2 className="mr-2 h-4 w-4" />
          Sil
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Arızayı Sil</AlertDialogTitle>
          <AlertDialogDescription>
            <span className="font-medium">#{faultNumber} - {faultTitle}</span> arızasını silmek istediğinizden emin misiniz?
            <br />
            <br />
            Bu işlem geri alınamaz ve arıza ile ilgili tüm veriler kalıcı olarak silinecektir.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            İptal
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Siliniyor..." : "Sil"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
