/**
 * Database Enum Güncellemeleri
 * Merkezi enum sistemine uygun olarak veritabanındaki enum değerlerini günceller
 */

import { PrismaClient } from '@prisma/client'
import { 
  FaultStatus, 
  FAULT_STATUS_LABELS,
  Priority,
  PRIORITY_LABELS
} from '../src/lib/enums'

const prisma = new PrismaClient()

async function updateEnums() {
  console.log('🔄 Enum değerleri güncelleniyor...')

  try {
    // 1. Arıza Durumları Güncelleme
    console.log('📝 Arıza durumları güncelleniyor...')
    
    // Mevcut durumları yeni enum değerlerine göre güncelle
    const statusUpdates = [
      { old: 'Açık', new: FAULT_STATUS_LABELS[FaultStatus.ACIK] },
      { old: 'Beklemede', new: FAULT_STATUS_LABELS[FaultStatus.BEKLEMEDE] },
      { old: 'Devam Ediyor', new: FAULT_STATUS_LABELS[FaultStatus.DEVAM_EDIYOR] },
      { old: 'Çözüldü', new: FAULT_STATUS_LABELS[FaultStatus.COZULDU] },
      // Tamamlandı durumunu kaldırıyoruz, çünkü Çözüldü ile aynı anlama geliyor
    ]

    for (const update of statusUpdates) {
      if (update.old !== update.new) {
        console.log(`  ➜ "${update.old}" → "${update.new}"`)
        await prisma.arizaDurum.updateMany({
          where: { ad: update.old },
          data: { ad: update.new }
        })
      }
    }

    // 2. Aciliyet Seviyeleri Güncelleme
    console.log('📝 Aciliyet seviyeleri güncelleniyor...')
    
    const priorityUpdates = [
      { old: 'Düşük', new: PRIORITY_LABELS[Priority.DUSUK] },
      { old: 'Orta', new: PRIORITY_LABELS[Priority.ORTA] },
      { old: 'Yüksek', new: PRIORITY_LABELS[Priority.YUKSEK] },
      { old: 'Kritik', new: PRIORITY_LABELS[Priority.KRITIK] },
    ]

    for (const update of priorityUpdates) {
      if (update.old !== update.new) {
        console.log(`  ➜ "${update.old}" → "${update.new}"`)
        await prisma.aciliyetSeviye.updateMany({
          where: { ad: update.old },
          data: { ad: update.new }
        })
      }
    }

    // 3. Eksik enum değerlerini ekle
    console.log('📝 Eksik enum değerleri ekleniyor...')    // İptal durumu yoksa ekle
    const iptalDurum = await prisma.arizaDurum.findFirst({
      where: { ad: FAULT_STATUS_LABELS[FaultStatus.IPTAL] }
    })

    if (!iptalDurum) {
      console.log(`  ➕ "${FAULT_STATUS_LABELS[FaultStatus.IPTAL]}" durumu ekleniyor`)
      
      // En yüksek sıra numarasını bul
      const maxSira = await prisma.arizaDurum.findFirst({
        orderBy: { sira: 'desc' },
        select: { sira: true }
      })
      
      await prisma.arizaDurum.create({
        data: {
          ad: FAULT_STATUS_LABELS[FaultStatus.IPTAL],
          renk: '#6B7280', // Gray color
          aciklama: 'İptal edilmiş veya geçersiz arızalar',
          sira: (maxSira?.sira || 0) + 1
        }
      })
    }

    // 4. Güncellenmiş durumları göster
    console.log('\n✅ Güncellenmiş Enum Değerleri:')
    
    const durumlar = await prisma.arizaDurum.findMany({
      orderBy: { ad: 'asc' }
    })
    
    console.log('\n📋 Arıza Durumları:')
    durumlar.forEach((durum: { ad: string; renk: string }) => {
      console.log(`  • ${durum.ad} (${durum.renk})`)
    })

    const aciliyetler = await prisma.aciliyetSeviye.findMany({
      orderBy: { seviye: 'asc' }
    })
    
    console.log('\n📋 Aciliyet Seviyeleri:')
    aciliyetler.forEach((aciliyet: { ad: string; seviye: number; renk: string }) => {
      console.log(`  • ${aciliyet.ad} - Seviye: ${aciliyet.seviye} (${aciliyet.renk})`)
    })

    console.log('\n🎉 Enum güncelleme tamamlandı!')

  } catch (error) {
    console.error('❌ Enum güncelleme hatası:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Script çalıştırılıyorsa güncelleme yap
if (require.main === module) {
  updateEnums()
    .then(() => {
      console.log('✅ Script başarıyla tamamlandı')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Script hatası:', error)
      process.exit(1)
    })
}

export { updateEnums }
