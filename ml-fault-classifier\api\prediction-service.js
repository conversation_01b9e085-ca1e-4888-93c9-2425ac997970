const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const KeywordClassifier = require('../models/keyword-classifier');

const app = express();
const PORT = process.env.PORT || 3050;
const MAIN_APP_URL = process.env.MAIN_APP_URL || 'http://localhost:3001';
const CONFIDENCE_THRESHOLD = parseFloat(process.env.CONFIDENCE_THRESHOLD) || 0.1;

// Middleware
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
  credentials: process.env.CORS_CREDENTIALS === 'true'
}));
app.use(express.json());

// Global classifier instance
let classifier = null;
let isModelLoaded = false;
let availableCategories = new Map(); // Mevcut kategoriler
let categoryMapping = new Map(); // ML kategori -> DB kategori mapping

// Model yükleme fonksiyonu
async function loadModel() {
  try {
    console.log('🤖 ML Model yükleniyor...');
    
    // Training data'yı yükle
    const trainingDataPath = path.join(__dirname, '../data/training-data.json');
    const trainingData = JSON.parse(fs.readFileSync(trainingDataPath, 'utf8'));
    
    // Classifier oluştur ve eğit
    classifier = new KeywordClassifier();
    classifier.setConfidenceThreshold(CONFIDENCE_THRESHOLD);
    classifier.train(trainingData);
    
    isModelLoaded = true;
    console.log('✅ ML Model başarıyla yüklendi!');
    
    return true;
  } catch (error) {
    console.error('❌ Model yükleme hatası:', error.message);
    return false;
  }
}

// Mevcut kategorileri güncelle
async function updateAvailableCategories() {
  try {
    // Ana projeden kategorileri al
    const response = await fetch(`${MAIN_APP_URL}/api/categories`);
    if (response.ok) {
      const data = await response.json();
      availableCategories.clear();
      categoryMapping.clear();
      
      data.categories.forEach(cat => {
        availableCategories.set(cat.id, {
          id: cat.id,
          ad: cat.ad,
          aciklama: cat.aciklama,
          renk: cat.renk,
          ikon: cat.ikon
        });
        
        // ML kategorilerini DB kategorileriyle eşleştir
        const mlCategory = findBestMatchingCategory(cat.ad);
        if (mlCategory) {
          categoryMapping.set(mlCategory, cat.id);
        }
      });
      
      console.log(`📊 ${availableCategories.size} kategori güncellendi`);
      return true;
    }
  } catch (error) {
    console.error('❌ Kategori güncelleme hatası:', error.message);
  }
  return false;
}

// ML kategorisini DB kategorisiyle eşleştir
function findBestMatchingCategory(dbCategoryName) {
  const mlCategories = [
    'Su Tesisatı', 'Elektrik', 'Boyama', 'Yapı', 'Güvenlik', 
    'Isıtma/Soğutma', 'Asansör', 'Çevre Düzenleme'
  ];
  
  const normalizedDbName = dbCategoryName.toLowerCase();
  
  for (const mlCat of mlCategories) {
    const normalizedMlName = mlCat.toLowerCase();
    
    // Tam eşleşme
    if (normalizedDbName === normalizedMlName) {
      return mlCat;
    }
    
    // Kısmi eşleşme
    if (normalizedDbName.includes(normalizedMlName) || normalizedMlName.includes(normalizedDbName)) {
      return mlCat;
    }
  }
  
  return null;
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    modelLoaded: isModelLoaded,
    availableCategories: availableCategories.size,
    service: 'ML Fault Classifier API',
    version: '1.0.0'
  });
});

// Model durumu endpoint
app.get('/status', (req, res) => {
  if (!isModelLoaded || !classifier) {
    return res.status(503).json({
      error: 'Model henüz yüklenmedi',
      status: 'loading'
    });
  }
  
  const status = classifier.getStatus();
  res.json({
    status: 'ready',
    model: status,
    availableCategories: availableCategories.size,
    categoryMapping: Object.fromEntries(categoryMapping),
    timestamp: new Date().toISOString()
  });
});

// Kategori tahmini endpoint
app.post('/predict', async (req, res) => {
  try {
    if (!isModelLoaded || !classifier) {
      return res.status(503).json({
        error: 'Model henüz yüklenmedi',
        status: 'loading'
      });
    }
    
    const { title, description } = req.body;
    
    if (!title || !description) {
      return res.status(400).json({
        error: 'Başlık ve açıklama gereklidir',
        required: ['title', 'description']
      });
    }
    
    // Tahmin yap
    const prediction = classifier.predict(title, description);
    
    // Mevcut kategorilerle filtrele
    if (prediction.success) {
      const dbCategoryId = categoryMapping.get(prediction.category);
      if (dbCategoryId) {
        const dbCategory = availableCategories.get(dbCategoryId);
        prediction.db_category = {
          id: dbCategoryId,
          ad: dbCategory.ad,
          aciklama: dbCategory.aciklama,
          renk: dbCategory.renk,
          ikon: dbCategory.ikon
        };
      } else {
        // Eşleşen kategori yoksa alternatifleri kontrol et
        prediction.success = false;
        prediction.message = 'Tahmin edilen kategori mevcut kategoriler arasında bulunamadı';
      }
    }
    
    // Alternatifleri de filtrele
    if (prediction.alternatives) {
      prediction.alternatives = prediction.alternatives
        .map(alt => {
          const dbCategoryId = categoryMapping.get(alt.category);
          if (dbCategoryId) {
            const dbCategory = availableCategories.get(dbCategoryId);
            return {
              ...alt,
              db_category: {
                id: dbCategoryId,
                ad: dbCategory.ad,
                aciklama: dbCategory.aciklama,
                renk: dbCategory.renk,
                ikon: dbCategory.ikon
              }
            };
          }
          return null;
        })
        .filter(alt => alt !== null);
    }
    
    res.json({
      success: true,
      prediction: prediction,
      availableCategories: availableCategories.size,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Tahmin hatası:', error.message);
    res.status(500).json({
      error: 'Tahmin sırasında hata oluştu',
      message: error.message
    });
  }
});

// Batch tahmin endpoint
app.post('/predict/batch', async (req, res) => {
  try {
    if (!isModelLoaded || !classifier) {
      return res.status(503).json({
        error: 'Model henüz yüklenmedi',
        status: 'loading'
      });
    }
    
    const { items } = req.body;
    
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        error: 'Items array gereklidir',
        required: ['items']
      });
    }
    
    if (items.length > 100) {
      return res.status(400).json({
        error: 'Maksimum 100 öğe tahmin edilebilir',
        maxItems: 100
      });
    }
    
    const results = [];
    
    items.forEach((item, index) => {
      try {
        const { title, description } = item;
        
        if (!title || !description) {
          results.push({
            index: index,
            success: false,
            error: 'Başlık ve açıklama gereklidir'
          });
          return;
        }
        
        const prediction = classifier.predict(title, description);
        results.push({
          index: index,
          success: true,
          prediction: prediction
        });
        
      } catch (error) {
        results.push({
          index: index,
          success: false,
          error: error.message
        });
      }
    });
    
    res.json({
      success: true,
      results: results,
      total: results.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Batch tahmin hatası:', error.message);
    res.status(500).json({
      error: 'Batch tahmin sırasında hata oluştu',
      message: error.message
    });
  }
});

// Kategori keyword'leri endpoint
app.get('/categories/:categoryName/keywords', (req, res) => {
  try {
    if (!isModelLoaded || !classifier) {
      return res.status(503).json({
        error: 'Model henüz yüklenmedi',
        status: 'loading'
      });
    }
    
    const { categoryName } = req.params;
    const { limit = 10 } = req.query;
    
    const keywords = classifier.getTopKeywords(categoryName, parseInt(limit));
    
    res.json({
      success: true,
      category: categoryName,
      keywords: keywords,
      count: keywords.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Keyword listesi hatası:', error.message);
    res.status(500).json({
      error: 'Keyword listesi alınırken hata oluştu',
      message: error.message
    });
  }
});

// Mevcut kategoriler endpoint
app.get('/categories', (req, res) => {
  try {
    if (!isModelLoaded || !classifier) {
      return res.status(503).json({
        error: 'Model henüz yüklenmedi',
        status: 'loading'
      });
    }
    
    // Mevcut DB kategorilerini döndür
    const categories = Array.from(availableCategories.values());
    
    res.json({
      success: true,
      categories: categories,
      total: categories.length,
      ml_categories: Object.fromEntries(categoryMapping),
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Kategori listesi hatası:', error.message);
    res.status(500).json({
      error: 'Kategori listesi alınırken hata oluştu',
      message: error.message
    });
  }
});

// Kategorileri güncelle endpoint
app.post('/categories/refresh', async (req, res) => {
  try {
    console.log('🔄 Kategoriler güncelleniyor...');
    
    const success = await updateAvailableCategories();
    
    if (success) {
      res.json({
        success: true,
        message: 'Kategoriler başarıyla güncellendi',
        availableCategories: availableCategories.size,
        categoryMapping: Object.fromEntries(categoryMapping),
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        error: 'Kategoriler güncellenemedi',
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error('Kategori güncelleme hatası:', error.message);
    res.status(500).json({
      error: 'Kategoriler güncellenirken hata oluştu',
      message: error.message
    });
  }
});

// Model yeniden yükleme endpoint
app.post('/reload', async (req, res) => {
  try {
    console.log('🔄 Model yeniden yükleniyor...');
    
    const success = await loadModel();
    
    if (success) {
      res.json({
        success: true,
        message: 'Model başarıyla yeniden yüklendi',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        error: 'Model yeniden yüklenemedi',
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error('Model yeniden yükleme hatası:', error.message);
    res.status(500).json({
      error: 'Model yeniden yüklenirken hata oluştu',
      message: error.message
    });
  }
});

// Güven eşiği ayarlama endpoint
app.post('/config/confidence', (req, res) => {
  try {
    if (!isModelLoaded || !classifier) {
      return res.status(503).json({
        error: 'Model henüz yüklenmedi',
        status: 'loading'
      });
    }
    
    const { threshold } = req.body;
    
    if (typeof threshold !== 'number' || threshold < 0 || threshold > 1) {
      return res.status(400).json({
        error: 'Güven eşiği 0-1 arasında bir sayı olmalıdır',
        required: ['threshold']
      });
    }
    
    classifier.setConfidenceThreshold(threshold);
    
    res.json({
      success: true,
      message: `Güven eşiği ${threshold} olarak ayarlandı`,
      threshold: threshold,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Güven eşiği ayarlama hatası:', error.message);
    res.status(500).json({
      error: 'Güven eşiği ayarlanırken hata oluştu',
      message: error.message
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Endpoint bulunamadı',
    availableEndpoints: [
      'GET /health',
      'GET /status',
      'POST /predict',
      'POST /predict/batch',
      'GET /categories',
      'POST /categories/refresh',
      'GET /categories/:category/keywords',
      'POST /reload',
      'POST /config/confidence'
    ]
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('API Hatası:', error);
  res.status(500).json({
    error: 'Sunucu hatası',
    message: error.message
  });
});

// Server başlatma
async function startServer() {
  try {
    // Modeli yükle
    const modelLoaded = await loadModel();
    
    if (!modelLoaded) {
      console.error('❌ Model yüklenemedi, sunucu başlatılamıyor');
      process.exit(1);
    }
    
    // Kategorileri güncelle
    console.log('📊 Kategoriler güncelleniyor...');
    await updateAvailableCategories();
    
    // Server'ı başlat
    app.listen(PORT, () => {
      console.log(`🚀 ML Fault Classifier API başlatıldı!`);
      console.log(`   📍 Port: ${PORT}`);
      console.log(`   🔗 URL: http://localhost:${PORT}`);
      console.log(`   📊 Health Check: http://localhost:${PORT}/health`);
      console.log(`   🤖 Model Durumu: http://localhost:${PORT}/status`);
      console.log(`   🎯 Tahmin API: http://localhost:${PORT}/predict`);
      console.log(`   📋 Kategoriler: http://localhost:${PORT}/categories`);
      console.log(`   🔄 Kategori Güncelleme: http://localhost:${PORT}/categories/refresh`);
      console.log('');
      console.log(`✅ API hazır! ${availableCategories.size} kategori ile entegre edildi.`);
    });
    
  } catch (error) {
    console.error('❌ Server başlatma hatası:', error.message);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Server kapatılıyor...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Server kapatılıyor...');
  process.exit(0);
});

// Server'ı başlat
startServer(); 