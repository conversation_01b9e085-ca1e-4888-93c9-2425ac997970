"use client"

import React, { useState } from 'react';
import { AdvancedMediaSelector, MediaFile } from './AdvancedMediaSelector';

export default function TestAdvancedMediaSelector() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile | undefined>(undefined);

  const handleSelect = (media: MediaFile | undefined) => {
    setSelectedMedia(media);
    console.log('Selected media:', media);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Advanced Media Selector Test</h1>
      
      <div className="space-y-6">
        <div className="border rounded-lg p-6 bg-gray-50">
          <h2 className="text-xl font-semibold mb-4">Current Selected Media</h2>
          {selectedMedia ? (
            <div className="flex items-center gap-4">
              <img 
                src={selectedMedia.url} 
                alt={selectedMedia.originalName} 
                className="w-24 h-24 object-cover rounded border"
              />
              <div>
                <div className="font-medium">{selectedMedia.originalName}</div>
                <div className="text-sm text-gray-500">{(selectedMedia.size / 1024).toFixed(2)} KB</div>
                <div className="text-sm text-blue-600">{selectedMedia.url}</div>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">No media selected</div>
          )}
        </div>

        <div className="space-y-4">
          <button
            onClick={() => setIsOpen(true)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          >
            Open Media Selector
          </button>
          
          {selectedMedia && (
            <button
              onClick={() => setSelectedMedia(undefined)}
              className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition ml-4"
            >
              Clear Selection
            </button>
          )}
        </div>

        <div className="border rounded-lg p-6 bg-blue-50">
          <h3 className="text-lg font-semibold mb-3">Test Workflow:</h3>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Click "Open Media Selector" button</li>
            <li>Go to "Upload" tab</li>
            <li>Select an image file - cropping modal should open automatically</li>
            <li>Adjust crop area and click "Kırp ve Kaydet"</li>
            <li>Should automatically switch to Gallery tab</li>
            <li>Cropped image should appear with "Yeni" badge</li>
            <li>Click "Proje görselini ata" to assign to project</li>
            <li>Modal should close and image should appear above</li>
          </ol>
        </div>

        <div className="border rounded-lg p-6 bg-green-50">
          <h3 className="text-lg font-semibold mb-3">Expected Fixes:</h3>
          <ul className="list-disc list-inside space-y-2 text-sm">
            <li>✅ Auto-crop: Upload tab automatically opens cropping when file selected</li>
            <li>🔧 No duplication: Cropped images don't appear twice in gallery (FIXED)</li>
            <li>✅ Gallery refresh: Cropped images properly appear in gallery</li>
            <li>✅ Workflow integration: Smooth Upload → Auto-crop → Gallery → Assignment</li>
            <li>✅ Visual feedback: Success messages and "Yeni" badges</li>
          </ul>
        </div>

        <div className="border rounded-lg p-6 bg-yellow-50">
          <h3 className="text-lg font-semibold mb-3">Debug Information:</h3>
          <p className="text-sm mb-2">
            Open browser console (F12) to see detailed logging during the upload process:
          </p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>🎯 Upload source tracking (AUTO-CROP, DRAG & DROP, MANUAL)</li>
            <li>📋 Media list updates with validation</li>
            <li>🚨 Duplicate detection alerts</li>
            <li>📥 Gallery refresh events</li>
            <li>📊 Enhanced deduplication statistics</li>
            <li>✅ Centralized upload handler usage</li>
          </ul>
        </div>

        <div className="border rounded-lg p-6 bg-red-50">
          <h3 className="text-lg font-semibold mb-3">🔧 COMPREHENSIVE FIX APPLIED:</h3>
          <ul className="list-disc list-inside space-y-2 text-sm">
            <li><strong>Root Cause Fixed:</strong> All 3 upload paths now use consistent state management</li>
            <li><strong>Centralized Handler:</strong> Single upload success handler prevents inconsistencies</li>
            <li><strong>Enhanced Deduplication:</strong> Checks both URL and filename for duplicates</li>
            <li><strong>State Validation:</strong> Automatic duplicate detection in all state updates</li>
            <li><strong>Comprehensive Logging:</strong> Track exact source of any remaining issues</li>
          </ul>
          <div className="mt-3 p-2 bg-green-100 rounded text-green-800 text-sm">
            <strong>Expected Result:</strong> Zero duplicate images in gallery, regardless of upload method or modal reopen.
          </div>
        </div>
      </div>

      <AdvancedMediaSelector
        open={isOpen}
        onClose={handleClose}
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        title="Test Project Image Selector"
        description="Test the improved workflow for project image selection"
        folder="projeler"
        acceptedTypes={["image/jpeg", "image/png", "image/webp"]}
        maxSizeMB={5}
      />
    </div>
  );
}
