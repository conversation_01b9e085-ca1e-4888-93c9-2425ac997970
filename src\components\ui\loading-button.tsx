"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"
import { But<PERSON> } from "./button"
import { useLoading } from "@/contexts/loading-context"

export interface LoadingButtonProps extends React.ComponentProps<typeof Button> {
  href?: string
  loading?: boolean
  loadingText?: string
  replace?: boolean
}

export function LoadingButton({ 
  href, 
  loading = false, 
  loadingText = "Yükleniyor...", 
  children, 
  onClick,
  disabled,
  replace = false,
  ...props 
}: LoadingButtonProps) {
  const router = useRouter()
  const { startLoading } = useLoading()
  const [isInternalLoading, setIsInternalLoading] = React.useState(false)

  const isLoading = loading || isInternalLoading

  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
    if (href) {
      e.preventDefault()
      setIsInternalLoading(true)
      startLoading()
      
      if (replace) {
        router.replace(href)
      } else {
        router.push(href)
      }
      
      // Loading overlay context'i otomatik olarak kapatacak
      // Ama buradaki internal loading'i biraz gecikmeyle kapatalım
      setTimeout(() => {
        setIsInternalLoading(false)
      }, 100)
    } else if (onClick) {
      // Eğer onClick async ise loading göster
      if (onClick.constructor.name === 'AsyncFunction') {
        setIsInternalLoading(true)
        try {
          await onClick(e)
        } finally {
          setIsInternalLoading(false)
        }
      } else {
        onClick(e)
      }
    }
  }

  return (
    <Button
      {...props}
      disabled={disabled || isLoading}
      onClick={handleClick}
    >
      {isLoading ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </Button>
  )
}
