# 🎯 Generic Media Selector Kullanım Kılavuzu

Bu kılavuz, projenizdeki tüm medya seçimi ihtiyaçları için tutarlı ve esnek bir çözüm sunan **Generic Media Selector** sisteminin nasıl kullanılacağını açıklar.

## 📋 İçerik

1. [Hızlı Başlangıç](#hızlı-başlangıç)
2. [Temel Konseptler](#temel-konseptler)
3. [Konfigürasyon Örnekleri](#konfigürasyon-örnekleri)
4. [Kullanım Senaryoları](#kullanım-senaryoları)
5. [Advanced Kullanım](#advanced-kullanım)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

---

## 🚀 Hızlı Başlangıç

### 1. Temel Kullanım

```tsx
import { GenericMediaSelector, useGenericMediaSelector } from '@/components/media/GenericMediaSelector';

function MyComponent() {
  const { selectedMedia, handleSelect } = useGenericMediaSelector();

  return (
    <GenericMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      buttonText="Select Image"
    />
  );
}
```

### 2. Kategori ile Kullanım

```tsx
import { corporateMediaConfig } from '@/config/media-configs';

function ProjectPage() {
  const { selectedMedia, handleSelect } = useGenericMediaSelector();

  return (
    <GenericMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      categoryKey="project-images"
      config={corporateMediaConfig}
      buttonText="Proje Görseli Seç"
    />
  );
}
```

---

## 🧠 Temel Konseptler

### 1. **Generic Types**
Sistem tamamen type-safe ve genişletilebilir:

```tsx
interface MyCustomMetadata {
  tags: string[];
  author: string;
  copyright?: string;
}

const { selectedMedia } = useGenericMediaSelector<MyCustomMetadata>();
```

### 2. **Configuration-Driven**
Her kullanım durumu için özel konfigürasyonlar:

```tsx
const myConfig = createMediaSelectorConfig([
  {
    id: 'my-category',
    key: 'my-category',
    name: 'My Category',
    acceptedTypes: ['image/*'],
    folder: 'my-folder',
    targetDimensions: { width: 800, height: 600 }
  }
]);
```

### 3. **Provider Pattern**
Uygulama genelinde paylaşılan konfigürasyon:

```tsx
function App() {
  return (
    <MediaSelectorProvider config={corporateMediaConfig}>
      <MyPages />
    </MediaSelectorProvider>
  );
}
```

---

## ⚙️ Konfigürasyon Örnekleri

### Blog/CMS Sistemi

```tsx
const blogConfig = createMediaSelectorConfig([
  {
    id: 'featured',
    key: 'featured',
    name: 'Featured Images',
    title: 'Blog Featured Image',
    description: 'Select main image for your post',
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    folder: 'blog/featured',
    maxSize: 2 * 1024 * 1024, // 2MB
    targetDimensions: { width: 1200, height: 630 }
  },
  {
    id: 'inline',
    key: 'inline', 
    name: 'Inline Images',
    title: 'Content Images',
    description: 'Add images to your content',
    acceptedTypes: ['image/*'],
    folder: 'blog/content',
    targetDimensions: { width: 800, height: 450 }
  }
], {
  defaultCategory: 'featured',
  allowMultiSelect: true,
  translations: {
    selectButton: 'Choose Image',
    selectTitle: 'Blog Media Library'
  }
});
```

### E-Commerce Sistemi

```tsx
interface ProductMetadata {
  productId: string;
  variant?: string;
  angle: 'front' | 'back' | 'side' | 'detail';
  isMain: boolean;
}

const ecommerceConfig = createMediaSelectorConfig<ProductMetadata>([
  {
    id: 'product-main',
    key: 'product-main',
    name: 'Main Product Images',
    acceptedTypes: ['image/jpeg', 'image/png'],
    folder: 'products/main',
    maxSize: 5 * 1024 * 1024,
    targetDimensions: { width: 1200, height: 1200 }
  }
]);
```

---

## 🎯 Kullanım Senaryoları

### 1. **Tek Dosya Seçimi**

```tsx
function AvatarUpload() {
  const { selectedMedia, handleSelect } = useGenericMediaSelector();

  return (
    <div className="flex flex-col items-center space-y-4">
      <GenericMediaSelector
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        categoryKey="user-avatars"
        targetWidth={200}
        targetHeight={200}
        buttonText="Upload Avatar"
      />
      
      {selectedMedia && (
        <img 
          src={selectedMedia.url} 
          alt="Avatar"
          className="w-20 h-20 rounded-full object-cover"
        />
      )}
    </div>
  );
}
```

### 2. **Çoklu Dosya Seçimi**

```tsx
function GalleryManager() {
  const { selectedItems, handleMultiSelect } = useGenericMediaSelector();

  return (
    <div>
      <GenericMediaSelector
        onMultiSelect={handleMultiSelect}
        selectedItems={selectedItems}
        categoryKey="gallery-images"
        multiSelect={true}
        buttonText={`Add Images (${selectedItems.length} selected)`}
      />
      
      <div className="grid grid-cols-4 gap-2 mt-4">
        {selectedItems.map(item => (
          <img 
            key={item.id}
            src={item.thumbnailSmall || item.url}
            className="aspect-square object-cover rounded"
          />
        ))}
      </div>
    </div>
  );
}
```

### 3. **Kategori Kısıtlamalı Seçim**

```tsx
function ProjectImageSelector() {
  const [projectImage, setProjectImage] = useState(null);

  return (
    <GenericMediaSelector
      onSelect={setProjectImage}
      selectedMedia={projectImage}
      categoryKey="project-images"
      restrictToCategory={true} // Sadece proje görselleri göster
      config={corporateMediaConfig}
    />
  );
}
```

### 4. **Dinamik Kategori Değiştirme**

```tsx
function DynamicMediaSelector() {
  const [category, setCategory] = useState('blog-featured');
  const { selectedMedia, handleSelect, clearSelection } = useGenericMediaSelector();

  const handleCategoryChange = (newCategory: string) => {
    setCategory(newCategory);
    clearSelection(); // Kategori değiştiğinde seçimi temizle
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        {['blog-featured', 'blog-inline', 'blog-gallery'].map(cat => (
          <Button
            key={cat}
            variant={category === cat ? "default" : "outline"}
            onClick={() => handleCategoryChange(cat)}
          >
            {cat.replace('-', ' ').replace('blog ', '')}
          </Button>
        ))}
      </div>

      <GenericMediaSelector
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        categoryKey={category}
        key={category} // Kategori değiştiğinde component'i yeniden render et
      />
    </div>
  );
}
```

---

## 🚀 Advanced Kullanım

### 1. **Custom Hook ile State Yönetimi**

```tsx
function useProjectMedia() {
  const { 
    selectedMedia, 
    selectedItems,
    loading,
    error,
    handleSelect,
    handleMultiSelect,
    setLoading,
    setError
  } = useGenericMediaSelector();

  const uploadAndSelect = async (file: File) => {
    setLoading(true);
    try {
      const uploadedMedia = await uploadToServer(file);
      handleSelect(uploadedMedia);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  return {
    selectedMedia,
    selectedItems,
    loading,
    error,
    handleSelect,
    handleMultiSelect,
    uploadAndSelect
  };
}
```

### 2. **Error Handling ve Progress Tracking**

```tsx
function AdvancedMediaSelector() {
  const { selectedMedia, handleSelect, setLoading, setError } = useGenericMediaSelector();

  const handleUploadProgress = (progress: number) => {
    console.log(`Upload: ${progress}%`);
  };

  const handleUploadComplete = (media: MediaFile) => {
    handleSelect(media);
    toast.success('Upload completed!');
  };

  const handleError = (error: Error) => {
    setError(error);
    toast.error(`Upload failed: ${error.message}`);
  };

  return (
    <GenericMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      onUploadProgress={handleUploadProgress}
      onUploadComplete={handleUploadComplete}
      onError={handleError}
      maxFileSize={5 * 1024 * 1024} // 5MB limit
    />
  );
}
```

### 3. **Custom Trigger Component**

```tsx
function CustomTriggerExample() {
  const { selectedMedia, handleSelect } = useGenericMediaSelector();

  const customTrigger = (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 cursor-pointer">
      {selectedMedia ? (
        <div>
          <img src={selectedMedia.thumbnailMedium} className="mx-auto mb-2" />
          <p className="text-sm text-gray-600">{selectedMedia.originalName}</p>
        </div>
      ) : (
        <div>
          <Upload className="mx-auto mb-2 text-gray-400" size={48} />
          <p className="text-gray-600">Click to upload or select media</p>
        </div>
      )}
    </div>
  );

  return (
    <GenericMediaSelector
      onSelect={handleSelect}
      selectedMedia={selectedMedia}
      trigger={customTrigger}
    />
  );
}
```

---

## 📚 Best Practices

### 1. **Konfigürasyon Yönetimi**

```tsx
// ✅ İyi: Merkezi konfigürasyon dosyaları
// src/config/media-configs.ts
export const blogMediaConfig = createMediaSelectorConfig(/* ... */);
export const ecommerceMediaConfig = createMediaSelectorConfig(/* ... */);

// ✅ İyi: Tip güvenliği
interface BlogMetadata {
  tags: string[];
  seoAlt: string;
}

const { selectedMedia } = useGenericMediaSelector<BlogMetadata>();
```

### 2. **Performance Optimizasyonu**

```tsx
// ✅ İyi: Memoized callbacks
const handleSelect = useCallback((media: MediaFile) => {
  setSelectedMedia(media);
  onMediaChange?.(media);
}, [onMediaChange]);

// ✅ İyi: Key prop ile re-render kontrolü
<GenericMediaSelector
  key={categoryId} // Kategori değiştiğinde yeniden render
  onSelect={handleSelect}
  categoryKey={categoryKey}
/>
```

### 3. **Error Handling**

```tsx
// ✅ İyi: Comprehensive error handling
function SafeMediaSelector() {
  const { selectedMedia, handleSelect, error } = useGenericMediaSelector();

  return (
    <div>
      <GenericMediaSelector
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        onError={(err) => {
          console.error('Media selector error:', err);
          toast.error('Something went wrong. Please try again.');
        }}
      />
      
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
```

### 4. **Accessibility**

```tsx
// ✅ İyi: Accessible implementation
<GenericMediaSelector
  onSelect={handleSelect}
  selectedMedia={selectedMedia}
  trigger={
    <Button
      aria-label="Select media file"
      aria-describedby="media-description"
    >
      Choose File
    </Button>
  }
/>
<p id="media-description" className="sr-only">
  Select image files up to 5MB. Supported formats: JPEG, PNG, WebP
</p>
```

---

## 🔧 Troubleshooting

### Yaygın Sorunlar ve Çözümleri

#### 1. **"useMediaSelectorConfig must be used within MediaSelectorProvider" Hatası**

```tsx
// ❌ Hatalı: Provider dışında kullanım
function MyComponent() {
  const { config } = useMediaSelectorConfig(); // Hata!
}

// ✅ Doğru: Provider ile sarma veya config prop'u kullanma
function App() {
  return (
    <MediaSelectorProvider config={myConfig}>
      <MyComponent />
    </MediaSelectorProvider>
  );
}

// VEYA
function MyComponent() {
  return (
    <GenericMediaSelector
      config={myConfig} // Direkt config geçme
      onSelect={handleSelect}
    />
  );
}
```

#### 2. **Kategori Bulunamadı Hatası**

```tsx
// ❌ Hatalı: Olmayan kategori
<GenericMediaSelector categoryKey="non-existent-category" />

// ✅ Doğru: Mevcut kategori kullanma
const availableCategories = myConfig.categories.map(cat => cat.key);
console.log('Available categories:', availableCategories);

<GenericMediaSelector categoryKey="existing-category" />
```

#### 3. **TypeScript Type Hataları**

```tsx
// ❌ Hatalı: Generic type belirtmeme
const { selectedMedia } = useGenericMediaSelector();
// selectedMedia.customField; // Type error!

// ✅ Doğru: Generic type belirtme
interface MyMetadata {
  customField: string;
}

const { selectedMedia } = useGenericMediaSelector<MyMetadata>();
// selectedMedia.metadata?.customField; // Type safe!
```

---

## 📈 Gelecek Geliştirmeler

### Planlanan Özellikler

1. **Drag & Drop Support**
2. **Bulk Upload**
3. **Image Editing Integration**
4. **Cloud Storage Integration**
5. **AI-powered Image Tagging**
6. **Performance Analytics**

### Katkıda Bulunma

Bu sistem sürekli geliştirilmektedir. Yeni özellik önerileri ve bug raporları için lütfen geliştirme ekibiyle iletişime geçin.

---

**Bu kılavuz ile Generic Media Selector sistemini etkili bir şekilde kullanabilir ve projenizin ihtiyaçlarına göre özelleştirebilirsiniz! 🚀** 