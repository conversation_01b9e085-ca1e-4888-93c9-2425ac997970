"use client"

import { useEffect, useState } from "react"
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Edit, 
  Trash2, 
  MoreHorizontal,
  CheckSquare,
  Square,
  AlertTriangle,
  BarChart3
} from "lucide-react"
import { toast } from "sonner"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Table, 
  TableHeader, 
  TableRow, 
  TableHead, 
  TableBody, 
  TableCell 
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Skeleton } from "@/components/ui/skeleton"
import { Breadcrumb } from "@/components/layout/breadcrumb"

interface Material {
  id: string
  ad: string
  aciklama?: string
  birim?: string
  olusturulma_tarihi?: string
  guncelleme_tarihi?: string
}

interface MaterialFormData {
  ad: string
  aciklama: string
  birim: string
}

const COMMON_UNITS = [
  "Adet", "Kg", "Metre", "Litre", "M²", "M³", 
  "Paket", "Kutu", "Torba", "Rulo", "Takım"
]

export default function MaterialsPage() {
  const [materials, setMaterials] = useState<Material[]>([])
  const [filteredMaterials, setFilteredMaterials] = useState<Material[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([])
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false)
  
  // Form states
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null)
  const [deletingMaterial, setDeletingMaterial] = useState<Material | null>(null)
  const [formData, setFormData] = useState<MaterialFormData>({
    ad: "",
    aciklama: "",
    birim: ""
  })
  const [formErrors, setFormErrors] = useState<Partial<MaterialFormData>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    fetchMaterials()
  }, [])

  useEffect(() => {
    filterMaterials()
  }, [materials, searchTerm])

  const fetchMaterials = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/materials")
      if (response.ok) {
        const data = await response.json()
        setMaterials(data)
      } else {
        toast.error("Malzemeler yüklenirken hata oluştu")
      }
    } catch (error) {
      console.error("Error fetching materials:", error)
      toast.error("Malzemeler yüklenirken hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  const filterMaterials = () => {
    if (!searchTerm.trim()) {
      setFilteredMaterials(materials)
      return
    }

    const filtered = materials.filter(material =>
      material.ad.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.aciklama?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.birim?.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredMaterials(filtered)
  }

  const validateForm = (): boolean => {
    const errors: Partial<MaterialFormData> = {}
    
    if (!formData.ad.trim()) {
      errors.ad = "Malzeme adı gereklidir"
    } else if (formData.ad.length < 2) {
      errors.ad = "Malzeme adı en az 2 karakter olmalıdır"
    }
    
    if (!formData.birim.trim()) {
      errors.birim = "Birim gereklidir"
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const resetForm = () => {
    setFormData({ ad: "", aciklama: "", birim: "" })
    setFormErrors({})
    setEditingMaterial(null)
  }

  const handleCreate = () => {
    resetForm()
    setIsCreateDialogOpen(true)
  }

  const handleEdit = (material: Material) => {
    setEditingMaterial(material)
    setFormData({
      ad: material.ad,
      aciklama: material.aciklama || "",
      birim: material.birim || ""
    })
    setFormErrors({})
    setIsEditDialogOpen(true)
  }

  const handleDelete = (material: Material) => {
    setDeletingMaterial(material)
    setIsDeleteDialogOpen(true)
  }

  const handleBulkDelete = () => {
    if (selectedMaterials.length === 0) {
      toast.error("Silinecek malzeme seçin")
      return
    }
    setIsBulkDeleteDialogOpen(true)
  }

  const submitForm = async () => {
    if (!validateForm()) return

    setIsSubmitting(true)
    try {
      const url = editingMaterial ? `/api/materials/${editingMaterial.id}` : "/api/materials"
      const method = editingMaterial ? "PUT" : "POST"
      const body = editingMaterial 
        ? { id: editingMaterial.id, ...formData }
        : formData

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body)
      })

      if (response.ok) {
        toast.success(editingMaterial ? "Malzeme güncellendi" : "Malzeme eklendi")
        setIsCreateDialogOpen(false)
        setIsEditDialogOpen(false)
        resetForm()
        fetchMaterials()
      } else {
        toast.error("İşlem sırasında hata oluştu")
      }
    } catch (error) {
      console.error("Error submitting form:", error)
      toast.error("İşlem sırasında hata oluştu")
    } finally {
      setIsSubmitting(false)
    }
  }

  const confirmDelete = async () => {
    if (!deletingMaterial) return

    try {
      const response = await fetch(`/api/materials/${deletingMaterial.id}`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: deletingMaterial.id })
      })

      if (response.ok) {
        toast.success("Malzeme silindi")
        setIsDeleteDialogOpen(false)
        setDeletingMaterial(null)
        fetchMaterials()
      } else {
        toast.error("Silme işlemi sırasında hata oluştu")
      }
    } catch (error) {
      console.error("Error deleting material:", error)
      toast.error("Silme işlemi sırasında hata oluştu")
    }
  }

  const confirmBulkDelete = async () => {
    try {
      const deletePromises = selectedMaterials.map(id =>
        fetch(`/api/materials/${id}`, {
          method: "DELETE",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ id })
        })
      )

      await Promise.all(deletePromises)
      toast.success(`${selectedMaterials.length} malzeme silindi`)
      setSelectedMaterials([])
      setIsBulkDeleteDialogOpen(false)
      fetchMaterials()
    } catch (error) {
      console.error("Error bulk deleting materials:", error)
      toast.error("Toplu silme işlemi sırasında hata oluştu")
    }
  }

  const toggleSelectMaterial = (materialId: string) => {
    setSelectedMaterials(prev =>
      prev.includes(materialId)
        ? prev.filter(id => id !== materialId)
        : [...prev, materialId]
    )
  }

  const toggleSelectAll = () => {
    if (selectedMaterials.length === filteredMaterials.length) {
      setSelectedMaterials([])
    } else {
      setSelectedMaterials(filteredMaterials.map(m => m.id))
    }
  }

  const exportToCSV = () => {
    const csvContent = [
      ["Ad", "Açıklama", "Birim", "Oluşturma Tarihi"].join(","),
      ...filteredMaterials.map(m => [
        m.ad,
        m.aciklama || "",
        m.birim || "",
        m.olusturulma_tarihi ? new Date(m.olusturulma_tarihi).toLocaleDateString("tr-TR") : ""
      ].join(","))
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    link.href = URL.createObjectURL(blob)
    link.download = `malzemeler_${new Date().toISOString().split("T")[0]}.csv`
    link.click()
    toast.success("Malzemeler CSV olarak dışa aktarıldı")
  }

  const statistics = {
    total: materials.length,
    withDescription: materials.filter(m => m.aciklama?.trim()).length,
    units: [...new Set(materials.map(m => m.birim).filter(Boolean))].length
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Malzeme Yönetimi</h1>
          <p className="text-muted-foreground">
            Malzemeleri ekleyin, düzenleyin ve yönetin
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Yeni Malzeme
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Malzeme</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total}</div>
            <p className="text-xs text-muted-foreground">
              Kayıtlı malzeme sayısı
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Açıklamalı</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.withDescription}</div>
            <p className="text-xs text-muted-foreground">
              Açıklaması olan malzemeler
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Farklı Birim</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.units}</div>
            <p className="text-xs text-muted-foreground">
              Kullanılan birim türü
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Malzemeler</CardTitle>
            <div className="flex items-center gap-2">
              {selectedMaterials.length > 0 && (
                <>
                  <Badge variant="secondary">
                    {selectedMaterials.length} seçili
                  </Badge>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Seçilenleri Sil
                  </Button>
                  <Separator orientation="vertical" className="h-6" />
                </>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={exportToCSV}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                CSV İndir
              </Button>
            </div>
          </div>
          
          {/* Search Bar */}
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Malzeme ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="sm" className="gap-2">
              <Filter className="h-4 w-4" />
              Filtrele
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {loading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[300px]" />
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-4 w-[80px]" />
                </div>
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <button
                      onClick={toggleSelectAll}
                      className="flex items-center justify-center"
                    >
                      {selectedMaterials.length === filteredMaterials.length && filteredMaterials.length > 0 ? (
                        <CheckSquare className="h-4 w-4" />
                      ) : (
                        <Square className="h-4 w-4" />
                      )}
                    </button>
                  </TableHead>
                  <TableHead>Malzeme Adı</TableHead>
                  <TableHead>Açıklama</TableHead>
                  <TableHead>Birim</TableHead>
                  <TableHead>Oluşturma Tarihi</TableHead>
                  <TableHead className="w-16">İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMaterials.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Package className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {searchTerm ? "Arama kriterlerine uygun malzeme bulunamadı" : "Henüz malzeme eklenmemiş"}
                        </p>
                        {!searchTerm && (
                          <Button onClick={handleCreate} className="gap-2">
                            <Plus className="h-4 w-4" />
                            İlk Malzemeyi Ekle
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredMaterials.map((material) => (
                    <TableRow key={material.id}>
                      <TableCell>
                        <button
                          onClick={() => toggleSelectMaterial(material.id)}
                          className="flex items-center justify-center"
                        >
                          {selectedMaterials.includes(material.id) ? (
                            <CheckSquare className="h-4 w-4" />
                          ) : (
                            <Square className="h-4 w-4" />
                          )}
                        </button>
                      </TableCell>
                      <TableCell className="font-medium">{material.ad}</TableCell>
                      <TableCell className="max-w-xs truncate">
                        {material.aciklama || (
                          <span className="text-muted-foreground italic">Açıklama yok</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{material.birim || "Birim yok"}</Badge>
                      </TableCell>
                      <TableCell>
                        {material.olusturulma_tarihi
                          ? new Date(material.olusturulma_tarihi).toLocaleDateString("tr-TR")
                          : "-"
                        }
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(material)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Düzenle
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDelete(material)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Sil
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isCreateDialogOpen || isEditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setIsCreateDialogOpen(false)
          setIsEditDialogOpen(false)
          resetForm()
        }
      }}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {editingMaterial ? "Malzeme Düzenle" : "Yeni Malzeme Ekle"}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="ad">Malzeme Adı *</Label>
              <Input
                id="ad"
                placeholder="Örn: Çimento, Boya, Vida..."
                value={formData.ad}
                onChange={(e) => setFormData(prev => ({ ...prev, ad: e.target.value }))}
                className={formErrors.ad ? "border-destructive" : ""}
              />
              {formErrors.ad && (
                <p className="text-sm text-destructive">{formErrors.ad}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="birim">Birim *</Label>
              <Input
                id="birim"
                placeholder="Örn: Kg, Adet, Litre..."
                value={formData.birim}
                onChange={(e) => setFormData(prev => ({ ...prev, birim: e.target.value }))}
                className={formErrors.birim ? "border-destructive" : ""}
                list="units"
              />
              <datalist id="units">
                {COMMON_UNITS.map(unit => (
                  <option key={unit} value={unit} />
                ))}
              </datalist>
              {formErrors.birim && (
                <p className="text-sm text-destructive">{formErrors.birim}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="aciklama">Açıklama</Label>
              <Textarea
                id="aciklama"
                placeholder="Malzeme hakkında detaylı bilgi..."
                value={formData.aciklama}
                onChange={(e) => setFormData(prev => ({ ...prev, aciklama: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateDialogOpen(false)
                setIsEditDialogOpen(false)
                resetForm()
              }}
            >
              İptal
            </Button>
            <Button onClick={submitForm} disabled={isSubmitting}>
              {isSubmitting ? "Kaydediliyor..." : (editingMaterial ? "Güncelle" : "Ekle")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Malzemeyi Sil</AlertDialogTitle>
            <AlertDialogDescription>
              <strong>{deletingMaterial?.ad}</strong> malzemesini silmek istediğinizden emin misiniz?
              Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Dialog */}
      <AlertDialog open={isBulkDeleteDialogOpen} onOpenChange={setIsBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Seçili Malzemeleri Sil</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedMaterials.length} malzemeyi silmek istediğinizden emin misiniz?
              Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={confirmBulkDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Tümünü Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
