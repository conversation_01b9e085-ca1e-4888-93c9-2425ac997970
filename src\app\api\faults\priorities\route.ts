import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const priorities = await prisma.aciliyetSeviye.findMany({
      where: {
        silindi_mi: false,
      },
      orderBy: {
        seviye: "asc",
      },
    })

    return NextResponse.json({
      priorities,
    })
  } catch (error) {
    console.error("Error fetching fault priorities:", error)
    return NextResponse.json(
      { message: "Öncelik seviyeleri yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
} 