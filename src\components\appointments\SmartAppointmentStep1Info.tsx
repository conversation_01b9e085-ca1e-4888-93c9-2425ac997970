import React, { useState } from "react"
import { AppointmentStatus, APPOINTMENT_STATUS_LABELS } from "@/lib/enums"

interface Step1Props {
  data: any
  onNext: (data: any) => void
}

export default function Step1AppointmentInfo({ data, onNext }: Step1Props) {
  const [date, setDate] = useState(data.date || "")
  const [time, setTime] = useState(data.time || "")
  const [status, setStatus] = useState(data.status || AppointmentStatus.PLANLI)
  const [description, setDescription] = useState(data.description || "")
  const [errors, setErrors] = useState<{date?: string, time?: string, status?: string}>({})

  const validate = () => {
    const newErrors: {date?: string, time?: string, status?: string} = {}
    if (!date) newErrors.date = "Tarih zorunludur."
    if (!time) newErrors.time = "Saat zorunludur."
    if (!status) newErrors.status = "Durum seçmelisiniz."
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!validate()) return
    setErrors({})
    onNext({ date, time, status, description })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-zinc-200">Randevu Tarihi</label>
          <input
            type="date"
            value={date}
            onChange={e => setDate(e.target.value)}
            className={`w-full text-base font-medium border rounded-lg px-4 py-2 bg-white dark:bg-zinc-900 focus:outline-none focus:ring-2 focus:ring-blue-500 transition ${errors.date ? 'border-red-500' : 'border-gray-300 dark:border-zinc-700'}`}
          />
          {errors.date && <div className="text-red-600 text-xs mt-1">{errors.date}</div>}
        </div>
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-zinc-200">Randevu Saati</label>
          <input
            type="time"
            value={time}
            onChange={e => setTime(e.target.value)}
            className={`w-full text-base font-medium border rounded-lg px-4 py-2 bg-white dark:bg-zinc-900 focus:outline-none focus:ring-2 focus:ring-blue-500 transition ${errors.time ? 'border-red-500' : 'border-gray-300 dark:border-zinc-700'}`}
          />
          {errors.time && <div className="text-red-600 text-xs mt-1">{errors.time}</div>}
        </div>
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-zinc-200">Randevu Durumu</label>
          <select
            value={status}
            onChange={e => setStatus(e.target.value)}
            className={`w-full text-base font-medium border rounded-lg px-4 py-2 bg-white dark:bg-zinc-900 focus:outline-none focus:ring-2 focus:ring-blue-500 transition ${errors.status ? 'border-red-500' : 'border-gray-300 dark:border-zinc-700'}`}
          >
            {Object.entries(APPOINTMENT_STATUS_LABELS).map(([value, label]) => (
              <option key={value} value={value}>{label}</option>
            ))}
          </select>
          {errors.status && <div className="text-red-600 text-xs mt-1">{errors.status}</div>}
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-zinc-200">Randevu Açıklaması (isteğe bağlı)</label>
        <textarea
          value={description}
          onChange={e => setDescription(e.target.value)}
          placeholder="Randevu hakkında ek bilgiler, özel talimatlar..."
          className="w-full border border-gray-300 dark:border-zinc-700 rounded-lg px-4 py-2 min-h-[80px] bg-white dark:bg-zinc-900 focus:outline-none focus:ring-2 focus:ring-blue-500 transition resize-none text-base font-medium"
        />
      </div>
      <div className="flex justify-end pt-2">
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow transition"
        >
          Teknisyen Seçimine Geç
        </button>
      </div>
    </form>
  )
} 