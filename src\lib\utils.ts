import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Türkçe karakterleri ve özel karakterleri temizleyerek slug oluşturur
 * @param text - Slug oluşturulacak metin
 * @returns Temizlenmiş slug
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[çğışöü]/g, (match) => {
      const replacements: { [key: string]: string } = {
        'ç': 'c', 'ğ': 'g', 'ı': 'i', 'ş': 's', 'ö': 'o', 'ü': 'u'
      }
      return replacements[match] || match
    })
    .replace(/[^a-z0-9\s-]/g, '') // Sadece harf, rakam, boşluk ve tire bırak
    .replace(/\s+/g, '-') // Boşlukları tire ile değiştir
    .replace(/-+/g, '-') // Birden fazla tireyi tek tireye çevir
    .replace(/^-|-$/g, '') // Başta ve sonda tire varsa kaldır
}

/**
 * Docker container içinde internal API çağrıları için base URL döndürür
 * Production'da container içinde localhost:3000 kullanır
 * Development'da dinamik host header kullanır
 */
export async function getInternalApiBaseUrl(): Promise<string> {
  if (process.env.NODE_ENV === 'production') {
    // Sadece IPv4 loopback kullan!
    return 'http://127.0.0.1:3000';
  }
  // Development environment - dinamik host kullan
  try {
    const { headers } = await import('next/headers');
    const headersList = await headers();
    const host = headersList.get('host') || 'localhost:3000';
    return `http://${host}`;
  } catch {
    return 'http://127.0.0.1:3000';
  }
}
