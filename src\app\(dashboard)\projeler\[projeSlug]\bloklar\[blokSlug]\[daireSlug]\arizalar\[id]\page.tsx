"use client"

import { useState, useEffect } from "react"
import { notFound } from "next/navigation"
import Image from "next/image"
import { ArrowLeft, Edit, Calendar, MapPin, User, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, ChevronRight } from "lucide-react"
import Link from "next/link"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DeleteFaultDialog } from "@/components/faults/delete-fault-dialog"
import { AppointmentTable } from "@/components/appointments/appointment-table"
import { AppointmentStatus } from "@/lib/enums"

interface FaultDetailPageProps {
  params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string }>
}

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
}

interface AppointmentTechnician {
  teknisyen: Technician
}

interface FaultAppointment {
  id: string
  randevu_tarihi: Date
  durum: AppointmentStatus
  aciklama: string | null
  teknisyenler: AppointmentTechnician[]
}

interface FaultData {
  id: string
  numara: string
  baslik: string
  aciklama: string
  resimler: string[]
  bildiren_ad_soyad: string
  bildiren_telefon: string
  olusturulma_tarihi: Date
  guncelleme_tarihi: Date
  tip: { ad: string; renk: string }
  durum: { ad: string; renk: string }
  aciliyet: { ad: string; renk: string }
  daire: {
    id: string
    numara: string
    slug: string
    blok: {
      id: string
      ad: string
      slug: string
      proje: { 
        id: string
        ad: string
        slug: string
      }
    }
  }

  randevular: FaultAppointment[]
}

const formatDate = (dateInput: string | Date) => {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput
  return date.toLocaleDateString("tr-TR", {
    day: "2-digit",
    month: "2-digit", 
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "BEKLEMEDE":
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    case "DEVAM_EDIYOR":
      return <Wrench className="h-4 w-4 text-blue-600" />
    case "TAMAMLANDI":
      return <span className="h-4 w-4 text-green-600">✓</span>
    case "IPTAL":
      return <span className="h-4 w-4 text-red-600">✕</span>
    default:
      return <AlertTriangle className="h-4 w-4" />
  }
}

export default function FaultDetailPage({ params }: FaultDetailPageProps) {
  const [fault, setFault] = useState<FaultData | null>(null)
  const [loading, setLoading] = useState(true)
  const [faultId, setFaultId] = useState<string | null>(null)
  const [projeSlug, setProjeSlug] = useState<string | null>(null)
  const [blokSlug, setBlokSlug] = useState<string | null>(null)
  const [daireSlug, setDaireSlug] = useState<string | null>(null)

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParams = await params
      setFaultId(resolvedParams.id)
      setProjeSlug(resolvedParams.projeSlug)
      setBlokSlug(resolvedParams.blokSlug)
      setDaireSlug(resolvedParams.daireSlug)
    }
    loadParams()
  }, [params])

  const loadFaultData = async () => {
    if (!faultId || !projeSlug || !blokSlug || !daireSlug) return
    
    try {
      setLoading(true)
      // Add timestamp to prevent caching
      const response = await fetch(`/api/projects/${projeSlug}/blocks/${blokSlug}/apartments/${daireSlug}/arizalar/${faultId}?t=${Date.now()}`, {
        cache: 'no-store'
      })
      if (!response.ok) {
        throw new Error('Failed to fetch fault data')
      }
      const data = await response.json()
      setFault(data)
    } catch (error) {
      console.error('Error loading fault data:', error)
      // Handle error - could show error message or redirect
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    if (faultId && projeSlug && blokSlug && daireSlug) {
      loadFaultData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [faultId, projeSlug, blokSlug, daireSlug])

  if (loading) {
    return <div className="flex items-center justify-center min-h-96">
      <div className="text-lg">Yükleniyor...</div>
    </div>
  }

  if (!fault) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Custom Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Ana Sayfa
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler`} className="hover:text-foreground transition-colors">
          Projeler
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler/${fault.daire.blok.proje.slug}`} className="hover:text-foreground transition-colors">
          {fault.daire.blok.proje.ad}
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}`} className="hover:text-foreground transition-colors">
          {fault.daire.blok.ad}
        </Link>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}`} className="hover:text-foreground transition-colors">
          Daire {fault.daire.numara}
        </Link>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground">Arıza #{fault.numara}</span>
      </div>

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Daire Arızalarına Dön
          </Link>
        </Button>
        <div className="flex-1">
          <h2 className="text-3xl font-bold tracking-tight">
            Arıza #{fault.numara}
          </h2>
          <p className="text-muted-foreground">
            {fault.baslik}
          </p>
          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
            <MapPin className="h-3 w-3" />
            {fault.daire.blok.proje.ad} - {fault.daire.blok.ad} - Daire {fault.daire.numara}
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.id}/duzenle`}>
              <Edit className="mr-2 h-4 w-4" />
              Düzenle
            </Link>
          </Button>
          <DeleteFaultDialog
            faultId={fault.id}
            faultNumber={fault.numara}
            faultTitle={fault.baslik}
            redirectTo={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}`}
          />
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Ana Bilgiler */}
        <div className="md:col-span-2 space-y-6">
          {/* Arıza Detayları */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Arıza Detayları
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Başlık</h4>
                <p>{fault.baslik}</p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Açıklama</h4>
                <p className="whitespace-pre-wrap">{fault.aciklama}</p>
              </div>
              <div className="flex gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Tip</h4>
                  <Badge variant="outline" style={{ backgroundColor: fault.tip.renk + "20" }}>
                    {fault.tip.ad}
                  </Badge>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Durum</h4>
                  <Badge variant="outline" style={{ backgroundColor: fault.durum.renk + "20", borderColor: fault.durum.renk }}>
                    {getStatusIcon(fault.durum.ad)}
                    <span className="ml-1">{fault.durum.ad}</span>
                  </Badge>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Öncelik</h4>
                  <Badge variant="outline" style={{ backgroundColor: fault.aciliyet.renk + "20", borderColor: fault.aciliyet.renk }}>
                    {fault.aciliyet.ad}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Fotoğraflar */}
          {fault.resimler && fault.resimler.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Fotoğraflar</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {fault.resimler.map((fotoUrl: string, index: number) => (
                    <div key={index} className="aspect-square rounded-lg overflow-hidden border">
                      <Image 
                        src={fotoUrl} 
                        alt={`Arıza fotoğrafı ${index + 1}`}
                        width={300}
                        height={300}
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Randevular */}
          <AppointmentTable 
            faultId={fault.id}
            appointments={fault.randevular || []}
            faultStatus={fault.durum.ad}
            onAppointmentAdded={loadFaultData}
          />
        </div>

        {/* Yan Panel */}
        <div className="space-y-6">
          {/* Konum Bilgisi */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Konum
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Proje</p>
                <p className="font-medium">{fault.daire.blok.proje.ad}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Blok</p>
                <p className="font-medium">{fault.daire.blok.ad}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Daire</p>
                <p className="font-medium">Daire {fault.daire.numara}</p>
              </div>
            </CardContent>
          </Card>

          {/* Bildiren Kişi */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Bildiren Kişi
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-2">Daire Sahibi/Kiracı</p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {fault.bildiren_ad_soyad.split(' ').map(name => name.charAt(0)).join('').slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{fault.bildiren_ad_soyad}</p>
                      <p className="text-sm text-muted-foreground">{fault.bildiren_telefon}</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Zaman Bilgileri */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Zaman Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Oluşturulma</p>
                <p className="font-medium">{formatDate(fault.olusturulma_tarihi)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Son Güncelleme</p>
                <p className="font-medium">{formatDate(fault.guncelleme_tarihi)}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 