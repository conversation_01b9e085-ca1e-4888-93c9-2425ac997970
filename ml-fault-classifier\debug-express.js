console.log('🔍 Express Debug Başlıyor...');

try {
  console.log('1. Express require ediliyor...');
  const express = require('express');
  console.log('✅ Express require edildi');
  
  console.log('2. Express app oluşturuluyor...');
  const app = express();
  console.log('✅ Express app oluşturuldu');
  
  console.log('3. Middleware ekleniyor...');
  app.use(express.json());
  console.log('✅ JSON middleware eklendi');
  
  console.log('4. Basit route ekleniyor...');
  app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
  });
  console.log('✅ Route eklendi');
  
  console.log('5. Server başlatılıyor...');
  const server = app.listen(3050, () => {
    console.log('✅ Express server 3050 portunda başarıyla başlatıldı!');
    console.log('🔗 Test: http://localhost:3050/health');
    
    // 5 saniye sonra kapat
    setTimeout(() => {
      console.log('🛑 Test tamamlandı, server kapatılıyor...');
      server.close();
      process.exit(0);
    }, 5000);
  });
  
  server.on('error', (error) => {
    console.error('❌ Server error:', error.message);
    process.exit(1);
  });
  
} catch (error) {
  console.error('❌ Hata:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
} 