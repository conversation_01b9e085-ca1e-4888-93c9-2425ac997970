import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string; randevuId: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug, id: arizaSlug, randevuId: randevuSlug } = await params

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Arızayı bul
    const fault = await prisma.ariza.findFirst({
      where: { 
        slug: arizaSlug,
        daire_id: apartment.id, 
        silindi_mi: false 
      }
    });
    if (!fault) return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 });

    // Randevuyu bul
    const appointment = await prisma.randevu.findFirst({
      where: { 
        slug: randevuSlug,
        ariza_id: fault.id,
        silindi_mi: false
      },
      include: {
        ariza: {
          select: {
            id: true,
            baslik: true,
            slug: true,
            numara: true,
            daire: {
              select: {
                numara: true,
                slug: true,
                blok: {
                  select: {
                    ad: true,
                    slug: true,
                    proje: {
                      select: {
                        ad: true,
                        slug: true
                      }
                    }
                  }
                }
              }
            }
          }
        },
        teknisyenler: {
          include: {
            teknisyen: {
              select: {
                id: true,
                ad: true,
                soyad: true,
                email: true,
                telefon: true,
                resim: true,
                uzmanlik_alanlari: {
                  select: {
                    id: true,
                    seviye: true,
                    uzmanlik_alani: {
                      select: {
                        id: true,
                        ad: true,
                        aciklama: true,
                        renk: true,
                      }
                    }
                  },
                  orderBy: {
                    seviye: "desc",
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!appointment) {
      return NextResponse.json(
        { error: "Randevu bulunamadı" },
        { status: 404 }
      )
    }

    // Transform the data to match frontend expectations
    const transformedAppointment = {
      ...appointment,
      teknisyenler: appointment.teknisyenler.map(at => ({
        ...at,
        teknisyen: {
          ...at.teknisyen,
          uzmanlikAlanlari: at.teknisyen.uzmanlik_alanlari.map(ua => ({
            id: ua.uzmanlik_alani.id,
            ad: ua.uzmanlik_alani.ad,
            aciklama: ua.uzmanlik_alani.aciklama,
            renk: ua.uzmanlik_alani.renk,
            seviye: ua.seviye
          }))
        }
      }))
    }

    return NextResponse.json(transformedAppointment)
  } catch (error) {
    console.error("Error fetching appointment:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string; randevuId: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug, id: arizaSlug, randevuId: randevuSlug } = await params
    const body = await request.json()
    const { randevu_tarihi, durum, aciklama, teknisyen_ids, yapilan_islemler } = body

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Arızayı bul
    const fault = await prisma.ariza.findFirst({
      where: { 
        slug: arizaSlug,
        daire_id: apartment.id, 
        silindi_mi: false 
      }
    });
    if (!fault) return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 });

    // Randevuyu bul
    const existingAppointment = await prisma.randevu.findFirst({
      where: { 
        slug: randevuSlug,
        ariza_id: fault.id,
        silindi_mi: false
      }
    })

    if (!existingAppointment) {
      return NextResponse.json(
        { error: "Randevu bulunamadı" },
        { status: 404 }
      )
    }

    // Update appointment
    const updatedAppointment = await prisma.randevu.update({
      where: { id: existingAppointment.id },
      data: {
        randevu_tarihi: randevu_tarihi ? new Date(randevu_tarihi) : undefined,
        durum: durum || undefined,
        aciklama: aciklama !== undefined ? aciklama : undefined,
        yapilan_islemler: yapilan_islemler !== undefined ? yapilan_islemler : undefined,
        guncelleme_tarihi: new Date(),
        // If teknisyen_ids is provided, update technician assignments
        ...(teknisyen_ids && {
          teknisyenler: {
            deleteMany: {}, // Remove all existing assignments
            create: teknisyen_ids.map((teknisyen_id: string) => ({
              teknisyen_id
            }))
          }
        })
      },
      include: {
        ariza: {
          select: {
            id: true,
            baslik: true,
            slug: true,
            numara: true,
            daire: {
              select: {
                numara: true,
                slug: true,
                blok: {
                  select: {
                    ad: true,
                    slug: true,
                    proje: {
                      select: {
                        ad: true,
                        slug: true
                      }
                    }
                  }
                }
              }
            }
          }
        },
        teknisyenler: {
          include: {
            teknisyen: {
              select: {
                id: true,
                ad: true,
                soyad: true,
                email: true,
                telefon: true,
                resim: true,
                uzmanlik_alanlari: {
                  select: {
                    id: true,
                    seviye: true,
                    uzmanlik_alani: {
                      select: {
                        id: true,
                        ad: true,
                        aciklama: true,
                        renk: true,
                      }
                    }
                  },
                  orderBy: {
                    seviye: "desc",
                  }
                }
              }
            }
          }
        }
      }
    })

    // Transform the data to match frontend expectations
    const transformedAppointment = {
      ...updatedAppointment,
      teknisyenler: updatedAppointment.teknisyenler.map(at => ({
        ...at,
        teknisyen: {
          ...at.teknisyen,
          uzmanlikAlanlari: at.teknisyen.uzmanlik_alanlari.map(ua => ({
            id: ua.uzmanlik_alani.id,
            ad: ua.uzmanlik_alani.ad,
            aciklama: ua.uzmanlik_alani.aciklama,
            renk: ua.uzmanlik_alani.renk,
            seviye: ua.seviye
          }))
        }
      }))
    }

    return NextResponse.json(transformedAppointment)
  } catch (error) {
    console.error("Error updating appointment:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string; randevuId: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug, id: arizaSlug, randevuId: randevuSlug } = await params

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Arızayı bul
    const fault = await prisma.ariza.findFirst({
      where: { 
        slug: arizaSlug,
        daire_id: apartment.id, 
        silindi_mi: false 
      }
    });
    if (!fault) return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 });

    // Randevuyu bul
    const existingAppointment = await prisma.randevu.findFirst({
      where: { 
        slug: randevuSlug,
        ariza_id: fault.id,
        silindi_mi: false
      }
    })

    if (!existingAppointment) {
      return NextResponse.json(
        { error: "Randevu bulunamadı" },
        { status: 404 }
      )
    }

    // Soft delete (mark as deleted)
    await prisma.randevu.update({
      where: { id: existingAppointment.id },
      data: {
        silindi_mi: true,
        silinme_tarihi: new Date(),
        silen_id: "system" // Use system or session user
      }
    })

    return NextResponse.json({ message: "Randevu başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting appointment:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 