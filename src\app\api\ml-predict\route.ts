import { NextRequest, NextResponse } from "next/server"

const ML_SERVICE_URL = process.env.ML_SERVICE_URL || 'http://localhost:3050'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description } = body

    if (!title || !description) {
      return NextResponse.json(
        { 
          error: 'Başlık ve açıklama gereklidir',
          required: ['title', 'description']
        },
        { status: 400 }
      )
    }

    // ML servisine istek gönder
    const response = await fetch(`${ML_SERVICE_URL}/predict`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ title, description }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      return NextResponse.json(
        { 
          error: 'ML servisi hatası',
          details: errorData
        },
        { status: response.status }
      )
    }

    const prediction = await response.json()

    return NextResponse.json({
      success: true,
      prediction: prediction.prediction,
      availableCategories: prediction.availableCategories,
      timestamp: prediction.timestamp
    })

  } catch (error) {
    console.error('ML tahmin hatası:', error)
    return NextResponse.json(
      { 
        error: 'ML tahmin sırasında hata oluştu',
        message: error instanceof Error ? error.message : 'Bilinmeyen hata'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    // ML servisinin durumunu kontrol et
    const response = await fetch(`${ML_SERVICE_URL}/health`)
    
    if (!response.ok) {
      return NextResponse.json(
        { 
          error: 'ML servisi erişilemiyor',
          status: 'unavailable'
        },
        { status: 503 }
      )
    }

    const health = await response.json()

    return NextResponse.json({
      success: true,
      mlService: health,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('ML servis durumu hatası:', error)
    return NextResponse.json(
      { 
        error: 'ML servis durumu alınamadı',
        status: 'error'
      },
      { status: 500 }
    )
  }
} 