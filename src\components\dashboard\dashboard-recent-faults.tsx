import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Eye, Edit, ChevronLeft, ChevronRight } from "lucide-react"

export function DashboardRecentFaults() {
  const recentFaults = [
    {
      id: "ARZ-2023-062",
      date: "12.06.2023",
      location: "Blok A, Daire 15",
      resident: "<PERSON><PERSON> Yılmaz",
      type: "Elektrik",
      priority: "critical",
      priorityLabel: "Kritik",
      status: "in-progress",
      statusLabel: "<PERSON><PERSON> Ediyor",
      assignee: {
        name: "<PERSON><PERSON><PERSON>",
        role: "Elek<PERSON>k<PERSON><PERSON>",
        avatar: ""
      }
    },
    {
      id: "ARZ-2023-061",
      date: "11.06.2023",
      location: "Blok B, Daire 8",
      resident: "<PERSON><PERSON><PERSON><PERSON>",
      type: "<PERSON> Te<PERSON>atı",
      priority: "high",
      priorityLabel: "<PERSON><PERSON><PERSON><PERSON>",
      status: "assigned",
      statusLabel: "Atandı",
      assignee: {
        name: "Ali Vural",
        role: "Tesisatçı",
        avatar: ""
      }
    },
    {
      id: "ARZ-2023-060",
      date: "10.06.2023",
      location: "Blok C, Daire 12",
      resident: "Fatma Şahin",
      type: "Boya",
      priority: "medium",
      priorityLabel: "Orta",
      status: "pending",
      statusLabel: "Beklemede",
      assignee: {
        name: "Hasan Yıldız",
        role: "Boya Ustası",
        avatar: ""
      }
    },
    {
      id: "ARZ-2023-059",
      date: "09.06.2023",
      location: "Blok A, Daire 5",
      resident: "Zeynep Korkmaz",
      type: "Kapı Tamiri",
      priority: "low",
      priorityLabel: "Düşük",
      status: "completed",
      statusLabel: "Tamamlandı",
      assignee: {
        name: "Murat Demir",
        role: "Marangoz",
        avatar: ""
      }
    }
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800"
      case "high":
        return "bg-orange-100 text-orange-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800"
      case "assigned":
        return "bg-cyan-100 text-cyan-800"
      case "in-progress":
        return "bg-purple-100 text-purple-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "completed":
        return "bg-green-100 text-green-800"
      case "cancelled":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getRowClass = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-50 border-l-4 border-l-red-500"
      case "high":
        return "bg-orange-50 border-l-4 border-l-orange-500"
      case "medium":
        return "bg-yellow-50 border-l-4 border-l-yellow-500"
      case "low":
        return "bg-green-50 border-l-4 border-l-green-500"
      default:
        return ""
    }
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-semibold text-gray-800">
            Son Arızalar
          </CardTitle>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              Son 7 Gün
            </Button>
            <Button variant="default" size="sm" className="bg-blue-100 text-blue-600 hover:bg-blue-200">
              Son 30 Gün
            </Button>
            <Button variant="outline" size="sm">
              Tümü
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Arıza No</TableHead>
                <TableHead>Konum</TableHead>
                <TableHead>Tip</TableHead>
                <TableHead>Aciliyet</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead>Atanan</TableHead>
                <TableHead>İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentFaults.map((fault) => (
                <TableRow 
                  key={fault.id}
                  className={`hover:bg-gray-50 ${getRowClass(fault.priority)}`}
                >
                  <TableCell>
                    <div>
                      <div className="font-medium text-gray-900">{fault.id}</div>
                      <div className="text-sm text-gray-500">{fault.date}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm font-medium text-gray-900">{fault.location}</div>
                      <div className="text-sm text-gray-500">{fault.resident}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-900">{fault.type}</span>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPriorityColor(fault.priority)}>
                      {fault.priorityLabel}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(fault.status)}>
                      {fault.statusLabel}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={fault.assignee.avatar} />
                        <AvatarFallback>
                          {fault.assignee.name.split(" ").map(n => n[0]).join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {fault.assignee.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {fault.assignee.role}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4 text-blue-600" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Edit className="h-4 w-4 text-yellow-600" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Toplam <span className="font-medium">124</span> arızadan{" "}
            <span className="font-medium">1-4</span> arası gösteriliyor
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" disabled>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="default" size="sm" className="bg-blue-100 text-blue-600 hover:bg-blue-200">
              1
            </Button>
            <Button variant="outline" size="sm">2</Button>
            <Button variant="outline" size="sm">3</Button>
            <span className="px-3 py-1 text-sm">...</span>
            <Button variant="outline" size="sm">12</Button>
            <Button variant="outline" size="sm">
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
