# 🚨 Select Bileşeni Kullanım Kuralları

## Problem
Next.js/React Select bileşeninde **SelectItem value=""** kullanımı runtime hatası verir:
```
Error: A <Select.Item /> must have a value prop that is not an empty string.
```

## ✅ <PERSON>ğru Kullanım

### 1. Placeholder/Default <PERSON>
```tsx
// ❌ YANLIŞ
<SelectItem value="">Seçin</SelectItem>

// ✅ DOĞRU  
<SelectItem value="__none__" disabled>Seçin</SelectItem>
<SelectItem value="auto">Otomatik</SelectItem>
<SelectItem value="all">Tümü</SelectItem>
```

### 2. Loading States
```tsx
// ❌ YANLIŞ
<SelectItem value="" disabled>Yükleniyor...</SelectItem>

// ✅ DOĞRU
<SelectItem value="__loading__" disabled>Yükleniyor...</SelectItem>
```

### 3. Form Handling
```tsx
// Değer kontrolü
const handleFormSubmit = (data) => {
  const submitData = {
    ...data,
    teknisyenId: data.teknisyenId === "auto" ? null : data.teknisyenId,
    kategoriId: data.kategoriId === "__none__" ? null : data.kategoriId
  }
}
```

## 🔧 Sabit Değerler
Proje genelinde kullanılacak sabit değerler:

```tsx
export const SELECT_VALUES = {
  NONE: "__none__",      // Hiçbiri/boş seçenek
  ALL: "all",            // Tümü (filtrelerde)
  AUTO: "auto",          // Otomatik (atamalar için)
  LOADING: "__loading__", // Yükleniyor durumu
} as const
```

## 🚨 Kontrol Listesi
- [ ] SelectItem'da **asla** `value=""` kullanma
- [ ] Placeholder seçenekleri için `disabled` ekle
- [ ] Form submit'te özel değerleri (auto, __none__) null'a çevir
- [ ] Loading state'lerde `__loading__` kullan

## 🔍 Hızlı Arama
Projedeki tüm boş value kullanımlarını bulmak için:
```bash
grep -r 'value=""' src/
```
