import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string }> }
) {
  try {
    const resolvedParams = await params;
    const { projeSlug } = resolvedParams;

    const project = await prisma.proje.findFirst({
      where: {
        slug: projeSlug,
        silindi_mi: false,
      },
      select: {
        id: true,
        ad: true,
        slug: true,
        aciklama: true,
        adres: true,
        baslangic_tarihi: true,
        bitis_tarihi: true,
        project_image_url: true,
        olusturulma_tarihi: true,
        _count: {
          select: {
            bloklar: { where: { silindi_mi: false } },
          },
        },
        bloklar: {
          where: { silindi_mi: false },
          select: { id: true },
        },
      },
    });

    if (!project) {
      return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    }

    // Daire sayısı
    const daire_sayisi = await prisma.daire.count({
      where: {
        blok: {
          proje_id: project.id,
          silindi_mi: false,
        },
        silindi_mi: false,
      },
    });

    return NextResponse.json({
      ...project,
      blok_sayisi: project._count.bloklar,
      daire_sayisi,
    });
  } catch (error) {
    console.error("Proje detayı getirilirken hata:", error);
    return NextResponse.json(
      { error: "Proje detayı getirilemedi" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string }> }
) {
  try {
    const resolvedParams = await params;
    const { projeSlug } = resolvedParams;
    const body = await request.json();

    // Temel validasyon
    if (!body.ad || !body.slug) {
      return NextResponse.json({ error: "Proje adı ve slug zorunludur" }, { status: 400 });
    }

    // Önce eski slug ile projeyi bul
    const existing = await prisma.proje.findUnique({ where: { slug: projeSlug } });
    if (!existing) {
      return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    }

    // Güncellemeyi id ile yap
    const updated = await prisma.proje.update({
      where: { id: existing.id },
      data: {
        ad: body.ad,
        slug: body.slug,
        aciklama: body.aciklama ?? null,
        adres: body.adres ?? null,
        baslangic_tarihi: body.baslangic_tarihi ? new Date(body.baslangic_tarihi) : undefined,
        bitis_tarihi: body.bitis_tarihi ? new Date(body.bitis_tarihi) : undefined,
        project_image_url: body.project_image_url ?? null,
      },
    });

    return NextResponse.json({ success: true, project: updated });
  } catch (error) {
    console.error("Proje güncellenirken hata:", error);
    return NextResponse.json({ error: "Proje güncellenemedi" }, { status: 500 });
  }
} 