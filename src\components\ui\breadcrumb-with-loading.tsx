"use client"

import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { useRouter } from "next/navigation"
import { useLoading } from "@/contexts/loading-context"

interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

interface BreadcrumbWithLoadingProps {
  items: BreadcrumbItem[]
}

export function BreadcrumbWithLoading({ items }: BreadcrumbWithLoadingProps) {
  const router = useRouter()
  const { startLoading } = useLoading()

  const handleNavigation = (href: string) => {
    startLoading()
    router.push(href)
  }

  return (
    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && <ChevronRight className="h-4 w-4 mx-2" />}
          {item.href ? (
            <button
              onClick={() => handleNavigation(item.href!)}
              className="hover:text-foreground transition-colors"
            >
              {item.label}
            </button>
          ) : (
            <span className={item.isActive ? "font-medium text-foreground" : ""}>
              {item.label}
            </span>
          )}
        </div>
      ))}
    </div>
  )
} 