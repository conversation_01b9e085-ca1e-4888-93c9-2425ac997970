"use client"

import { useState, useEffect } from "react"
import { Plus, Package, Loader2, CheckCircle2, Trash2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useToast } from "@/hooks/use-toast"

interface Malzeme {
  id: string
  ad: string
  birim: string
}

interface Teknisyen {
  id: string
  ad: string
  soyad: string
  email: string
  resim: string | null
}

interface MaterialUsage {
  id: string
  malzeme: Malzeme
  teknisyen: Teknisyen
  miktar: number
  kullanim_aciklama: string | null
  olusturulma_tarihi: string
}

interface AppointmentMaterialUsageProps {
  appointmentId: string
  appointmentTechnicians: Array<{
    teknisyen: Teknisyen
  }>
}

const formSchema = z.object({
  malzeme_id: z.string().min(1, "Malzeme seçmelisiniz"),
  teknisyen_id: z.string().min(1, "Teknisyen seçmelisiniz"),
  miktar: z.number().min(0.1, "Miktar 0'dan büyük olmalıdır"),
  kullanim_aciklama: z.string().optional(),
})

type FormData = z.infer<typeof formSchema>

export function AppointmentMaterialUsage({ appointmentId, appointmentTechnicians }: AppointmentMaterialUsageProps) {  const [materialUsages, setMaterialUsages] = useState<MaterialUsage[]>([])
  const [malzemeler, setMalzemeler] = useState<Malzeme[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const { addToast } = useToast()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      malzeme_id: "",
      teknisyen_id: "",
      miktar: 1,
      kullanim_aciklama: "",
    },
  })

  // Veri yükleme
  const loadData = async () => {
    try {
      setLoading(true)
      
      // Malzeme kullanımlarını yükle
      const usageResponse = await fetch(`/api/appointments/${appointmentId}/malzemeler`)
      if (usageResponse.ok) {
        const usageData = await usageResponse.json()
        setMaterialUsages(usageData)
      }      // Malzemeleri yükle
      const materialsResponse = await fetch('/api/materials')
      if (materialsResponse.ok) {
        const materialsData = await materialsResponse.json()
        setMalzemeler(materialsData)
      }
    } catch (error) {
      console.error("Error loading data:", error)
      addToast({
        title: "Hata",
        message: "Veriler yüklenirken bir hata oluştu",
        type: "error",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [appointmentId])
  // Form gönderimi
  const onSubmit = async (data: FormData) => {
    try {
      setSubmitting(true)
      
      // Seçilen malzemeyi bul
      const selectedMaterial = malzemeler.find(m => m.id === data.malzeme_id)
      if (!selectedMaterial) {
        throw new Error("Seçilen malzeme bulunamadı")
      }

      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Malzeme kullanımı kaydedilemedi")
      }

      addToast({
        title: "Başarılı",
        message: "Malzeme kullanımı başarıyla kaydedildi",
        type: "success",
      })

      // Formu sıfırla ve dialog'u kapat
      form.reset()
      setIsAddDialogOpen(false)
      
      // Verileri yeniden yükle
      loadData()
    } catch (error) {
      console.error("Error submitting form:", error)
      addToast({
        title: "Hata",
        message: error instanceof Error ? error.message : "Malzeme kullanımı kaydedilirken bir hata oluştu",
        type: "error",
      })
    } finally {
      setSubmitting(false)
    }
  }

  // Malzeme silme
  const deleteMaterialUsage = async (usageId: string) => {
    if (!confirm("Bu malzeme kullanım kaydını silmek istediğinizden emin misiniz?")) {
      return
    }

    try {
      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler/${usageId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Malzeme kullanımı silinemedi")
      }

      addToast({
        title: "Başarılı",
        message: "Malzeme kullanımı başarıyla silindi",
        type: "success",
      })

      loadData()
    } catch (error) {
      console.error("Error deleting material usage:", error)
      addToast({
        title: "Hata",
        message: error instanceof Error ? error.message : "Malzeme kullanımı silinirken bir hata oluştu",
        type: "error",
      })
    }
  }
  // Filtrelenmiş malzemeler - artık kategori yok, hepsini göster
  const filteredMalzemeler = malzemeler

  const formatDateTime = (dateTimeString: string) => {
    return new Date(dateTimeString).toLocaleString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Malzeme Kullanımı
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Malzeme Kullanımı
              {materialUsages.length > 0 && (
                <Badge variant="secondary">
                  {materialUsages.length} kayıt
                </Badge>
              )}
            </CardTitle>            <div className="flex items-center gap-2">
              <Button onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Malzeme Ekle
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {materialUsages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>Henüz malzeme kullanım kaydı bulunmuyor</p>
              <p className="text-sm">Kullanılan malzemeleri kaydetmek için yukarıdaki butona tıklayın</p>
            </div>
          ) : (            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Malzeme</TableHead>
                  <TableHead>Miktar</TableHead>
                  <TableHead>Teknisyen</TableHead>
                  <TableHead>Tarih</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>                {materialUsages.map((usage) => (
                  <TableRow key={usage.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{usage.malzeme.ad}</p>
                        {usage.kullanim_aciklama && (
                          <p className="text-xs text-muted-foreground">{usage.kullanim_aciklama}</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {usage.miktar} {usage.malzeme.birim}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={usage.teknisyen.resim || ""} />
                          <AvatarFallback className="text-xs">
                            {usage.teknisyen.ad.charAt(0)}{usage.teknisyen.soyad.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{usage.teknisyen.ad} {usage.teknisyen.soyad}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {formatDateTime(usage.olusturulma_tarihi)}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteMaterialUsage(usage.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Malzeme Ekleme Dialog'u */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Malzeme Kullanımı Ekle
            </DialogTitle>
            <DialogDescription>
              Randevuda kullanılan malzemeyi kaydedin.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="teknisyen_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Teknisyen</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Teknisyen seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {appointmentTechnicians.map(({ teknisyen }) => (
                            <SelectItem key={teknisyen.id} value={teknisyen.id}>
                              {teknisyen.ad} {teknisyen.soyad}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="malzeme_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Malzeme</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Malzeme seçin" />
                        </SelectTrigger>                      </FormControl>
                      <SelectContent>
                        {filteredMalzemeler.map((malzeme) => (
                          <SelectItem key={malzeme.id} value={malzeme.id}>
                            <div className="flex items-center justify-between w-full">
                              <span>{malzeme.ad}</span>
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <span>{malzeme.birim}</span>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="miktar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Miktar</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.1"
                        min="0.1"
                        placeholder="Kullanılan miktar"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="kullanim_aciklama"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kullanım Açıklaması (İsteğe bağlı)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Malzeme ne için kullanıldı?"
                        className="resize-none"
                        rows={2}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  disabled={submitting}
                >
                  İptal
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Kaydediliyor...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Kaydet
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
