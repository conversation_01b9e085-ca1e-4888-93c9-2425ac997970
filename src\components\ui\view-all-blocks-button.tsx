"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useLoading } from "@/contexts/loading-context"

interface ViewAllBlocksButtonProps {
  projectSlug: string
}

export function ViewAllBlocksButton({ projectSlug }: ViewAllBlocksButtonProps) {
  const router = useRouter()
  const { startLoading } = useLoading()

  const handleClick = () => {
    startLoading()
    router.push(`/projeler/${projectSlug}/bloklar`)
  }

  return (
    <Button onClick={handleClick}>
      Tüm Blokları Görüntüle
    </Button>
  )
} 