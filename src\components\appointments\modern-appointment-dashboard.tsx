"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { 
  Calendar, 
  Clock, 
  Users, 
  CheckCircle, 
  AlertCircle, 
  Plus,
  Filter,
  Search,
  TrendingUp,
  Wrench,
  MapPin,
  Star,
  Zap,
  Target
} from "lucide-react"
import { toast } from "sonner"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AppointmentStatus, APPOINTMENT_STATUS_LABELS, APPOINTMENT_STATUS_COLORS } from "@/lib/enums"

interface AppointmentData {
  id: string
  randevu_tarihi: Date
  durum: keyof typeof AppointmentStatus
  aciklama: string | null
  ariza: {
    id: string
    numara: string
    baslik: string
    daire: {
      numara: string
      blok: {
        ad: string
        proje: {
          ad: string
          slug: string
        }
      }
    }
  }
  teknisyenler?: Array<{
    teknisyen?: {
      id: string
      ad: string
      soyad: string
    }
  }>
}

interface DashboardStats {
  todayTotal: number
  todayCompleted: number
  todayPending: number
  todayProgress: number
  weekTotal: number
  weekCompleted: number
  monthTotal: number
  monthCompleted: number
  averageCompletionTime: number
  technicianUtilization: number
}

interface ModernAppointmentDashboardProps {
  appointments: AppointmentData[]
  onRefresh: () => void
}

export function ModernAppointmentDashboard({ 
  appointments, 
  onRefresh 
}: ModernAppointmentDashboardProps) {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [quickFilter, setQuickFilter] = useState<"ALL" | "TODAY" | "PENDING" | "COMPLETED">("TODAY")
  const [stats, setStats] = useState<DashboardStats>({
    todayTotal: 0,
    todayCompleted: 0,
    todayPending: 0,
    todayProgress: 0,
    weekTotal: 0,
    weekCompleted: 0,
    monthTotal: 0,
    monthCompleted: 0,
    averageCompletionTime: 0,
    technicianUtilization: 0
  })

  useEffect(() => {
    calculateStats()
  }, [appointments])

  const calculateStats = () => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekStart = new Date(today.getTime() - (today.getDay() * 24 * 60 * 60 * 1000))
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)

    const todayAppointments = appointments.filter(apt => {
      const aptDate = new Date(apt.randevu_tarihi)
      return aptDate >= today && aptDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)
    })

    const weekAppointments = appointments.filter(apt => {
      const aptDate = new Date(apt.randevu_tarihi)
      return aptDate >= weekStart
    })

    const monthAppointments = appointments.filter(apt => {
      const aptDate = new Date(apt.randevu_tarihi)
      return aptDate >= monthStart
    })

    const todayCompleted = todayAppointments.filter(apt => apt.durum === 'TAMAMLANDI').length
    const todayPending = todayAppointments.filter(apt => apt.durum === 'PLANLI').length
    const weekCompleted = weekAppointments.filter(apt => apt.durum === 'TAMAMLANDI').length
    const monthCompleted = monthAppointments.filter(apt => apt.durum === 'TAMAMLANDI').length

    setStats({
      todayTotal: todayAppointments.length,
      todayCompleted,
      todayPending,
      todayProgress: todayAppointments.length > 0 ? Math.round((todayCompleted / todayAppointments.length) * 100) : 0,
      weekTotal: weekAppointments.length,
      weekCompleted,
      monthTotal: monthAppointments.length,
      monthCompleted,
      averageCompletionTime: 2.5, // Mock data - gerçekte hesaplanacak
      technicianUtilization: 85 // Mock data - gerçekte hesaplanacak
    })
  }

  const getFilteredAppointments = () => {
    let filtered = appointments

    // Quick filter
    if (quickFilter === "TODAY") {
      const today = new Date()
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000)
      filtered = filtered.filter(apt => {
        const aptDate = new Date(apt.randevu_tarihi)
        return aptDate >= todayStart && aptDate < todayEnd
      })
    } else if (quickFilter === "PENDING") {
      filtered = filtered.filter(apt => apt.durum === 'PLANLI')
    } else if (quickFilter === "COMPLETED") {
      filtered = filtered.filter(apt => apt.durum === 'TAMAMLANDI')
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(apt => 
        apt.ariza.numara.toLowerCase().includes(searchTerm.toLowerCase()) ||
        apt.ariza.baslik.toLowerCase().includes(searchTerm.toLowerCase()) ||
        apt.ariza.daire.numara.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered.slice(0, 10) // Son 10 randevu
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString("tr-TR", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("tr-TR", {
      day: "2-digit",
      month: "short",
    })
  }

  return (
    <div className="space-y-6">
      {/* Header with Quick Actions */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            📅 Randevu Yönetimi
          </h1>
          <p className="text-muted-foreground mt-1">
            Mevcut randevuları görüntüleyin ve yönetin
          </p>
        </div>
        
        <Button onClick={onRefresh} variant="outline" size="sm">
          <TrendingUp className="w-4 h-4 mr-2" />
          Yenile
        </Button>
      </div>

      {/* Modern Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100 hover:shadow-md transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 mb-1">Bugün Toplam</p>
                <p className="text-2xl font-bold text-blue-900">{stats.todayTotal}</p>
                <p className="text-xs text-blue-600 mt-1">
                  {stats.todayCompleted} tamamlandı
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <Progress value={stats.todayProgress} className="h-2" />
              <p className="text-xs text-blue-600 mt-1">%{stats.todayProgress} tamamlandı</p>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-emerald-50 to-emerald-100 hover:shadow-md transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-600 mb-1">Bu Hafta</p>
                <p className="text-2xl font-bold text-emerald-900">{stats.weekTotal}</p>
                <p className="text-xs text-emerald-600 mt-1">
                  {stats.weekCompleted} tamamlandı
                </p>
              </div>
              <div className="h-12 w-12 bg-emerald-200 rounded-full flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
            <div className="mt-4">
              <Progress 
                value={stats.weekTotal > 0 ? Math.round((stats.weekCompleted / stats.weekTotal) * 100) : 0} 
                className="h-2" 
              />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100 hover:shadow-md transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 mb-1">Bekleyen</p>
                <p className="text-2xl font-bold text-orange-900">{stats.todayPending}</p>
                <p className="text-xs text-orange-600 mt-1">
                  Bugün için
                </p>
              </div>
              <div className="h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100 hover:shadow-md transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 mb-1">Teknisyen</p>
                <p className="text-2xl font-bold text-purple-900">%{stats.technicianUtilization}</p>
                <p className="text-xs text-purple-600 mt-1">
                  Kullanım oranı
                </p>
              </div>
              <div className="h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex gap-2 flex-wrap">
          <Button
            variant={quickFilter === "TODAY" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("TODAY")}
          >
            <Zap className="w-4 h-4 mr-1" />
            Bugün
          </Button>
          <Button
            variant={quickFilter === "PENDING" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("PENDING")}
          >
            <AlertCircle className="w-4 h-4 mr-1" />
            Bekleyen
          </Button>
          <Button
            variant={quickFilter === "COMPLETED" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("COMPLETED")}
          >
            <CheckCircle className="w-4 h-4 mr-1" />
            Tamamlanan
          </Button>
          <Button
            variant={quickFilter === "ALL" ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilter("ALL")}
          >
            Tümü
          </Button>
        </div>

        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Arıza no, başlık veya daire ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 w-64"
          />
        </div>
      </div>

      {/* Today's Appointments */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            Son Randevular
            <Badge variant="secondary" className="ml-2">
              {getFilteredAppointments().length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {getFilteredAppointments().length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>Filtreye uygun randevu bulunamadı</p>
              </div>
            ) : (
              getFilteredAppointments().map((appointment) => (
                <div
                  key={appointment.id}
                  className={`flex items-center justify-between p-4 border rounded-lg transition-colors cursor-pointer ${
                    appointment.durum === "TAMAMLANDI" 
                      ? "bg-green-50 hover:bg-green-100 border-green-200 shadow-sm" 
                      : appointment.durum === "DEVAM_EDIYOR"
                      ? "bg-orange-50 hover:bg-orange-100 border-orange-200 shadow-sm"
                      : "hover:bg-muted/50"
                  }`}
                  onClick={() => {
                    router.push(`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}/${appointment.ariza.daire.numara}/arizalar/${appointment.ariza.id}/randevular/${appointment.id}`)
                  }}
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="flex flex-col items-center text-center min-w-[60px]">
                      <p className="text-xs text-muted-foreground">
                        {formatDate(appointment.randevu_tarihi)}
                      </p>
                      <p className="font-semibold text-sm">
                        {formatTime(appointment.randevu_tarihi)}
                      </p>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">
                          {appointment.ariza.numara}
                        </span>
                        <Badge 
                          style={{ 
                            backgroundColor: APPOINTMENT_STATUS_COLORS[appointment.durum] + "20",
                            borderColor: APPOINTMENT_STATUS_COLORS[appointment.durum],
                            color: APPOINTMENT_STATUS_COLORS[appointment.durum]
                          }}
                          className="text-xs"
                        >
                          {APPOINTMENT_STATUS_LABELS[appointment.durum]}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">
                        {appointment.ariza.baslik}
                      </p>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <MapPin className="h-3 w-3" />
                        {appointment.ariza.daire.blok.proje.ad} - {appointment.ariza.daire.blok.ad} - Daire {appointment.ariza.daire.numara}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {appointment.teknisyenler && appointment.teknisyenler.length > 0 ? (
                        <div className="flex -space-x-2">
                          {appointment.teknisyenler.slice(0, 2).map((at, index) => (
                            <Avatar key={index} className="h-8 w-8 border-2 border-background">
                              <AvatarFallback className="text-xs">
                                {at.teknisyen?.ad.charAt(0)}{at.teknisyen?.soyad.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                          ))}
                          {appointment.teknisyenler.length > 2 && (
                            <div className="h-8 w-8 rounded-full border-2 border-background bg-muted flex items-center justify-center text-xs font-medium">
                              +{appointment.teknisyenler.length - 2}
                            </div>
                          )}
                        </div>
                      ) : (
                        <Badge variant="outline" className="text-xs">
                          <Users className="h-3 w-3 mr-1" />
                          Atanmamış
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 