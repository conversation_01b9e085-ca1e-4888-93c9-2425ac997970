"use client"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"

const fetcher = async () => {
  const res = await fetch("/api/faults?limit=5")
  return res.json()
}

function DashboardRecentFaults() {
  const { data, isLoading } = useQuery({
    queryKey: ["faults", 5],
    queryFn: fetcher
  })

  if (isLoading || !data || !data.faults) {
    return <Card><CardContent>Yükleniyor...</CardContent></Card>
  }

  return (
    <Card>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Arıza No</TableHead>
              <TableHead>Başlık</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Tarih</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.faults.map((fault: { id: string; numara: string; baslik: string; durum?: { ad: string }; olusturulma_tarihi: string }) => (
              <TableRow key={fault.id}>
                <TableCell>#{fault.numara}</TableCell>
                <TableCell>{fault.baslik}</TableCell>
                <TableCell>{fault.durum?.ad}</TableCell>
                <TableCell>{new Date(fault.olusturulma_tarihi).toLocaleDateString("tr-TR")}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export default DashboardRecentFaults
