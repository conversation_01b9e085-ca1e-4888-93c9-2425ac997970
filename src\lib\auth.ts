import NextAuth from "next-auth"
import type { JWT } from "next-auth/jwt"
import type { Session, User } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import bcrypt from "bcryptjs"

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },      async authorize(credentials: Record<string, unknown> | undefined) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email as string,
            silindi_mi: false
          }
        })

        if (!user || !user.sifre) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password as string,
          user.sifre
        )

        if (!isPasswordValid) {
          return null
        }        // Check if user is approved - allow ACTIVE users to login
        if (user.durum !== 'ACTIVE') {
          throw new Error("Hesabınız henüz onaylanmamış. Lütfen yöneticinizle iletişime geçin.")
        }

        return {
          id: user.id,
          email: user.email,
          name: `${user.ad} ${user.soyad}`,
          role: user.rol,
          status: user.durum,
          image: user.resim,
        }
      }
    })
  ],
  pages: {
    signIn: "/auth/signin",
  },
  callbacks: {    async jwt({ token, user }: { token: JWT; user: User | null }) {      if (user) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        token.role = (user as any).role
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        token.status = (user as any).status
      }
      return token
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
        session.user.status = token.status as string
      }
      return session
    },
  },
})

declare module "next-auth" {
  interface User {
    role: string
    status: string
  }
  
  interface Session {
    user: {
      id: string
      role: string
      status: string
      email: string
      name: string
      image?: string
    }
  }
}
