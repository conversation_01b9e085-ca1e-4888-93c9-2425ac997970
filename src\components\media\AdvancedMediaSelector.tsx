import React, { useState, useEffect, useRef } from 'react';
import { Crop, Trash2 } from 'lucide-react';
import Cropper, { Area } from 'react-easy-crop';

export interface MediaFile {
  url: string;
  originalName: string;
  size: number;
  alt?: string;
}

interface AdvancedMediaSelectorProps {
  onSelect: (media: MediaFile | undefined) => void;
  open: boolean;
  onClose: () => void;
  selectedMedia?: MediaFile | null;
  buttonText?: string;
  title?: string;
  description?: string;
  acceptedTypes?: string[];
  maxSizeMB?: number;
  folder?: string;
}

export const AdvancedMediaSelector: React.FC<AdvancedMediaSelectorProps> = ({
  onSelect,
  open,
  onClose,
  selectedMedia,
  buttonText = 'Görsel Seç',
  title = 'Görsel Seç',
  description = '',
  acceptedTypes = ['image/*'],
  maxSizeMB = 5,
  folder = 'media',
}) => {
  const [tab, setTab] = useState<'gallery' | 'upload'>('gallery');
  const [search, setSearch] = useState('');
  const [mediaList, setMediaList] = useState<MediaFile[]>([]);

  // Debug wrapper for setMediaList
  const setMediaListWithLogging = (newList: MediaFile[] | ((prev: MediaFile[]) => MediaFile[])) => {
    if (typeof newList === 'function') {
      setMediaList(prev => {
        const result = newList(prev);
        console.log('📝 Media list updated via function:', {
          before: prev.length,
          after: result.length,
          urls: result.map(m => m.url)
        });
        return result;
      });
    } else {
      console.log('📝 Media list updated directly:', {
        count: newList.length,
        urls: newList.map(m => m.url)
      });
      setMediaList(newList);
    }
  };
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [pendingFiles, setPendingFiles] = useState<File[]>([]);
  const [cropFileIndex, setCropFileIndex] = useState<number | null>(null);
  const [cropImageUrl, setCropImageUrl] = useState<string | null>(null);
  const [cropWidth, setCropWidth] = useState(1200);
  const [cropHeight, setCropHeight] = useState(400);
  const [altText, setAltText] = useState('');
  const [recentlyUploadedUrl, setRecentlyUploadedUrl] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const readySizes = [
    { label: '1200x400', width: 1200, height: 400 },
    { label: '800x600', width: 800, height: 600 },
    { label: '600x400', width: 600, height: 400 },
  ];
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [mediaToDelete, setMediaToDelete] = useState<MediaFile | undefined>(undefined);

  // Parent'tan gelen selectedMedia sadece ilk açılışta initialize edilsin
  const [localSelectedMedia, setLocalSelectedMedia] = useState<MediaFile | undefined>(selectedMedia ?? undefined);
  const [croppedFile, setCroppedFile] = useState<File | null>(null);
  const [originalImageBeingCropped, setOriginalImageBeingCropped] = useState<MediaFile | null>(null);

  async function handleCropCompleteAndSave() {
    if (cropImageUrl && croppedAreaPixels !== null && cropFileIndex !== null) {
      const croppedBlob = await getCroppedImg(cropImageUrl, croppedAreaPixels);
      if (croppedBlob) {
        const file = new File([croppedBlob], `cropped_${Date.now()}.jpg`, { type: 'image/jpeg' });

        // If this is from upload tab (pendingFiles), upload immediately
        if (pendingFiles.length > 0) {
          console.log('🔄 Starting upload process for cropped image');
          setUploading(true);
          setError(null);
          const formData = new FormData();
          formData.append('file', file);
          formData.append('folder', folder);
          try {
            const res = await fetch('/api/upload', {
              method: 'POST',
              body: formData,
            });
            const data = await res.json();
            console.log('📤 Upload response:', data);
            if (data && data.media) {
              console.log('✅ Upload successful, refreshing gallery to include new image:', data.media.url);

              setLocalSelectedMedia(data.media);
              setRecentlyUploadedUrl(data.media.url); // Mark as recently uploaded
              setPendingFiles([]); // Clear pending files
              setTab('gallery'); // Switch to gallery tab
              setSuccessMessage('Görsel başarıyla kırpıldı ve galeriye eklendi!');

              // Refresh gallery to get the latest state from server (with deduplication)
              setTimeout(() => refreshGallery(), 100);

              // Clear indicators after 3 seconds
              setTimeout(() => {
                setRecentlyUploadedUrl(null);
                setSuccessMessage(null);
              }, 3000);
            } else {
              setError(data.error || 'Yükleme başarısız.');
            }
          } catch (e) {
            console.error('❌ Upload error:', e);
            setError('Yükleme sırasında hata oluştu.');
          } finally {
            setUploading(false);
          }
        } else {
          // If this is from gallery tab (existing image), just set cropped file
          setCroppedFile(file);
          setLocalSelectedMedia(undefined); // Seçimi sıfırla
        }
      }
      setCropModalOpen(false);
      setCropFileIndex(null);
      setCropImageUrl(null);
      setOriginalImageBeingCropped(null);
    }
  }

  // Crop helper
  async function getCroppedImg(imageSrc: string, crop: Area): Promise<Blob | null> {
    const image = await createImage(imageSrc);
    const canvas = document.createElement('canvas');
    canvas.width = crop.width;
    canvas.height = crop.height;
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    ctx.drawImage(
      image,
      crop.x,
      crop.y,
      crop.width,
      crop.height,
      0,
      0,
      crop.width,
      crop.height
    );
    return new Promise(resolve => {
      canvas.toBlob(blob => {
        resolve(blob);
      }, 'image/jpeg', 0.95);
    });
  }
  function createImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new window.Image();
      img.addEventListener('load', () => resolve(img));
      img.addEventListener('error', error => reject(error));
      img.setAttribute('crossOrigin', 'anonymous');
      img.src = url;
    });
  }

  useEffect(() => {
    if (open) {
      setLocalSelectedMedia(undefined); // Modal her açıldığında sıfırla
      setCropModalOpen(false);
      setCrop({ x: 0, y: 0 });
      setZoom(1);
      setCroppedAreaPixels(null);
      setPendingFiles([]);
      setCropFileIndex(null);
      setCropImageUrl(null);
      setCroppedFile(null); // Kırpılmış dosyayı sıfırla
      setOriginalImageBeingCropped(null);
      setRecentlyUploadedUrl(null); // Reset recently uploaded indicator
      setSuccessMessage(null); // Reset success message
      setAltText('');
      setError(null);
      setUploading(false);
    }
  }, [open]);

  // Deduplication helper function
  const deduplicateMediaList = (mediaList: MediaFile[]): MediaFile[] => {
    const seen = new Set<string>();
    const deduplicated: MediaFile[] = [];

    for (const media of mediaList) {
      // Use URL as the primary key for deduplication
      if (!seen.has(media.url)) {
        seen.add(media.url);
        deduplicated.push(media);
      } else {
        console.log('🚫 Skipping duplicate media:', media.url);
      }
    }

    console.log(`📊 Deduplication: ${mediaList.length} → ${deduplicated.length} items`);
    return deduplicated;
  };

  // Gallery refresh function with deduplication
  const refreshGallery = async () => {
    console.log('🔄 Refreshing gallery from API');
    setError(null);
    try {
      const res = await fetch(`/api/media?customFolder=${folder}`);
      const data = await res.json();
      const newMediaList = data.data || [];
      console.log('📥 Gallery refresh response:', newMediaList.map(m => ({ url: m.url, name: m.originalName })));

      // Apply deduplication before setting the media list
      const deduplicatedList = deduplicateMediaList(newMediaList);
      setMediaListWithLogging(deduplicatedList);
    } catch (e) {
      console.error('❌ Gallery refresh error:', e);
      setError('Galeri yüklenemedi.');
    }
  };

  useEffect(() => {
    if (open && tab === 'gallery') {
      console.log('🎯 Gallery tab opened, refreshing media list');
      refreshGallery();
    }
  }, [open, tab, folder]);

  // Arama ile filtrelenmiş liste
  const filteredMedia = search
    ? mediaList.filter(m => m.originalName.toLowerCase().includes(search.toLowerCase()))
    : mediaList;

  // Dosya yükleme fonksiyonu
  const handleFileUpload = async (file: File) => {
    setUploading(true);
    setError(null);
    if (file.size > maxSizeMB * 1024 * 1024) {
      setError(`Dosya çok büyük. Maksimum boyut: ${maxSizeMB}MB`);
      setUploading(false);
      return;
    }
    if (
      acceptedTypes[0] !== 'image/*' &&
      !acceptedTypes.some(type => file.type === type)
    ) {
      setError('Desteklenmeyen dosya formatı.');
      setUploading(false);
      return;
    }
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', folder);
    try {
      const res = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      const data = await res.json();
      if (data && data.media) {
        setMediaList(prev => [data.media, ...prev]);
        setLocalSelectedMedia(data.media);
        setTab('gallery');
      } else {
        setError(data.error || 'Yükleme başarısız.');
      }
    } catch (e) {
      setError('Yükleme sırasında hata oluştu.');
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  // Dosya seçilince otomatik olarak crop modalını aç
  const handlePendingFileAdd = (file: File) => {
    // Auto-crop için dosyayı direkt crop modalına gönder
    setCropImageUrl(URL.createObjectURL(file));
    setCropFileIndex(0); // Tek dosya için index 0
    setPendingFiles([file]); // Tek dosya olarak ayarla
    setCropModalOpen(true); // Crop modalını otomatik aç
  };

  // Silme işlemi
  async function handleDeleteMedia() {
    if (!mediaToDelete) return;
    setUploading(true);
    setError(null);
    try {
      const res = await fetch(`/api/media?url=${encodeURIComponent(mediaToDelete.url)}`, { method: 'DELETE' });
      if (res.ok) {
        setMediaListWithLogging(prev => prev.filter(m => m.url !== mediaToDelete.url));
        // Eğer silinen görsel seçiliyse parent'a da bildir
        if (localSelectedMedia?.url === mediaToDelete.url) {
          setLocalSelectedMedia(undefined);
          onSelect(undefined);
        }
        setDeleteDialogOpen(false);
        setMediaToDelete(undefined);
      } else {
        setError('Silme işlemi başarısız.');
      }
    } catch (e) {
      setError('Silme sırasında hata oluştu.');
    } finally {
      setUploading(false);
    }
  }

  // onSelect çağrıldığında hem local hem parent güncellensin
  const handleSelectMedia = (media: MediaFile | undefined) => {
    setLocalSelectedMedia(media);
    onSelect(media);
  };

  // Galeri grid'inde boş alana tıklayınca seçimi iptal et
  const handleGalleryBackgroundClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Sadece grid'in kendisine tıklanırsa (resim veya buton değil)
    if (e.target === e.currentTarget) {
      handleSelectMedia(undefined);
    }
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-3xl w-full min-h-[400px] flex flex-col relative">
        <div className="flex-1 flex flex-col">
          {/* Sağ üstte kapatma butonu */}
          <button
            type="button"
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-2xl font-bold focus:outline-none"
            aria-label="Kapat"
            onClick={onClose}
          >
            ×
          </button>
          <div className="font-bold text-lg mb-2">{title}</div>
          <div className="text-sm text-muted-foreground mb-4">{description}</div>
          <div className="flex gap-2 mb-4">
            <button
              type="button"
              className={`px-4 py-2 rounded ${tab === 'gallery' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
              onClick={() => setTab('gallery')}
            >
              Galeri
            </button>
            <button
              type="button"
              className={`px-4 py-2 rounded ${tab === 'upload' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}`}
              onClick={() => setTab('upload')}
            >
              Yükle
            </button>
          </div>
          {tab === 'gallery' && (
            <>
              <div className="mb-2">
                <span className="inline-block px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs font-semibold mr-2">Klasör: /{folder}/</span>
              </div>
              <input
                type="text"
                className="w-full border rounded px-3 py-2 mb-4"
                placeholder="Dosya ara..."
                value={search}
                onChange={e => setSearch(e.target.value)}
              />
              <div
                className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4 cursor-pointer"
                onClick={handleGalleryBackgroundClick}
                style={{ minHeight: 120 }}
              >
                {filteredMedia.length === 0 && <div className="col-span-full text-center text-gray-400">Hiç dosya yok</div>}
                {filteredMedia.map((media) => (
                  <div
                    key={media.url}
                    className={`relative border rounded cursor-pointer p-1 flex flex-col items-center transition-all ${
                      localSelectedMedia?.url === media.url
                        ? 'border-blue-500 ring-2 ring-blue-300'
                        : recentlyUploadedUrl === media.url
                        ? 'border-green-500 ring-2 ring-green-300 bg-green-50'
                        : 'hover:border-blue-300'
                    }`}
                    onClick={e => { e.stopPropagation(); handleSelectMedia(media); }}
                  >
                    <img src={media.url} alt={media.originalName} className="w-24 h-24 object-cover rounded mb-1" />
                    <div className="text-xs text-center truncate w-full">{media.originalName}</div>
                    {recentlyUploadedUrl === media.url && (
                      <div className="absolute top-1 left-1 px-1 py-0.5 bg-green-500 text-white text-xs rounded">
                        Yeni
                      </div>
                    )}
                    <button
                      type="button"
                      className="absolute top-1 right-1 p-1 bg-white rounded-full shadow hover:bg-red-100"
                      onClick={e => { e.stopPropagation(); setMediaToDelete(media); setDeleteDialogOpen(true); }}
                      title="Sil"
                      disabled={uploading}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </button>
                  </div>
                ))}
              </div>
              {/* Seçili Dosyalar Alanı sadece galeri tabında ve localSelectedMedia ile */}
              {tab === 'gallery' && localSelectedMedia && (
                <div className="mt-6">
                  <div className="font-semibold mb-2 text-sm">Seçilen Dosyalar (1)</div>
                  <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                    <img src={localSelectedMedia.url} alt={localSelectedMedia.alt || localSelectedMedia.originalName} className="w-16 h-16 object-cover rounded border" />
                    <div className="flex-1 min-w-0">
                      <div className="truncate font-medium text-gray-800 text-sm">{localSelectedMedia.originalName}</div>
                      <div className="text-xs text-gray-500">{(localSelectedMedia.size / 1024).toFixed(2)} KB</div>
                    </div>
                    <button
                      className="p-2 hover:bg-gray-200 rounded transition"
                      title="Kırp"
                      type="button"
                      onClick={() => {
                        setOriginalImageBeingCropped(localSelectedMedia);
                        setCropImageUrl(localSelectedMedia.url);
                        setCropModalOpen(true);
                      }}
                    >
                      <Crop className="w-5 h-5 text-blue-600" />
                    </button>
                    <button
                      className="p-2 hover:bg-red-100 rounded transition"
                      title="Kaldır"
                      type="button"
                      onClick={() => handleSelectMedia(undefined)}
                    >
                      <span className="text-lg text-red-500">×</span>
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
          {tab === 'upload' && (
            <div className="mt-4">
              {/* Yükleme alanı burada olacak */}
              {/* Sürükle-bırak yükleme alanı, dosya limiti, feedback */}
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center text-gray-400 mb-2 transition-all ${uploading ? 'opacity-60 pointer-events-none' : 'hover:border-blue-400'}`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => !uploading && fileInputRef.current?.click()}
                style={{ cursor: uploading ? 'not-allowed' : 'pointer' }}
              >
                <input
                  type="file"
                  accept={acceptedTypes.join(',')}
                  ref={fileInputRef}
                  className="hidden"
                  onChange={e => {
                    if (e.target.files && e.target.files[0]) {
                      handlePendingFileAdd(e.target.files[0]);
                      e.target.value = '';
                    }
                  }}
                  disabled={uploading}
                />
                <div className="flex flex-col items-center justify-center gap-2">
                  <svg width="40" height="40" fill="none" viewBox="0 0 24 24"><path d="M12 16V4m0 0-4 4m4-4 4 4" stroke="currentColor" strokeWidth="1.5"/></svg>
                  <span className="font-medium">Dosyaları sürükleyip bırakın</span>
                  <span className="text-xs text-gray-400">veya <span className="underline text-blue-600 cursor-pointer">yükleme için tıklayın</span></span>
                </div>
                {uploading && <div className="mt-2 text-blue-600 animate-pulse">Yükleniyor...</div>}
              </div>
              <div className="text-xs text-gray-500 mb-2">
                Desteklenen formatlar: {acceptedTypes.join(', ')} | Maksimum boyut: {maxSizeMB}MB
              </div>
              {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
              {successMessage && <div className="text-green-600 text-sm mb-2 font-medium">{successMessage}</div>}
              {/* Seçilen Dosyalar (pendingFiles) - Auto-crop workflow */}
              {pendingFiles.length > 0 && (
                <div className="mt-6">
                  <div className="font-semibold mb-2 text-sm">Kırpma İşlemi Devam Ediyor...</div>
                  {pendingFiles.map((file, idx) => (
                    <div key={idx} className="flex items-center gap-3 p-3 border rounded-lg bg-blue-50 mb-2">
                      <img src={URL.createObjectURL(file)} alt={file.name} className="w-16 h-16 object-cover rounded border" />
                      <div className="flex-1 min-w-0">
                        <div className="truncate font-medium text-gray-800 text-sm">{file.name}</div>
                        <div className="text-xs text-gray-500">{(file.size / 1024).toFixed(2)} KB</div>
                        <div className="text-xs text-blue-600 mt-1">Kırpma işlemini tamamlayın</div>
                      </div>
                      <button
                        className="p-2 hover:bg-red-100 rounded transition"
                        title="İptal"
                        type="button"
                        onClick={() => {
                          setPendingFiles([]);
                          setCropModalOpen(false);
                          setCropImageUrl(null);
                          setCropFileIndex(null);
                        }}
                      >
                        <span className="text-lg text-red-500">×</span>
                      </button>
                    </div>
                  ))}
                  {/* Crop modalı */}
                  {cropModalOpen && cropImageUrl && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
                      <div className="bg-white rounded-lg shadow-lg p-0 w-full max-w-4xl flex overflow-hidden">
                        {/* Sol Panel */}
                        <div className="w-80 p-6 border-r bg-gray-50 flex flex-col gap-4">
                          <div className="text-lg font-bold mb-2">Resim Düzenleyici</div>
                          <div className="text-xs text-gray-500 mb-2 truncate">{cropImageUrl.split('/').pop()}</div>
                          <div>
                            <label className="block text-xs font-medium mb-1">Hedef Boyutlar</label>
                            <div className="flex gap-2 mb-2">
                              <input type="number" className="w-20 border rounded px-2 py-1 text-sm" value={cropWidth} onChange={e => setCropWidth(Number(e.target.value))} />
                              <span className="text-gray-400">x</span>
                              <input type="number" className="w-20 border rounded px-2 py-1 text-sm" value={cropHeight} onChange={e => setCropHeight(Number(e.target.value))} />
                            </div>
                            <div className="flex gap-2 mb-2">
                              <input type="range" min={100} max={2000} value={cropWidth} onChange={e => setCropWidth(Number(e.target.value))} />
                              <input type="range" min={100} max={2000} value={cropHeight} onChange={e => setCropHeight(Number(e.target.value))} />
                            </div>
                            <div className="mb-2">
                              <label className="block text-xs font-medium mb-1">Hazır Boyutlar</label>
                              <select className="w-full border rounded px-2 py-1 text-sm" value={`${cropWidth}x${cropHeight}`} onChange={e => {
                                const [w, h] = e.target.value.split('x').map(Number);
                                setCropWidth(w); setCropHeight(h);
                              }}>
                                {readySizes.map(s => (
                                  <option key={s.label} value={`${s.width}x${s.height}`}>{s.label}</option>
                                ))}
                              </select>
                            </div>
                          </div>
                          <div>
                            <label className="block text-xs font-medium mb-1">Yakınlaştırma</label>
                            <input type="range" min={1} max={3} step={0.01} value={zoom} onChange={e => setZoom(Number(e.target.value))} />
                            <div className="text-xs text-gray-500">{zoom.toFixed(2)}x</div>
                          </div>
                          <div>
                            <label className="block text-xs font-medium mb-1">Alt Yazı (alt text)</label>
                            <input type="text" className="w-full border rounded px-2 py-1 text-sm" value={altText} onChange={e => setAltText(e.target.value)} placeholder="Açıklama girin..." />
                          </div>
                        </div>
                        {/* Orta Panel */}
                        <div className="flex-1 flex flex-col items-center justify-center bg-white p-6">
                          <div className="relative w-full h-[400px] bg-gray-100 rounded">
                            <Cropper
                              image={cropImageUrl}
                              crop={crop}
                              zoom={zoom}
                              aspect={cropWidth / cropHeight}
                              onCropChange={setCrop}
                              onZoomChange={setZoom}
                              onCropComplete={(_, croppedPixels) => setCroppedAreaPixels(croppedPixels)}
                            />
                          </div>
                          <div className="flex justify-end gap-2 mt-4 w-full">
                            <button
                              className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
                              onClick={() => setCropModalOpen(false)}
                            >
                              İptal
                            </button>
                            <button
                              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                              onClick={handleCropCompleteAndSave}
                            >
                              Kırp ve Kaydet
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Auto-upload after cropping - no manual upload button needed */}
                </div>
              )}
            </div>
          )}
        </div>
        {(localSelectedMedia || croppedFile) && (
          <div className="flex justify-end mt-6">
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              onClick={async () => {
                if (croppedFile) {
                  setUploading(true);
                  setError(null);
                  const formData = new FormData();
                  formData.append('file', croppedFile);
                  formData.append('folder', folder);
                  try {
                    const res = await fetch('/api/upload', {
                      method: 'POST',
                      body: formData,
                    });
                    const data = await res.json();
                    if (data && data.media) {
                      console.log('✅ Manual upload successful, refreshing gallery');

                      // Refresh gallery to ensure consistency
                      setTimeout(() => refreshGallery(), 100);

                      onSelect(data.media);
                      setCroppedFile(null);
                      setLocalSelectedMedia(data.media);
                      setOriginalImageBeingCropped(null);
                    } else {
                      setError(data.error || 'Yükleme başarısız.');
                    }
                  } catch (e) {
                    setError('Yükleme sırasında hata oluştu.');
                  } finally {
                    setUploading(false);
                    onClose();
                  }
                } else if (localSelectedMedia) {
                  onSelect(localSelectedMedia);
                  onClose();
                }
              }}
              disabled={uploading}
            >
              Proje görselini ata
            </button>
          </div>
        )}
        {/* Silme onay modalı */}
        {deleteDialogOpen && mediaToDelete && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
            <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full">
              <div className="font-bold text-lg mb-2">Görseli Sil</div>
              <div className="mb-4">Bu görseli silmek istediğinize emin misiniz?</div>
              <div className="flex gap-2 justify-end">
                <button
                  className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300"
                  onClick={() => setDeleteDialogOpen(false)}
                >
                  İptal
                </button>
                <button
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  onClick={handleDeleteMedia}
                  disabled={uploading}
                >
                  Sil
                </button>
              </div>
            </div>
          </div>
        )}
        {/* Alt kısımda iptal butonu, modal kutusunun içinde */}
        <div className="mt-8 flex justify-end">
          <button
            type="button"
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
            onClick={onClose}
          >
            İptal
          </button>
        </div>
      </div>
    </div>
  );
}; 