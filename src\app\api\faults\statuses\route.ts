import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const statuses = await prisma.arizaDurum.findMany({
      where: {
        silindi_mi: false,
      },
      orderBy: {
        sira: "asc",
      },
    })

    return NextResponse.json({
      statuses,
    })
  } catch (error) {
    console.error("Error fetching fault statuses:", error)
    return NextResponse.json(
      { message: "Durumlar yüklenirken hata olu<PERSON>tu" },
      { status: 500 }
    )
  }
} 