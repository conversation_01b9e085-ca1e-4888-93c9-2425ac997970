"use client"

import { useState, useEffect } from "react"
import { Calendar, Clock, User, X, Zap, CheckCircle, FileText } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AppointmentStatus, APPOINTMENT_STATUS_LABELS } from "@/lib/enums"
import { toast } from "sonner"
import { SmartTechnicianAssignment } from "./smart-technician-assignment"
import SmartAppointmentForm from "@/components/appointments/SmartAppointmentForm"

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
}

interface FaultData {
  id: string
  baslik: string
  tip: { ad: string; renk: string }
  aciliyet: { ad: string; renk: string }
  daire: {
    numara: string
    blok: {
      ad: string
      proje: { ad: string }
    }
  }
}

interface AddAppointmentDialogProps {
  faultId: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

const formSchema = z.object({
  randevu_tarihi: z.string().min(1, "Randevu tarihi gereklidir"),
  randevu_saati: z.string().min(1, "Randevu saati gereklidir"),
  durum: z.nativeEnum(AppointmentStatus),
  aciklama: z.string().optional(),
  teknisyen_ids: z.array(z.string()).min(1, "En az bir teknisyen seçmelisiniz"),
}).refine((data) => {
  // Saat kontrolü: 08:00 - 19:00 arası
  const time = data.randevu_saati;
  const [hour, minute] = time.split(':').map(Number);
  const totalMinutes = hour * 60 + minute;
  const startTime = 8 * 60; // 08:00
  const endTime = 19 * 60; // 19:00
  
  return totalMinutes >= startTime && totalMinutes <= endTime;
}, {
  message: "Randevu saati 08:00 - 19:00 arasında olmalıdır",
  path: ["randevu_saati"]
})

type FormData = z.infer<typeof formSchema>

export function AddAppointmentDialog({ 
  faultId, 
  open, 
  onOpenChange, 
  onSuccess 
}: AddAppointmentDialogProps) {
  // Load fault data
  useEffect(() => {
    const loadFaultData = async () => {
      if (!faultId || !open) return
      
      try {
        const response = await fetch(`/api/faults/${faultId}`)
        if (response.ok) {
          const data = await response.json()
          // setFault(data) // This state is no longer needed
        }
      } catch (error) {
        console.error("Failed to load fault data:", error)
      }
    }

    loadFaultData()
  }, [faultId, open])

  // Update form when selected technicians change
  // useEffect(() => {
  //   form.setValue("teknisyen_ids", selectedTechnicians)
  // }, [selectedTechnicians, form])

  // const handleTechnicianAssignment = (technicians: Technician[]) => {
  //   setSelectedTechnicianObjects(technicians)
  //   setSelectedTechnicians(technicians.map(t => t.id))
  // }

  // const onSubmit = async (data: FormData) => {
  //   if (selectedTechnicians.length === 0) {
  //     toast.error("En az bir teknisyen seçmelisiniz")
  //     return
  //   }

  //   try {
  //     setLoading(true)

  //     // Tarihi ve saati birleştir
  //     const combinedDateTime = `${data.randevu_tarihi}T${data.randevu_saati}:00`

  //     const response = await fetch("/api/appointments", {
  //       method: "POST",
  //       headers: {
  //         "Content-Type": "application/json",
  //       },
  //       body: JSON.stringify({
  //         ariza_id: faultId,
  //         randevu_tarihi: combinedDateTime,
  //         durum: data.durum,
  //         aciklama: data.aciklama || null,
  //         teknisyen_ids: data.teknisyen_ids,
  //       }),
  //     })

  //     if (!response.ok) {
  //       throw new Error("Failed to create appointment")
  //     }

  //     toast.success("🎉 Randevu başarıyla oluşturuldu!", {
  //       description: "Yeni randevu sisteme kaydedildi ve teknisyenler bilgilendirildi.",
  //       duration: 4000
  //     })

  //     // Reset form and state
  //     form.reset()
  //     setSelectedTechnicians([])
  //     setSelectedTechnicianObjects([])
  //     onSuccess()
  //   } catch (error) {
  //     console.error("Error creating appointment:", error)
  //     toast.error("Randevu oluşturulurken bir hata oluştu", {
  //       description: "Lütfen tekrar deneyin veya sistem yöneticisine başvurun.",
  //       duration: 6000
  //     })
  //   } finally {
  //     setLoading(false)
  //   }
  // }

  // const handleClose = () => {
  //   form.reset()
  //   setSelectedTechnicians([])
  //   setSelectedTechnicianObjects([])
  //   setUseSmartAssignment(true)
  //   onOpenChange(false)
  // }

  // Generate datetime input min value (current time)
  // const now = new Date()
  // const minDateTime = now.toISOString().slice(0, 16)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <SmartAppointmentForm ariza_id={faultId} onSuccess={onSuccess} onClose={() => onOpenChange(false)} />
      </DialogContent>
    </Dialog>
  )
}
