import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CacheManager, CACHE_TTL } from "@/lib/cache"

export async function GET() {
  try {
    // Try to get from cache first
    const cacheKey = 'api:technicians:all'
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for technicians: ${cacheKey}`)
      return NextResponse.json(cached)
    }
    
    console.log(`Cache miss for technicians: ${cacheKey}`)
    
    const technicians = await prisma.user.findMany({
      where: {
        rol: "TECHNICIAN",
        durum: "ACTIVE",
        silindi_mi: false,
      },
      include: {
        // Aktif randevu sayısını hesapla
        randevu_teknisyenleri: {
          where: {
            randevu: {
              durum: {
                in: ["PLANLI", "DEVAM_EDIYOR"]
              },
              silindi_mi: false,
            }
          },
          select: {
            id: true,
          },
        },
        // Uzmanlık alanlarını getir
        uzmanlik_alanlari: {
          select: {
            id: true,
            seviye: true,
            uzman<PERSON>_alani: {
              select: {
                id: true,
                ad: true,
                aciklama: true,
                renk: true,
              }
            }
          },
          orderBy: {
            seviye: "desc", // En yüksek seviyeden başla
          }
        },
      },
      orderBy: {
        ad: "asc",
      },
    })

    // Tip tanımı
    interface Technician {
      id: string;
      ad: string;
      soyad: string;
      email: string;
      telefon: string;
      resim: string | null;
      durum: string;
      randevu_teknisyenleri: { id: string }[];
      uzmanlik_alanlari: {
        seviye: string;
        uzmanlik_alani: {
          id: string;
          ad: string;
          aciklama: string | null;
          renk: string;
        };
      }[];
    }

    // Aktif randevu sayısını ve uzmanlık alanlarını ekle
    const techniciansWithTaskCount = (technicians as Technician[]).map((tech) => ({
      id: tech.id,
      ad: tech.ad,
      soyad: tech.soyad,
      email: tech.email,
      telefon: tech.telefon,
      resim: tech.resim,
      durum: tech.durum,
      aktifRandevuSayisi: tech.randevu_teknisyenleri.length,
      uzmanlikAlanlari: tech.uzmanlik_alanlari.map((ua) => ({
        id: ua.uzmanlik_alani.id,
        ad: ua.uzmanlik_alani.ad,
        aciklama: ua.uzmanlik_alani.aciklama,
        renk: ua.uzmanlik_alani.renk,
        seviye: ua.seviye,
      })),
    }))

    // Store in cache for 15 minutes
    await CacheManager.set(cacheKey, techniciansWithTaskCount, CACHE_TTL.TECHNICIANS)

    return NextResponse.json(techniciansWithTaskCount)
  } catch (error) {
    console.error("Teknisyenler yüklenirken hata:", error)
    return NextResponse.json(
      { message: "Teknisyenler yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

// Benzersiz email üretmek için yardımcı fonksiyon
function generateDummyEmail() {
  return `teknisyen-${Date.now()}-${Math.floor(Math.random()*100000)}@dummy.local`;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { ad, soyad, telefon, aktif = true, uzmanliklar = [] } = body;
    // Benzersiz dummy email üret
    const email = generateDummyEmail();
    // Yeni teknisyen oluştur
    const newTech = await prisma.user.create({
      data: {
        ad,
        soyad,
        telefon,
        email,
        rol: "TECHNICIAN",
        durum: aktif ? "ACTIVE" : "INACTIVE",
        silindi_mi: false,
        uzmanlik_alanlari: {
          create: uzmanliklar.map((uzmanlik: {id: string, seviye: string}) => ({
            uzmanlik_alani_id: uzmanlik.id,
            seviye: uzmanlik.seviye,
          })),
        },
      },
      include: {
        uzmanlik_alanlari: {
          include: { uzmanlik_alani: true },
        },
      },
    });
    
    // Invalidate technicians cache
    await CacheManager.delete('api:technicians:all')
    
    return NextResponse.json(newTech, { status: 201 });
  } catch (error) {
    console.error("Teknisyen eklenirken hata:", error);
    return NextResponse.json({ message: "Teknisyen eklenirken hata oluştu" }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { id, ad, soyad, telefon, aktif = true, uzmanliklar = [] } = body;
    // Teknisyen güncelle
    const updatedTech = await prisma.user.update({
      where: { id },
      data: {
        ad,
        soyad,
        telefon,
        durum: aktif ? "ACTIVE" : "INACTIVE",
        uzmanlik_alanlari: {
          deleteMany: {},
          create: uzmanliklar.map((uzmanlik: {id: string, seviye: string}) => ({
            uzmanlik_alani_id: uzmanlik.id,
            seviye: uzmanlik.seviye,
          })),
        },
      },
      include: {
        uzmanlik_alanlari: {
          include: { uzmanlik_alani: true },
        },
      },
    });
    
    // Invalidate technicians cache
    await CacheManager.delete('api:technicians:all')
    
    return NextResponse.json(updatedTech);
  } catch (error) {
    console.error("Teknisyen güncellenirken hata:", error);
    return NextResponse.json({ message: "Teknisyen güncellenirken hata oluştu" }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    // Soft delete
    await prisma.user.update({
      where: { id },
      data: { silindi_mi: true, durum: "INACTIVE" },
    });
    
    // Invalidate technicians cache
    await CacheManager.delete('api:technicians:all')
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Teknisyen silinirken hata:", error);
    return NextResponse.json({ message: "Teknisyen silinirken hata oluştu" }, { status: 500 });
  }
}
