// ===== APPLICATION CONSTANTS =====

export const APP_NAME = "Bakım Onarım Yönetim Sistemi"
export const APP_VERSION = "1.7.1"
export const APP_DESCRIPTION = "Profesyonel bakım onarım yönetim platformu"

// ===== API ENDPOINTS =====

export const API_ENDPOINTS = {
  // User Management
  USERS: "/api/users",
  USER_BY_ID: (id: string) => `/api/users/${id}`,
  
  // Authentication
  AUTH: {
    SIGNIN: "/api/auth/signin",
    SIGNUP: "/api/auth/register",
    SIGNOUT: "/api/auth/signout"
  },
  
  // Fault Management
  FAULTS: "/api/faults",
  FAULT_BY_ID: (id: string) => `/api/faults/${id}`,
  FAULT_STATS: "/api/faults/stats",
  FAULT_MONTHLY_STATS: "/api/faults/monthly-stats",
  FAULT_PRIORITIES: "/api/faults/priorities",
  FAULT_STATUSES: "/api/faults/statuses",
  
  // Appointment Management
  APPOINTMENTS: "/api/appointments",
  APPOINTMENT_BY_ID: (id: string) => `/api/appointments/${id}`,
  APPOINTMENT_RESULT: (id: string) => `/api/appointments/${id}/sonuc`,
  APPOINTMENT_MATERIALS: (id: string) => `/api/appointments/${id}/malzemeler`,
  APPOINTMENT_WORKS: (id: string) => `/api/appointments/${id}/islemler`,
  
  // Technician Management
  TECHNICIANS: "/api/technicians",
  TECHNICIAN_BY_ID: (id: string) => `/api/technicians/${id}`,
  TECHNICIAN_EXPERTISE: (id: string) => `/api/technicians/${id}/expertise`,
  
  // Project Management
  PROJECTS: "/api/projects",
  PROJECT_BY_ID: (id: string) => `/api/projects/${id}`,
  BLOCKS: "/api/blocks",
  BLOCK_BY_ID: (id: string) => `/api/blocks/${id}`,
  APARTMENTS: "/api/apartments",
  APARTMENT_BY_ID: (id: string) => `/api/apartments/${id}`,
  
  // Material Management
  MATERIALS: "/api/materials",
  MATERIAL_BY_ID: (id: string) => `/api/materials/${id}`,
  
  // Category Management
  CATEGORIES: "/api/categories",
  CATEGORY_BY_ID: (id: string) => `/api/categories/${id}`,
  
  // Priority Management
  PRIORITIES: "/api/priorities",
  PRIORITY_BY_ID: (id: string) => `/api/priorities/${id}`,
  
  // Status Management
  STATUSES: "/api/statuses",
  STATUS_BY_ID: (id: string) => `/api/statuses/${id}`,
  
  // Work Types
  WORK_TYPES: "/api/islem-turleri",
  
  // Expertise Areas
  EXPERTISE_AREAS: "/api/expertise-areas",
  
  // File Upload
  UPLOAD: "/api/upload"
} as const

// ===== PAGINATION DEFAULTS =====

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  DEFAULT_PAGE: 1
} as const

// ===== DATE FORMATS =====

export const DATE_FORMATS = {
  DISPLAY: "DD.MM.YYYY",
  DISPLAY_WITH_TIME: "DD.MM.YYYY HH:mm",
  API: "YYYY-MM-DD",
  ISO: "YYYY-MM-DDTHH:mm:ss.SSSZ"
} as const

// ===== ROLES & PERMISSIONS =====

export const ROLE_PERMISSIONS = {
  ADMIN: [
    'users:read', 'users:write', 'users:delete',
    'faults:read', 'faults:write', 'faults:delete',
    'appointments:read', 'appointments:write', 'appointments:delete',
    'technicians:read', 'technicians:write', 'technicians:delete',
    'projects:read', 'projects:write', 'projects:delete',
    'materials:read', 'materials:write', 'materials:delete',
    'reports:read', 'settings:read', 'settings:write'
  ],
  MANAGER: [
    'users:read', 'users:write',
    'faults:read', 'faults:write',
    'appointments:read', 'appointments:write',
    'technicians:read', 'technicians:write',
    'projects:read', 'projects:write',
    'materials:read', 'materials:write',
    'reports:read'
  ],
  TECHNICIAN: [
    'faults:read',
    'appointments:read', 'appointments:write',
    'materials:read'
  ],
  USER: [
    'faults:read', 'faults:write',
    'appointments:read'
  ]
} as const

// ===== STATUS MAPPINGS =====

export const STATUS_COLORS = {
  // User Status Colors
  USER_STATUS: {
    PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
    ACTIVE: "bg-green-100 text-green-800 border-green-200",
    INACTIVE: "bg-gray-100 text-gray-800 border-gray-200",
    SUSPENDED: "bg-red-100 text-red-800 border-red-200"
  },
  
  // Role Colors
  USER_ROLE: {
    ADMIN: "bg-purple-100 text-purple-800 border-purple-200",
    MANAGER: "bg-blue-100 text-blue-800 border-blue-200",
    TECHNICIAN: "bg-orange-100 text-orange-800 border-orange-200",
    USER: "bg-gray-100 text-gray-800 border-gray-200"
  },
  
  // Appointment Status Colors
  APPOINTMENT_STATUS: {
    PLANLI: "bg-blue-100 text-blue-800 border-blue-200",
    DEVAM_EDIYOR: "bg-orange-100 text-orange-800 border-orange-200",
    TAMAMLANDI: "bg-green-100 text-green-800 border-green-200",
    IPTAL: "bg-red-100 text-red-800 border-red-200"
  },
  
  // Work Status Colors
  WORK_STATUS: {
    PLANLI: "bg-blue-100 text-blue-800 border-blue-200",
    DEVAM_EDIYOR: "bg-orange-100 text-orange-800 border-orange-200",
    TAMAMLANDI: "bg-green-100 text-green-800 border-green-200",
    BEKLEMEDE: "bg-yellow-100 text-yellow-800 border-yellow-200",
    IPTAL: "bg-red-100 text-red-800 border-red-200"
  }
} as const

// ===== LABEL MAPPINGS =====

export const STATUS_LABELS = {
  // User Status Labels
  USER_STATUS: {
    PENDING: "Beklemede",
    ACTIVE: "Aktif",
    INACTIVE: "Pasif",
    SUSPENDED: "Askıda"
  },
  
  // Role Labels
  USER_ROLE: {
    ADMIN: "Yönetici",
    MANAGER: "Proje Müdürü",
    TECHNICIAN: "Teknisyen",
    USER: "Kullanıcı"
  },
  
  // Appointment Status Labels
  APPOINTMENT_STATUS: {
    PLANLI: "Planlandı",
    DEVAM_EDIYOR: "Devam Ediyor",
    TAMAMLANDI: "Tamamlandı",
    IPTAL: "İptal Edildi"
  },
  
  // Work Status Labels
  WORK_STATUS: {
    PLANLI: "Planlandı",
    DEVAM_EDIYOR: "Devam Ediyor",
    TAMAMLANDI: "Tamamlandı",
    BEKLEMEDE: "Beklemede",
    IPTAL: "İptal Edildi"
  },
  
  // Appointment Result Status Labels
  APPOINTMENT_RESULT_STATUS: {
    TAMAMEN_COZULDU: "Tamamen Çözüldü",
    KISMI_COZULDU: "Kısmen Çözüldü",
    COZULEMEDI: "Çözülemedi",
    ERTELENDI: "Ertelendi",
    IPTAL_EDILDI: "İptal Edildi"
  },
  
  // Expertise Level Labels
  EXPERTISE_LEVEL: {
    BASLANGIC: "Başlangıç",
    ORTA: "Orta",
    ILERI: "İleri",
    UZMAN: "Uzman"
  }
} as const

// ===== VALIDATION RULES =====

export const VALIDATION_RULES = {
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SYMBOLS: false
  },
  
  PHONE: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 15,
    PATTERN: /^(\+90|0)?5[0-9]{9}$/
  },
  
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  
  FILE_UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
    MAX_FILES: 5
  }
} as const

// ===== QUERY KEYS =====

export const QUERY_KEYS = {
  // User queries
  USERS: ['users'] as const,
  USER: (id: string) => ['users', id] as const,
  
  // Fault queries
  FAULTS: ['faults'] as const,
  FAULT: (id: string) => ['faults', id] as const,
  FAULT_STATS: ['faults', 'stats'] as const,
  FAULT_MONTHLY_STATS: ['faults', 'monthly-stats'] as const,
  
  // Appointment queries
  APPOINTMENTS: ['appointments'] as const,
  APPOINTMENT: (id: string) => ['appointments', id] as const,
  
  // Technician queries
  TECHNICIANS: ['technicians'] as const,
  TECHNICIAN: (id: string) => ['technicians', id] as const,
  
  // Project queries
  PROJECTS: ['projects'] as const,
  PROJECT: (id: string) => ['projects', id] as const,
  BLOCKS: ['blocks'] as const,
  APARTMENTS: ['apartments'] as const,
  
  // Material queries
  MATERIALS: ['materials'] as const,
  MATERIAL: (id: string) => ['materials', id] as const,
  
  // Category queries
  CATEGORIES: ['categories'] as const,
  CATEGORY: (id: string) => ['categories', id] as const,
  
  // Priority queries
  PRIORITIES: ['priorities'] as const,
  PRIORITY: (id: string) => ['priorities', id] as const,
  
  // Status queries
  STATUSES: ['statuses'] as const,
  STATUS: (id: string) => ['statuses', id] as const,
  
  // Other queries
  WORK_TYPES: ['work-types'] as const,
  EXPERTISE_AREAS: ['expertise-areas'] as const
} as const

// ===== ERROR MESSAGES =====

export const ERROR_MESSAGES = {
  REQUIRED_FIELD: "Bu alan zorunludur",
  INVALID_EMAIL: "Geçerli bir email adresi giriniz",
  INVALID_PHONE: "Geçerli bir telefon numarası giriniz",
  PASSWORD_TOO_SHORT: `Şifre en az ${VALIDATION_RULES.PASSWORD.MIN_LENGTH} karakter olmalıdır`,
  PASSWORD_TOO_LONG: `Şifre en fazla ${VALIDATION_RULES.PASSWORD.MAX_LENGTH} karakter olmalıdır`,
  PASSWORDS_DONT_MATCH: "Şifreler eşleşmiyor",
  FILE_TOO_LARGE: `Dosya boyutu ${VALIDATION_RULES.FILE_UPLOAD.MAX_SIZE / (1024 * 1024)}MB'den büyük olamaz`,
  INVALID_FILE_TYPE: "Desteklenmeyen dosya türü",
  TOO_MANY_FILES: `En fazla ${VALIDATION_RULES.FILE_UPLOAD.MAX_FILES} dosya yükleyebilirsiniz`,
  NETWORK_ERROR: "Ağ bağlantısı hatası",
  UNAUTHORIZED: "Bu işlem için yetkiniz bulunmuyor",
  NOT_FOUND: "Aradığınız kayıt bulunamadı",
  SERVER_ERROR: "Sunucu hatası oluştu, lütfen daha sonra tekrar deneyiniz"
} as const

// ===== SUCCESS MESSAGES =====

export const SUCCESS_MESSAGES = {
  CREATED: "Kayıt başarıyla oluşturuldu",
  UPDATED: "Kayıt başarıyla güncellendi",
  DELETED: "Kayıt başarıyla silindi",
  SAVED: "Değişiklikler kaydedildi",
  SENT: "Başarıyla gönderildi",
  UPLOADED: "Dosya başarıyla yüklendi"
} as const 