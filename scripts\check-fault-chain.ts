import { prisma } from "../src/lib/prisma";

async function main() {
  const projects = await prisma.proje.findMany({ where: { silindi_mi: false } });
  if (projects.length === 0) {
    console.log("Veritabanında hiç proje yok.");
    return;
  }

  for (const project of projects) {
    console.log("\nProje:", project.slug, '| id:', project.id);
    const blocks = await prisma.blok.findMany({ where: { proje_id: project.id, silindi_mi: false } });
    if (blocks.length === 0) {
      console.log(`  Projeye bağlı blok yok. (proje id: ${project.id}, slug: ${project.slug})`);
      continue;
    }
    for (const block of blocks) {
      console.log("  Blok:", block.slug, '| id:', block.id);
      const apartments = await prisma.daire.findMany({ where: { blok_id: block.id, silindi_mi: false } });
      if (apartments.length === 0) {
        console.log(`    Bloka bağlı daire yok. (blok id: ${block.id}, slug: ${block.slug})`);
        continue;
      }
      for (const apartment of apartments) {
        console.log("    Daire:", apartment.slug, '| id:', apartment.id);
        const faults = await prisma.ariza.findMany({ where: { daire_id: apartment.id, silindi_mi: false } });
        if (faults.length === 0) {
          console.log(`      Daireye bağlı arıza yok. (daire id: ${apartment.id}, slug: ${apartment.slug})`);
          continue;
        }
        for (const fault of faults) {
          console.log("      Arıza:", fault.slug, '| id:', fault.id);
        }
      }
    }
  }
}

main().then(() => prisma.$disconnect()); 