import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "sonner";
import ToastContainer from "@/components/ui/toast-container";
import { LoadingProvider } from "@/contexts/loading-context";
import { LoadingOverlay } from "@/components/ui/loading-overlay";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
      title: "Bakım Onarım v1.5.0 - Smart Appointment System",
  description: "Konut bakım ve onarım arıza takip sistemi - Teknisyen yönetimi ve atama sistemi eklendi",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="tr">
      <body className={`${inter.className} antialiased`}>
        <LoadingProvider>
          {children}
          <LoadingOverlay />
          <ToastContainer />
          <Toaster richColors position="top-right" />
        </LoadingProvider>
      </body>
    </html>
  );
}
