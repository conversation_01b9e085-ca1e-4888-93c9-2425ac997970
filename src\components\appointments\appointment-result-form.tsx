"use client"

import { useState, useEffect } from "react"
import { 
  CheckCircle2, 
  XCircle, 
  Clock, 
  AlertTriangle, 
  Star, 
  Save, 
  Edit,
  Loader2,
  FileText,
  DollarSign
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useToast } from "@/hooks/use-toast"
import { 
  AppointmentResultAction, 
  APPOINTMENT_RESULT_ACTION_LABELS, 
  APPOINTMENT_RESULT_ACTION_COLORS,
  APPOINTMENT_RESULT_ACTION_DESCRIPTIONS
} from "@/lib/enums"

interface AppointmentResult {
  id: string
  action?: string
  teknisyen_notlari: string | null
  musteri_memnuniyet: number | null
  musteri_yorumu: string | null
  karsilasilan_zorluklar: string | null
  resimler: string[]
  gercek_baslangic: string | null
  gercek_bitis: string | null
  toplam_sure_dk: number | null
  iscilik_maliyeti: number
  olusturulma_tarihi: string
  guncelleme_tarihi: string
}

interface AppointmentResultFormProps {
  appointmentId: string
  onResultSaved?: (shouldCreateNewAppointment?: boolean) => void
}

const formSchema = z.object({
  action: z.nativeEnum(AppointmentResultAction),
  teknisyen_notlari: z.string().min(1, "Teknisyen notları gereklidir"),
  musteri_memnuniyet: z.number().min(1).max(5).optional(),
  musteri_yorumu: z.string().optional(),
  karsilasilan_zorluklar: z.string().optional(),
  gercek_baslangic: z.string().optional(),
  gercek_bitis: z.string().optional(),
  iscilik_maliyeti: z.number().min(0),
})

type FormData = z.infer<typeof formSchema>

export function AppointmentResultForm({ appointmentId, onResultSaved }: AppointmentResultFormProps) {
  const [result, setResult] = useState<AppointmentResult | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const { addToast } = useToast()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      action: AppointmentResultAction.FAULT_RESOLVED,
      teknisyen_notlari: "",
      musteri_memnuniyet: 5,
      musteri_yorumu: "",
      karsilasilan_zorluklar: "",
      gercek_baslangic: "",
      gercek_bitis: "",
      iscilik_maliyeti: 0,
    },
  })

  // Sonucu yükle
  const loadResult = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/appointments/${appointmentId}/sonuc`)
      
      if (response.ok) {
        const data = await response.json()
        setResult(data)
        
        // Form verilerini doldur
        form.reset({
          action: data.action || AppointmentResultAction.FAULT_RESOLVED,
          teknisyen_notlari: data.teknisyen_notlari || "",
          musteri_memnuniyet: data.musteri_memnuniyet || 5,
          musteri_yorumu: data.musteri_yorumu || "",
          karsilasilan_zorluklar: data.karsilasilan_zorluklar || "",
          gercek_baslangic: data.gercek_baslangic ? 
            new Date(data.gercek_baslangic).toISOString().slice(0, 16) : "",
          gercek_bitis: data.gercek_bitis ? 
            new Date(data.gercek_bitis).toISOString().slice(0, 16) : "",
          iscilik_maliyeti: data.iscilik_maliyeti || 0,
        }, {
          keepDefaultValues: true // Önemli: Varsayılan değerleri koru
        })
      } else if (response.status !== 404) {
        // 404 is normal (no result exists yet), only throw for other errors
        throw new Error("Sonuç bilgileri yüklenirken hata oluştu")
      }
      // If 404, just continue - no result exists yet, which is normal
    } catch (error) {
      // Only log non-404 errors
      if (error instanceof Error && !error.message.includes('404')) {
        console.error("Error loading result:", error)
        addToast({
          title: "Hata", 
          message: error.message,
          type: "error",
        })
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isEditDialogOpen) {
      loadResult()
    }
  }, [appointmentId, isEditDialogOpen])

  // Form açıldığında form state'ini sıfırla
  useEffect(() => {
    if (isEditDialogOpen) {
      form.reset(form.getValues())
    }
  }, [isEditDialogOpen])

  // Form gönderimi
  const onSubmit = async (data: FormData) => {
    try {
      setSubmitting(true)
      
      const response = await fetch(`/api/appointments/${appointmentId}/sonuc`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          gercek_baslangic: data.gercek_baslangic || null,
          gercek_bitis: data.gercek_bitis || null,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Sonuç kaydedilemedi")
      }

      const resultData = await response.json()
      setResult(resultData)

      addToast({
        title: "Başarılı",
        message: "Randevu sonucu başarıyla kaydedildi",
        type: "success",
      })

      setIsEditDialogOpen(false)
      
      // Yeni randevu gerekli mi?
      onResultSaved?.(resultData.shouldCreateNewAppointment)
    } catch (error) {
      console.error("Error submitting result:", error)
      addToast({
        title: "Hata",
        message: error instanceof Error ? error.message : "Sonuç kaydedilirken bir hata oluştu",
        type: "error",
      })
    } finally {
      setSubmitting(false)
    }
  }

  const formatDateTime = (dateTimeString: string | null) => {
    if (!dateTimeString) return "Belirtilmemiş"
    return new Date(dateTimeString).toLocaleString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatDuration = (minutes: number | null) => {
    if (!minutes) return null
    
    if (minutes < 60) {
      return `${minutes} dakika`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return `${hours} saat${remainingMinutes > 0 ? ` ${remainingMinutes} dakika` : ""}`
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ))
  }

  const getActionBadge = (action: string) => {
    const actionKey = action as keyof typeof APPOINTMENT_RESULT_ACTION_LABELS
    return (
      <Badge 
        style={{ 
          backgroundColor: APPOINTMENT_RESULT_ACTION_COLORS[actionKey] + "20",
          borderColor: APPOINTMENT_RESULT_ACTION_COLORS[actionKey],
          color: APPOINTMENT_RESULT_ACTION_COLORS[actionKey]
        }}
      >
        {APPOINTMENT_RESULT_ACTION_LABELS[actionKey]}
      </Badge>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Randevu Sonucu
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Randevu Sonucu
            </CardTitle>
            <Button
              onClick={() => setIsEditDialogOpen(true)}
              variant={result ? "outline" : "default"}
            >
              {result ? (
                <>
                  <Edit className="mr-2 h-4 w-4" />
                  Düzenle
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Sonuç Gir
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {!result ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>Henüz randevu sonucu girilmemiş</p>
              <p className="text-sm">Randevu tamamlandıktan sonra sonuçları kaydetmek için yukarıdaki butona tıklayın</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Ana Aksiyon */}
              <div className="flex items-center gap-3">
                {result.action && getActionBadge(result.action)}
              </div>

              {/* Zaman Bilgileri */}
              {(result.gercek_baslangic || result.gercek_bitis) && (
                <div className="bg-muted/50 p-4 rounded-lg">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Zaman Bilgileri
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Başlangıç</p>
                      <p className="font-medium">{formatDateTime(result.gercek_baslangic)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Bitiş</p>
                      <p className="font-medium">{formatDateTime(result.gercek_bitis)}</p>
                    </div>
                    {result.toplam_sure_dk && (
                      <div>
                        <p className="text-muted-foreground">Toplam Süre</p>
                        <p className="font-medium">{formatDuration(result.toplam_sure_dk)}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* İşçilik Maliyeti */}
              {result.iscilik_maliyeti > 0 && (
                <div className="bg-muted/50 p-4 rounded-lg">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    İşçilik Maliyeti
                  </h4>
                  <p className="font-semibold text-lg">{formatCurrency(result.iscilik_maliyeti)}</p>
                </div>
              )}

              {/* Teknisyen Notları */}
              {result.teknisyen_notlari && (
                <div>
                  <h4 className="font-medium mb-2">Teknisyen Notları</h4>
                  <p className="text-sm text-muted-foreground whitespace-pre-wrap bg-muted/50 p-3 rounded">
                    {result.teknisyen_notlari}
                  </p>
                </div>
              )}

              {/* Karşılaşılan Zorluklar */}
              {result.karsilasilan_zorluklar && (
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    Karşılaşılan Zorluklar
                  </h4>
                  <p className="text-sm text-muted-foreground whitespace-pre-wrap bg-yellow-50 p-3 rounded border border-yellow-200">
                    {result.karsilasilan_zorluklar}
                  </p>
                </div>
              )}

              {/* Müşteri Memnuniyeti */}
              {result.musteri_memnuniyet && (
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-400" />
                    Müşteri Memnuniyeti
                  </h4>
                  <div className="flex items-center gap-2">
                    {renderStars(result.musteri_memnuniyet)}
                    <span className="text-sm text-muted-foreground">
                      {result.musteri_memnuniyet}/5
                    </span>
                  </div>
                  {result.musteri_yorumu && (
                    <p className="text-sm text-muted-foreground mt-2 bg-muted/50 p-3 rounded">
                      &quot;{result.musteri_yorumu}&quot;
                    </p>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sonuç Düzenleme Dialog'u */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {result ? <Edit className="h-5 w-5" /> : <Save className="h-5 w-5" />}
              {result ? "Randevu Sonucunu Düzenle" : "Randevu Sonucunu Gir"}
            </DialogTitle>
            <DialogDescription>
              Randevu tamamlandıktan sonra sonuçları kaydedin. İş akışı seçiminize göre otomatik aksiyonlar alınacaktır.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              
              {/* Ana Aksiyon Seçimi */}
              <FormField
                control={form.control}
                name="action"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-semibold">
                      🎯 Ne Yapılacak? *
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="text-lg p-4">
                          <SelectValue placeholder="Aksiyon seçin" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(APPOINTMENT_RESULT_ACTION_LABELS).map(([value, label]) => (
                          <SelectItem key={value} value={value} className="py-3">
                            <div className="flex flex-col gap-1">
                              <span className="font-medium">{label}</span>
                              <span className="text-xs text-muted-foreground">
                                {APPOINTMENT_RESULT_ACTION_DESCRIPTIONS[value as keyof typeof APPOINTMENT_RESULT_ACTION_DESCRIPTIONS]}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Teknisyen Notları */}
              <FormField
                control={form.control}
                name="teknisyen_notlari"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Teknisyen Notları *</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Yapılan işlemler, bulgular ve sonuçlar..."
                        className="resize-none"
                        rows={4}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Zaman Bilgileri */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="gercek_baslangic"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gerçek Başlangıç Saati</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gercek_bitis"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gerçek Bitiş Saati</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* İşçilik Maliyeti */}
              <FormField
                control={form.control}
                name="iscilik_maliyeti"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>İşçilik Maliyeti (₺)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        min="0"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Opsiyonel Bilgiler */}
              <div className="space-y-4 border-t pt-4">
                <h3 className="font-medium text-muted-foreground">Opsiyonel Bilgiler</h3>
                
                <FormField
                  control={form.control}
                  name="karsilasilan_zorluklar"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Karşılaşılan Zorluklar</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Karşılaşılan problemler, engeller..."
                          className="resize-none"
                          rows={3}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="musteri_memnuniyet"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Müşteri Memnuniyeti (1-5)</FormLabel>
                        <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Puan seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {[1, 2, 3, 4, 5].map((rating) => (
                              <SelectItem key={rating} value={rating.toString()}>
                                <div className="flex items-center gap-2">
                                  {renderStars(rating)}
                                  <span>{rating}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="musteri_yorumu"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Müşteri Yorumu</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Müşterinin yorumları ve geri bildirimleri..."
                          className="resize-none"
                          rows={3}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditDialogOpen(false)}
                  disabled={submitting}
                >
                  İptal
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Kaydediliyor...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Kaydet
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
