"use strict";
/**
 * Merkezi Enum Yönetim <PERSON>
 * Tüm enum değerleri ve etiketleri bu dosyadan yönetilir
 */
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u;
Object.defineProperty(exports, "__esModule", { value: true });
exports.priorityColors = exports.statusColors = exports.priorityLabels = exports.statusLabels = exports.EXPERTISE_LEVEL_DESCRIPTIONS = exports.EXPERTISE_LEVEL_COLORS = exports.EXPERTISE_LEVEL_LABELS = exports.ExpertiseLevel = exports.APPOINTMENT_RESULT_ACTION_DESCRIPTIONS = exports.APPOINTMENT_RESULT_ACTION_COLORS = exports.APPOINTMENT_RESULT_ACTION_LABELS = exports.AppointmentResultAction = exports.APPOINTMENT_STATUS_DESCRIPTIONS = exports.APPOINTMENT_STATUS_COLORS = exports.APPOINTMENT_STATUS_LABELS = exports.AppointmentStatus = exports.PROJECT_STATUS_LABELS = exports.ProjectStatus = exports.APARTMENT_TYPE_LABELS = exports.ApartmentType = exports.USER_STATUS_LABELS = exports.UserStatus = exports.USER_ROLE_LABELS = exports.UserRole = exports.PRIORITY_LEVELS = exports.PRIORITY_COLORS = exports.PRIORITY_LABELS = exports.Priority = exports.FAULT_STATUS_DESCRIPTIONS = exports.FAULT_STATUS_COLORS = exports.FAULT_STATUS_LABELS = exports.FaultStatus = void 0;
exports.getEnumLabel = getEnumLabel;
exports.getLabelEnum = getLabelEnum;
exports.getEnumValues = getEnumValues;
exports.getEnumLabels = getEnumLabels;
exports.enumToSelectOptions = enumToSelectOptions;
// ===== ARIZA DURUMLARI =====
var FaultStatus;
(function (FaultStatus) {
    FaultStatus["ACIK"] = "ACIK";
    FaultStatus["DEVAM_EDIYOR"] = "DEVAM_EDIYOR";
    FaultStatus["BEKLEMEDE"] = "BEKLEMEDE";
    FaultStatus["COZULDU"] = "COZULDU";
    FaultStatus["IPTAL"] = "IPTAL";
})(FaultStatus || (exports.FaultStatus = FaultStatus = {}));
exports.FAULT_STATUS_LABELS = (_a = {},
    _a[FaultStatus.ACIK] = "Açık",
    _a[FaultStatus.DEVAM_EDIYOR] = "Devam Ediyor",
    _a[FaultStatus.BEKLEMEDE] = "Beklemede",
    _a[FaultStatus.COZULDU] = "Çözüldü",
    _a[FaultStatus.IPTAL] = "İptal",
    _a);
exports.statusLabels = exports.FAULT_STATUS_LABELS;
exports.FAULT_STATUS_COLORS = (_b = {},
    _b[FaultStatus.ACIK] = "bg-red-100 text-red-800 border-red-200",
    _b[FaultStatus.DEVAM_EDIYOR] = "bg-blue-100 text-blue-800 border-blue-200",
    _b[FaultStatus.BEKLEMEDE] = "bg-yellow-100 text-yellow-800 border-yellow-200",
    _b[FaultStatus.COZULDU] = "bg-green-100 text-green-800 border-green-200",
    _b[FaultStatus.IPTAL] = "bg-gray-100 text-gray-800 border-gray-200",
    _b);
exports.statusColors = exports.FAULT_STATUS_COLORS;
exports.FAULT_STATUS_DESCRIPTIONS = (_c = {},
    _c[FaultStatus.ACIK] = "Yeni bildirilen, henüz işleme alınmamış arızalar",
    _c[FaultStatus.DEVAM_EDIYOR] = "Üzerinde aktif olarak çalışılan arızalar",
    _c[FaultStatus.BEKLEMEDE] = "Malzeme, onay veya dış faktör bekleyen arızalar",
    _c[FaultStatus.COZULDU] = "Tamamen çözülmüş ve kapatılmış arızalar",
    _c[FaultStatus.IPTAL] = "İptal edilmiş veya geçersiz arızalar",
    _c);
// ===== ACİLİYET SEVİYELERİ =====
var Priority;
(function (Priority) {
    Priority["DUSUK"] = "DUSUK";
    Priority["ORTA"] = "ORTA";
    Priority["YUKSEK"] = "YUKSEK";
    Priority["KRITIK"] = "KRITIK";
})(Priority || (exports.Priority = Priority = {}));
exports.PRIORITY_LABELS = (_d = {},
    _d[Priority.DUSUK] = "Düşük",
    _d[Priority.ORTA] = "Orta",
    _d[Priority.YUKSEK] = "Yüksek",
    _d[Priority.KRITIK] = "Kritik",
    _d);
exports.priorityLabels = exports.PRIORITY_LABELS;
exports.PRIORITY_COLORS = (_e = {},
    _e[Priority.DUSUK] = "bg-green-100 text-green-800 border-green-200",
    _e[Priority.ORTA] = "bg-yellow-100 text-yellow-800 border-yellow-200",
    _e[Priority.YUKSEK] = "bg-orange-100 text-orange-800 border-orange-200",
    _e[Priority.KRITIK] = "bg-red-100 text-red-800 border-red-200",
    _e);
exports.priorityColors = exports.PRIORITY_COLORS;
exports.PRIORITY_LEVELS = (_f = {},
    _f[Priority.DUSUK] = 1,
    _f[Priority.ORTA] = 2,
    _f[Priority.YUKSEK] = 3,
    _f[Priority.KRITIK] = 4,
    _f);
// ===== KULLANICI ROLLERİ =====
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["MANAGER"] = "MANAGER";
    UserRole["TECHNICIAN"] = "TECHNICIAN";
    UserRole["USER"] = "USER";
})(UserRole || (exports.UserRole = UserRole = {}));
exports.USER_ROLE_LABELS = (_g = {},
    _g[UserRole.ADMIN] = "Yönetici",
    _g[UserRole.MANAGER] = "Yönetici",
    _g[UserRole.TECHNICIAN] = "Teknisyen",
    _g[UserRole.USER] = "Kullanıcı",
    _g);
// ===== KULLANICI DURUMLARI =====
var UserStatus;
(function (UserStatus) {
    UserStatus["PENDING"] = "PENDING";
    UserStatus["ACTIVE"] = "ACTIVE";
    UserStatus["INACTIVE"] = "INACTIVE";
    UserStatus["BLOCKED"] = "BLOCKED";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
exports.USER_STATUS_LABELS = (_h = {},
    _h[UserStatus.PENDING] = "Bekliyor",
    _h[UserStatus.ACTIVE] = "Aktif",
    _h[UserStatus.INACTIVE] = "Pasif",
    _h[UserStatus.BLOCKED] = "Bloklu",
    _h);
// ===== DAİRE TİPLERİ =====
var ApartmentType;
(function (ApartmentType) {
    ApartmentType["BIR_ARTI_SIFIR"] = "BIR_ARTI_SIFIR";
    ApartmentType["BIR_ARTI_BIR"] = "BIR_ARTI_BIR";
    ApartmentType["IKI_ARTI_BIR"] = "IKI_ARTI_BIR";
    ApartmentType["UC_ARTI_BIR"] = "UC_ARTI_BIR";
    ApartmentType["DORT_ARTI_BIR"] = "DORT_ARTI_BIR";
    ApartmentType["DUBLEKS"] = "DUBLEKS";
    ApartmentType["PENTHOUSE"] = "PENTHOUSE";
})(ApartmentType || (exports.ApartmentType = ApartmentType = {}));
exports.APARTMENT_TYPE_LABELS = (_j = {},
    _j[ApartmentType.BIR_ARTI_SIFIR] = "1+0",
    _j[ApartmentType.BIR_ARTI_BIR] = "1+1",
    _j[ApartmentType.IKI_ARTI_BIR] = "2+1",
    _j[ApartmentType.UC_ARTI_BIR] = "3+1",
    _j[ApartmentType.DORT_ARTI_BIR] = "4+1",
    _j[ApartmentType.DUBLEKS] = "Dubleks",
    _j[ApartmentType.PENTHOUSE] = "Penthouse",
    _j);
// ===== PROJE DURUMLARI =====
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["AKTIF"] = "AKTIF";
    ProjectStatus["TAMAMLANDI"] = "TAMAMLANDI";
    ProjectStatus["IPTAL"] = "IPTAL";
    ProjectStatus["BEKLEMEDE"] = "BEKLEMEDE";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
exports.PROJECT_STATUS_LABELS = (_k = {},
    _k[ProjectStatus.AKTIF] = "Aktif",
    _k[ProjectStatus.TAMAMLANDI] = "Tamamlandı",
    _k[ProjectStatus.IPTAL] = "İptal",
    _k[ProjectStatus.BEKLEMEDE] = "Beklemede",
    _k);
// ===== RANDEVU DURUMLARI =====
var AppointmentStatus;
(function (AppointmentStatus) {
    AppointmentStatus["PLANLI"] = "PLANLI";
    AppointmentStatus["DEVAM_EDIYOR"] = "DEVAM_EDIYOR";
    AppointmentStatus["TAMAMLANDI"] = "TAMAMLANDI";
    AppointmentStatus["IPTAL"] = "IPTAL";
})(AppointmentStatus || (exports.AppointmentStatus = AppointmentStatus = {}));
exports.APPOINTMENT_STATUS_LABELS = (_l = {},
    _l[AppointmentStatus.PLANLI] = "Planlı",
    _l[AppointmentStatus.DEVAM_EDIYOR] = "Devam Ediyor",
    _l[AppointmentStatus.TAMAMLANDI] = "Tamamlandı",
    _l[AppointmentStatus.IPTAL] = "İptal",
    _l);
exports.APPOINTMENT_STATUS_COLORS = (_m = {},
    _m[AppointmentStatus.PLANLI] = "bg-blue-100 text-blue-800 border-blue-200",
    _m[AppointmentStatus.DEVAM_EDIYOR] = "bg-orange-100 text-orange-800 border-orange-200",
    _m[AppointmentStatus.TAMAMLANDI] = "bg-green-100 text-green-800 border-green-200",
    _m[AppointmentStatus.IPTAL] = "bg-gray-100 text-gray-800 border-gray-200",
    _m);
exports.APPOINTMENT_STATUS_DESCRIPTIONS = (_o = {},
    _o[AppointmentStatus.PLANLI] = "Planlanmış, henüz başlamamış randevular",
    _o[AppointmentStatus.DEVAM_EDIYOR] = "Devam eden, aktif randevular",
    _o[AppointmentStatus.TAMAMLANDI] = "Başarıyla tamamlanmış randevular",
    _o[AppointmentStatus.IPTAL] = "İptal edilmiş randevular",
    _o);
// ===== RANDEVU SONUÇ AKSİYONLARI =====
var AppointmentResultAction;
(function (AppointmentResultAction) {
    AppointmentResultAction["FAULT_RESOLVED"] = "FAULT_RESOLVED";
    AppointmentResultAction["CANCELLED"] = "CANCELLED";
    AppointmentResultAction["NEXT_APPOINTMENT_REQUIRED"] = "NEXT_APPOINTMENT_REQUIRED";
})(AppointmentResultAction || (exports.AppointmentResultAction = AppointmentResultAction = {}));
exports.APPOINTMENT_RESULT_ACTION_LABELS = (_p = {},
    _p[AppointmentResultAction.FAULT_RESOLVED] = "Arıza Çözüldü (Kapatılsın)",
    _p[AppointmentResultAction.CANCELLED] = "İptal Edildi",
    _p[AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED] = "Sonraki Randevu Gerekli",
    _p);
exports.APPOINTMENT_RESULT_ACTION_COLORS = (_q = {},
    _q[AppointmentResultAction.FAULT_RESOLVED] = "#10B981",
    _q[AppointmentResultAction.CANCELLED] = "#6B7280",
    _q[AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED] = "#3B82F6",
    _q);
exports.APPOINTMENT_RESULT_ACTION_DESCRIPTIONS = (_r = {},
    _r[AppointmentResultAction.FAULT_RESOLVED] = "Arıza tamamen çözüldü. Hem randevu hem arıza kapatılacak.",
    _r[AppointmentResultAction.CANCELLED] = "Randevu iptal edildi. Arıza da kapatılacak.",
    _r[AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED] = "Bu randevu tamamlandı ama arıza devam ediyor. Yeni randevu gerekli.",
    _r);
// ===== UZMANLIK SEVİYELERİ =====
var ExpertiseLevel;
(function (ExpertiseLevel) {
    ExpertiseLevel["BASLANGIC"] = "BASLANGIC";
    ExpertiseLevel["ORTA"] = "ORTA";
    ExpertiseLevel["ILERI"] = "ILERI";
    ExpertiseLevel["UZMAN"] = "UZMAN";
})(ExpertiseLevel || (exports.ExpertiseLevel = ExpertiseLevel = {}));
exports.EXPERTISE_LEVEL_LABELS = (_s = {},
    _s[ExpertiseLevel.BASLANGIC] = "Başlangıç",
    _s[ExpertiseLevel.ORTA] = "Orta",
    _s[ExpertiseLevel.ILERI] = "İleri",
    _s[ExpertiseLevel.UZMAN] = "Uzman",
    _s);
exports.EXPERTISE_LEVEL_COLORS = (_t = {},
    _t[ExpertiseLevel.BASLANGIC] = "#10B981",
    _t[ExpertiseLevel.ORTA] = "#3B82F6",
    _t[ExpertiseLevel.ILERI] = "#F59E0B",
    _t[ExpertiseLevel.UZMAN] = "#DC2626",
    _t);
exports.EXPERTISE_LEVEL_DESCRIPTIONS = (_u = {},
    _u[ExpertiseLevel.BASLANGIC] = "Temel seviye bilgi ve deneyim",
    _u[ExpertiseLevel.ORTA] = "Orta seviye bilgi ve deneyim",
    _u[ExpertiseLevel.ILERI] = "İleri seviye bilgi ve deneyim",
    _u[ExpertiseLevel.UZMAN] = "Uzman seviye bilgi ve deneyim",
    _u);
// ===== HELPER FONKSİYONLARI =====
/**
 * Enum değerini etiketine çevirir
 */
function getEnumLabel(enumValue, labelMap) {
    return labelMap[enumValue] || enumValue;
}
/**
 * Etiketi enum değerine çevirir
 */
function getLabelEnum(label, labelMap) {
    return Object.keys(labelMap).find(function (key) { return labelMap[key] === label; });
}
/**
 * Enum değerlerinin listesini döndürür
 */
function getEnumValues(enumObj) {
    return Object.values(enumObj);
}
/**
 * Enum etiketlerinin listesini döndürür
 */
function getEnumLabels(labelMap) {
    return Object.values(labelMap);
}
/**
 * Select options için enum'ları çevirir
 */
function enumToSelectOptions(enumObj, labelMap) {
    return Object.values(enumObj).map(function (value) { return ({
        value: value,
        label: labelMap[value] || value
    }); });
}
