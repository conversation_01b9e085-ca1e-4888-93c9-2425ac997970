import React, { useState } from "react";
import { GenericMediaSelector, GenericMediaFile, createMediaSelectorConfig } from "@/components/media/GenericMediaSelector";
import { Button } from "@/components/ui/button";
import { Image } from "lucide-react";

// Proje görselleri için <PERSON> config
const projectMediaConfig = createMediaSelectorConfig([
  {
    id: 'project-images',
    key: 'project-images',
    name: '<PERSON><PERSON>',
    title: '<PERSON>je <PERSON>rse<PERSON>',
    description: 'Projeye ait görsel seçin veya yükleyin',
    acceptedTypes: ['image/*'],
    folder: 'images/projeler', // public/images/projeler
    maxSize: 5 * 1024 * 1024, // 5MB
    targetDimensions: { width: 1200, height: 800 }
  }
], {
  defaultCategory: 'project-images',
  showPreview: true,
  translations: {
    selectButton: 'Proje Görse<PERSON>',
    selectTitle: 'Proje Medya Yöneticisi',
    selectDescription: '<PERSON><PERSON>ye ait görsel seçin veya yükleyin'
  }
});

export interface ProjectMediaSelectorProps {
  value?: GenericMediaFile | null;
  onChange: (media: GenericMediaFile | null) => void;
  buttonText?: string;
  disabled?: boolean;
}

export const ProjectMediaSelector: React.FC<ProjectMediaSelectorProps> = ({
  value,
  onChange,
  buttonText = "Proje Görseli Seç",
  disabled = false,
}) => {
  return (
    <GenericMediaSelector
      onSelect={onChange}
      selectedMedia={value}
      categoryKey="project-images"
      config={projectMediaConfig}
      buttonText={buttonText}
      showPreview={true}
      disabled={disabled}
    />
  );
}; 