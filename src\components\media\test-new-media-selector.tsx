"use client"

import React, { useState } from 'react';
import { NewMediaSelector, MediaFile } from './NewMediaSelector';

export default function TestNewMediaSelector() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile | undefined>(undefined);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
    setTestResults(prev => [...prev, `[${timestamp}] ${emoji} ${message}`]);
  };

  const handleSelect = (media: MediaFile | undefined) => {
    setSelectedMedia(media);
    addResult(`Media selected: ${media ? media.originalName : 'None'}`, 'success');
  };

  const handleClose = () => {
    setIsOpen(false);
    addResult('Modal closed');
  };

  const handleOpen = () => {
    setIsOpen(true);
    addResult('Modal opened');
  };

  const runDuplicationTest = () => {
    addResult('🧪 Starting duplication test...', 'info');
    addResult('1. Upload an image via Upload tab', 'info');
    addResult('2. Complete cropping process', 'info');
    addResult('3. Close and reopen modal multiple times', 'info');
    addResult('4. Check Gallery tab for duplicates', 'info');
    addResult('Expected: NO duplicates should appear', 'success');
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">🆕 New Media Selector - Zero Duplicates</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Controls */}
        <div className="space-y-6">
          <div className="border rounded-lg p-6 bg-green-50 border-green-200">
            <h2 className="text-xl font-semibold mb-4 text-green-800">🎯 New Architecture</h2>
            <ul className="space-y-2 text-sm text-green-700">
              <li>✅ <strong>Reducer Pattern:</strong> Single source of truth</li>
              <li>✅ <strong>Immutable State:</strong> No direct mutations</li>
              <li>✅ <strong>Action-Based:</strong> All changes via dispatch</li>
              <li>✅ <strong>Built-in Deduplication:</strong> Prevents duplicates at reducer level</li>
              <li>✅ <strong>No Race Conditions:</strong> Unidirectional data flow</li>
            </ul>
          </div>

          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
            <div className="space-y-3">
              <button
                onClick={handleOpen}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition w-full"
              >
                Open New Media Selector
              </button>
              
              <button
                onClick={runDuplicationTest}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition w-full"
              >
                Run Duplication Test
              </button>
              
              <button
                onClick={clearResults}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition w-full"
              >
                Clear Results
              </button>
            </div>
          </div>

          <div className="border rounded-lg p-6 bg-blue-50">
            <h3 className="text-lg font-semibold mb-3">🧪 Comprehensive Test Cases:</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li><strong>Auto-Crop Upload:</strong> Upload tab → select file → crop → verify single instance</li>
              <li><strong>Drag & Drop:</strong> Drag file to upload area → crop → verify no duplicates</li>
              <li><strong>Modal Reopen:</strong> Close and reopen modal 5+ times → verify consistency</li>
              <li><strong>Tab Switching:</strong> Switch between Gallery/Upload tabs → verify state</li>
              <li><strong>Multiple Uploads:</strong> Upload several images → verify all unique</li>
              <li><strong>Delete & Re-upload:</strong> Delete image → upload same image → verify single instance</li>
            </ol>
          </div>

          <div className="border rounded-lg p-6 bg-yellow-50">
            <h3 className="text-lg font-semibold mb-3">🔍 Debug Console:</h3>
            <p className="text-sm mb-2">
              Open browser console (F12) to see reducer actions:
            </p>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>🔄 REDUCER: Action dispatches</li>
              <li>📊 REDUCER: Duplicate prevention</li>
              <li>✅ REDUCER: State updates</li>
              <li>🚫 REDUCER: Blocked duplicates</li>
            </ul>
          </div>
        </div>

        {/* Results */}
        <div className="space-y-6">
          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Selected Media</h2>
            {selectedMedia ? (
              <div className="flex items-center gap-4">
                <img 
                  src={selectedMedia.url} 
                  alt={selectedMedia.originalName} 
                  className="w-24 h-24 object-cover rounded border"
                />
                <div>
                  <div className="font-medium">{selectedMedia.originalName}</div>
                  <div className="text-sm text-gray-500">{(selectedMedia.size / 1024).toFixed(2)} KB</div>
                  <div className="text-xs text-blue-600 break-all">{selectedMedia.url}</div>
                </div>
              </div>
            ) : (
              <div className="text-gray-500">No media selected</div>
            )}
          </div>

          <div className="border rounded-lg p-6 bg-gray-50">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="bg-black text-green-400 p-4 rounded font-mono text-xs max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <div className="text-gray-500">No test results yet...</div>
              ) : (
                testResults.map((result, index) => (
                  <div key={index}>{result}</div>
                ))
              )}
            </div>
          </div>

          <div className="border rounded-lg p-6 bg-red-50 border-red-200">
            <h3 className="text-lg font-semibold mb-3 text-red-800">🚨 Critical Differences:</h3>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-red-100 rounded">
                <strong>Old Component:</strong> Multiple state update paths, race conditions, useEffect conflicts
              </div>
              <div className="p-3 bg-green-100 rounded">
                <strong>New Component:</strong> Single reducer, immutable state, action-based updates
              </div>
              <div className="p-3 bg-blue-100 rounded">
                <strong>Expected Result:</strong> ZERO duplicates under any circumstances
              </div>
            </div>
          </div>

          <div className="border rounded-lg p-6 bg-green-50 border-green-200">
            <h3 className="text-lg font-semibold mb-3 text-green-800">✅ Success Criteria:</h3>
            <ul className="space-y-2 text-sm text-green-700">
              <li>✅ No duplicate images in gallery</li>
              <li>✅ Consistent behavior across all upload methods</li>
              <li>✅ Reliable state when modal is reopened</li>
              <li>✅ Clean, maintainable code architecture</li>
              <li>✅ Predictable state management</li>
            </ul>
          </div>
        </div>
      </div>

      <NewMediaSelector
        open={isOpen}
        onClose={handleClose}
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        title="New Media Selector - Zero Duplicates"
        description="Completely rewritten with reducer pattern to eliminate duplicates"
        folder="projeler"
        acceptedTypes={["image/jpeg", "image/png", "image/webp"]}
        maxSizeMB={5}
      />
    </div>
  );
}
