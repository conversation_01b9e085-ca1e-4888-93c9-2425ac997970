"use client"

import { useQuery } from "@tanstack/react-query"
import { Card, CardContent } from "@/components/ui/card"

const fetcher = async () => {
  const res = await fetch("/api/technicians")
  return res.json()
}

function DashboardTechnicians() {
  const { data, isLoading } = useQuery({
    queryKey: ["technicians"],
    queryFn: fetcher
  })

  if (isLoading || !data) {
    return <Card><CardContent>Yükleniyor...</CardContent></Card>
  }

  return (
    <Card>
      <CardContent>
        <h3 className="font-semibold mb-2">Teknisyenler</h3>
        <ul className="space-y-1">
          {data.map((tech: { id: string; ad: string; soyad: string; aktifRandevuSayisi: number }) => (
            <li key={tech.id} className="flex justify-between">
              <span>{tech.ad} {tech.soyad}</span>
              <span className="text-xs text-muted-foreground">Aktif <PERSON>: {tech.aktifRandevuSayisi}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}

export default DashboardTechnicians
