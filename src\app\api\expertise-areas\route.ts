import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const expertiseAreas = await prisma.uzmanlikAlani.findMany({
      where: {
        silindi_mi: false,
      },
      select: {
        id: true,
        ad: true,
        aciklama: true,
        renk: true,
      },
      orderBy: {
        ad: "asc",
      },
    })

    return NextResponse.json(expertiseAreas)
  } catch (error) {
    console.error("Uzmanlık alanları yüklenirken hata:", error)
    return NextResponse.json(
      { message: "Uzmanlık alanları yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { ad, aciklama = "", renk = "#3B82F6" } = body;
    const newExpertise = await prisma.uzmanlikAlani.create({
      data: {
        ad,
        aciklama,
        renk,
        silindi_mi: false,
      },
    });
    return NextResponse.json(newExpertise, { status: 201 });
  } catch (error) {
    console.error("Uzmanlık alanı eklenirken hata:", error);
    return NextResponse.json({ message: "Uzmanlık alanı eklenirken hata oluştu" }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { id, ad, aciklama = "", renk = "#3B82F6" } = body;
    const updatedExpertise = await prisma.uzmanlikAlani.update({
      where: { id },
      data: { ad, aciklama, renk },
    });
    return NextResponse.json(updatedExpertise);
  } catch (error) {
    console.error("Uzmanlık alanı güncellenirken hata:", error);
    return NextResponse.json({ message: "Uzmanlık alanı güncellenirken hata oluştu" }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { id } = await request.json();
    await prisma.uzmanlikAlani.update({
      where: { id },
      data: { silindi_mi: true },
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Uzmanlık alanı silinirken hata:", error);
    return NextResponse.json({ message: "Uzmanlık alanı silinirken hata oluştu" }, { status: 500 });
  }
}
