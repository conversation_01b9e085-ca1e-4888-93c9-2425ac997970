import React from "react"
import { 
  SelectItem,
} from "@/components/ui/select"

// Safe değerler için sabitler
export const SELECT_VALUES = {
  NONE: "__none__",      // Hiçbiri/bo<PERSON> seçenek
  ALL: "all",            // Tümü (filtrelerde)
  AUTO: "auto",          // Otomatik (atamalar için)
  LOADING: "__loading__", // Yükleniyor durumu
} as const

/**
 * SafeSelectItem wrapper that prevents empty string values
 * Automatically converts empty/null/undefined values to safe defaults
 */
interface SafeSelectItemProps {
  value: string | null | undefined
  disabled?: boolean
  children: React.ReactNode
  className?: string
}

export function SafeSelectItem({ 
  value, 
  disabled, 
  children, 
  className 
}: SafeSelectItemProps) {
  // Prevent empty string values
  let safeValue: string
  let isAutoDisabled = false

  if (value === "" || value === null || value === undefined) {
    safeValue = SELECT_VALUES.NONE
    isAutoDisabled = true
    console.warn(`SafeSelectItem: Empty/null/undefined value provided, using "${SELECT_VALUES.NONE}" and auto-disabling.`)
  } else {
    safeValue = value
  }

  return (
    <SelectItem 
      value={safeValue} 
      disabled={disabled || isAutoDisabled} 
      className={className}
    >
      {children}
    </SelectItem>
  )
}

/**
 * Utility functions for handling select values
 */
export const SelectUtils = {
  /**
   * Convert special select values to null for API submission
   */
  toApiValue: (value: string | undefined): string | null => {
    if (!value || value === SELECT_VALUES.NONE || value === SELECT_VALUES.AUTO) {
      return null
    }
    return value
  },

  /**
   * Convert API value to safe select value
   */
  toSelectValue: (value: string | null | undefined, defaultValue = SELECT_VALUES.NONE): string => {
    return value || defaultValue
  },

  /**
   * Check if value is a special select value
   */  isSpecialValue: (value: string): boolean => {
    return (Object.values(SELECT_VALUES) as string[]).includes(value)
  }
}
