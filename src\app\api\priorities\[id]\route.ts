import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

// Validation schema
const priorityUpdateSchema = z.object({
  ad: z.string().min(1, "Ad alanı gereklidir"),
  seviye: z.number().min(1, "Seviye 1'den büyük olmalıdır").max(10, "Seviye 10'dan küçük olmalıdır"),
  renk: z.string().regex(/^#[0-9A-F]{6}$/i, "Geçerli bir hex renk kodu giriniz"),
  aciklama: z.string().optional(),
})

// GET - Get single priority
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const priority = await prisma.aciliyetSeviye.findFirst({
      where: {
        id: id,
        silindi_mi: false,
      },
    })

    if (!priority) {
      return NextResponse.json(
        { message: "Öncelik seviyesi bulunamadı" },
        { status: 404 }
      )
    }

    return NextResponse.json(priority)
  } catch (error) {
    console.error("Error fetching priority:", error)
    return NextResponse.json(
      { message: "Öncelik seviyesi yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

// PUT - Update priority
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    
    // Validate input
    const validatedData = priorityUpdateSchema.parse(body)
    
    // Check if priority exists
    const existingPriority = await prisma.aciliyetSeviye.findFirst({
      where: {
        id: id,
        silindi_mi: false,
      },
    })

    if (!existingPriority) {
      return NextResponse.json(
        { message: "Öncelik seviyesi bulunamadı" },
        { status: 404 }
      )
    }

    // Check if level is used by another priority
    if (validatedData.seviye !== existingPriority.seviye) {
      const existingLevel = await prisma.aciliyetSeviye.findFirst({
        where: {
          seviye: validatedData.seviye,
          silindi_mi: false,
          id: { not: id },
        },
      })

      if (existingLevel) {
        return NextResponse.json(
          { message: `${validatedData.seviye} seviyesi zaten kullanılıyor` },
          { status: 400 }
        )
      }
    }

    // Check if name is used by another priority
    if (validatedData.ad !== existingPriority.ad) {
      const existingName = await prisma.aciliyetSeviye.findFirst({
        where: {
          ad: validatedData.ad,
          silindi_mi: false,
          id: { not: id },
        },
      })

      if (existingName) {
        return NextResponse.json(
          { message: `"${validatedData.ad}" adı zaten kullanılıyor` },
          { status: 400 }
        )
      }
    }

    // Update priority
    const priority = await prisma.aciliyetSeviye.update({
      where: {
        id: id,
      },
      data: {
        ...validatedData,
        guncelleme_tarihi: new Date(),
      },
    })

    return NextResponse.json(priority)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Geçersiz veri", errors: error.errors },
        { status: 400 }
      )
    }
    
    console.error("Error updating priority:", error)
    return NextResponse.json(
      { message: "Öncelik seviyesi güncellenirken hata oluştu" },
      { status: 500 }
    )
  }
}

// DELETE - Delete priority
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Check if priority exists
    const existingPriority = await prisma.aciliyetSeviye.findFirst({
      where: {
        id: id,
        silindi_mi: false,
      },
    })

    if (!existingPriority) {
      return NextResponse.json(
        { message: "Öncelik seviyesi bulunamadı" },
        { status: 404 }
      )
    }

    // Check if priority is being used by faults
    const usageCount = await prisma.ariza.count({
      where: {
        aciliyet_id: id,
        silindi_mi: false,
      },
    })

    if (usageCount > 0) {
      return NextResponse.json(
        { 
          message: `Bu öncelik seviyesi ${usageCount} arızada kullanılıyor. Önce bu arızaların öncelik seviyesini değiştirin.` 
        },
        { status: 400 }
      )
    }

    // Soft delete
    await prisma.aciliyetSeviye.update({
      where: {
        id: id,
      },
      data: {
        silindi_mi: true,
        silinme_tarihi: new Date(),
      },
    })

    return NextResponse.json({ message: "Öncelik seviyesi başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting priority:", error)
    return NextResponse.json(
      { message: "Öncelik seviyesi silinirken hata oluştu" },
      { status: 500 }
    )
  }
} 