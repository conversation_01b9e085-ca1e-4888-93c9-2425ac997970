"use client"

import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants'

export interface BreadcrumbItem {
  label: string
  href?: string
  isActive: boolean
}

// URL segment'lerini Türkçe label'lara mapping
const pathMapping: Record<string, string> = {
  'dashboard': 'Dashboard',
  'projeler': '<PERSON>je <PERSON>önetim<PERSON>',
  'bloklar': '<PERSON><PERSON><PERSON> Yönetimi', 
  'daireler': 'Daire Yönetimi',
  'ariza': '<PERSON><PERSON><PERSON><PERSON> Yönetimi',
  'yeni': 'Yeni Arıza',
  'duzenle': '<PERSON><PERSON><PERSON><PERSON>',
  'raporlar': 'Raporlar',
  'ayarlar': 'Ayarlar',
  'oncelik-seviyeleri': 'Öncelik Seviyeleri',
  'durum-tanimlari': 'Du<PERSON> Tanımları',
  'kategoriler': '<PERSON><PERSON><PERSON>',
  'kullanici': '<PERSON><PERSON><PERSON><PERSON><PERSON>önetimi',
  'ekip': '<PERSON><PERSON><PERSON>önetim<PERSON>',
  'malzeme': '<PERSON><PERSON><PERSON> Yönetimi',
  'rapor': 'Raporlar',
  'ariza-tipleri': 'Arıza Tipleri',
  'aciliyet': 'Aciliyet Seviyeleri',
  'durumlar': 'Durumlar',
  'auth': 'Kimlik Doğrulama',
  'signin': 'Giriş Yap',
  'signup': 'Kayıt Ol',
  'teknisyen': 'Teknisyen',
  'teknisyenler': 'Teknisyen Yönetimi',
  'arizalar': 'Arızalar',
  'kullanicilar': 'Kullanıcı Yönetimi',
  'malzemeler': 'Malzeme Yönetimi',
  'randevu': 'Randevu Yönetimi'
}

// Optimized data fetchers with error handling
const fetchDynamicData = async (type: string, id: string): Promise<string> => {
  try {
    switch (type) {
      case 'ariza':
        const faultRes = await fetch(`/api/faults/${id}`)
        if (!faultRes.ok) throw new Error('Fault not found')
        const fault = await faultRes.json()
        return `${fault.numara} - ${fault.baslik}`
      
      case 'projeler':
        const projectRes = await fetch(`/api/projects/${id}`)
        if (!projectRes.ok) throw new Error('Project not found')
        const project = await projectRes.json()
        return project.ad
      
      case 'randevu':
        const appointmentRes = await fetch(`/api/appointments/${id}`)
        if (!appointmentRes.ok) throw new Error('Appointment not found')
        const appointment = await appointmentRes.json()
        return `Randevu - ${new Date(appointment.randevu_tarihi).toLocaleDateString('tr-TR')}`
      
      default:
        return id
    }
  } catch (error) {
    console.error('Error fetching dynamic data:', error)
    return 'Yükleniyor...'
  }
}

// Custom hook for breadcrumb data fetching
const useBreadcrumbData = (segments: string[]) => {
  const queryClient = useQueryClient()
  
  // Pre-fetch fault data for edit pages
  const faultEditMatch = segments[segments.length - 1] === 'duzenle' && segments.length >= 2
  const faultId = faultEditMatch ? segments[segments.length - 2] : null
  
  const { data: faultData } = useQuery({
    queryKey: QUERY_KEYS.FAULT(faultId!),
    queryFn: () => fetchDynamicData('ariza', faultId!),
    enabled: !!faultId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
  
  return { faultData, faultId }
}

export function useBreadcrumb() {
  const pathname = usePathname()
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([])
  const [loading, setLoading] = useState(false)
  
  const segments = pathname.split('/').filter(Boolean)
  const { faultData, faultId } = useBreadcrumbData(segments)

  useEffect(() => {
    const generateBreadcrumbs = async () => {
      setLoading(true)
      
      // Root item - her zaman Dashboard
      const items: BreadcrumbItem[] = [
        {
          label: 'Dashboard',
          href: '/dashboard',
          isActive: pathname === '/dashboard'
        }
      ]

      // Root sayfasında sadece Dashboard göster
      if (pathname === '/' || pathname === '/dashboard') {
        setBreadcrumbs(items)
        setLoading(false)
        return
      }

      // Arıza düzenle sayfası için özel breadcrumb (cached data kullan)
      if (faultId && faultData) {
        items.push(
          {
            label: 'Arıza Yönetimi',
            href: '/ariza',
            isActive: false
          },
          {
            label: faultData,
            href: `/ariza/${faultId}`,
            isActive: false
          },
          {
            label: 'Düzenle',
            href: undefined,
            isActive: true
          }
        )
        
        setBreadcrumbs(items)
        setLoading(false)
        return
      }

      // Path'i segment'lere böl ve işle
      let currentPath = ''

      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i]
        currentPath += `/${segment}`
        const isLast = i === segments.length - 1

        let label = pathMapping[segment] || segment

        // UUID pattern kontrolü (dynamic ID'ler için)
        const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(segment)
        
        if (isUUID && i > 0) {
          // Dynamic content için cached data kullan
          const parentSegment = segments[i - 1]
          label = await fetchDynamicData(parentSegment, segment)
        }

        items.push({
          label,
          href: !isLast ? currentPath : undefined,
          isActive: isLast
        })
      }

      setBreadcrumbs(items)
      setLoading(false)
    }

    generateBreadcrumbs()
  }, [pathname, faultData, faultId])

  return { breadcrumbs, loading }
}

// Helper function - component'ler için özel breadcrumb tanımlama
export function useCustomBreadcrumb(customItems: BreadcrumbItem[]) {
  return { breadcrumbs: customItems, loading: false }
}
