"use client"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent } from "@/components/ui/card"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"

const fetcher = async () => {
  const res = await fetch("/api/appointments?limit=5")
  return res.json()
}

function DashboardScheduled() {
  const { data, isLoading } = useQuery({
    queryKey: ["appointments", 5],
    queryFn: fetcher
  })

  if (isLoading || !data) {
    return <Card><CardContent>Yükleniyor...</CardContent></Card>
  }

  return (
    <Card>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Randevu Tarihi</TableHead>
              <TableHead>Arıza</TableHead>
              <TableHead>Teknisyenler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((appt: { id: string; randevu_tarihi: string; ariza?: { baslik: string }; teknisyenler: { teknisyen?: { ad: string } }[] }) => (
              <TableRow key={appt.id}>
                <TableCell>{new Date(appt.randevu_tarihi).toLocaleString("tr-TR")}</TableCell>
                <TableCell>{appt.ariza?.baslik}</TableCell>
                <TableCell>{appt.teknisyenler.map((t) => t.teknisyen?.ad).join(", ")}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export default DashboardScheduled
