import Redis from 'ioredis'

// Redis configuration for Docker environment
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  db: parseInt(process.env.REDIS_DB || '0'),
  connectTimeout: 10000,
  commandTimeout: 8000,
  enableOfflineQueue: true,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 1000,
  lazyConnect: false, // Connect immediately for Docker
  keepAlive: 30000,
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000)
    console.log(`🔄 Redis retry attempt ${times}, delay: ${delay}ms`)
    return delay
  },
  reconnectOnError: (err: Error) => {
    const targetError = 'READONLY'
    return err.message.includes(targetError)
  },
}

// Create Redis instance
const redis = new Redis(redisConfig)

// Connection event handlers
redis.on('connect', () => {
  console.log('✅ Redis connected successfully')
})

redis.on('ready', () => {
  console.log('🚀 Redis is ready to accept commands')
})

redis.on('error', (err) => {
  console.error('❌ Redis connection error:', err)
})

redis.on('close', () => {
  console.log('🔌 Redis connection closed')
})

redis.on('reconnecting', () => {
  console.log('🔄 Redis reconnecting...')
})

redis.on('end', () => {
  console.log('⚡ Redis connection ended')
})

// Health check function
export async function checkRedisHealth(): Promise<boolean> {
  const maxRetries = 3
  let retries = 0
  
  while (retries < maxRetries) {
    try {
      if (redis.status === 'end' || redis.status === 'close') {
        console.log(`Redis disconnected, attempting reconnection...`)
        await redis.connect()
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
      if (redis.status === 'connecting' || redis.status === 'wait') {
        console.log(`Redis status: ${redis.status}, retry ${retries + 1}/${maxRetries}`)
        await new Promise(resolve => setTimeout(resolve, 1000))
        retries++
        continue
      }
      
      if (redis.status !== 'ready') {
        console.log(`Redis not ready (status: ${redis.status}), retry ${retries + 1}/${maxRetries}`)
        retries++
        await new Promise(resolve => setTimeout(resolve, 1000))
        continue
      }
      
      const result = await redis.ping()
      if (result === 'PONG') {
        return true
      }
      
      retries++
    } catch (error) {
      console.error(`Redis health check failed (attempt ${retries + 1}/${maxRetries}):`, error)
      retries++
      if (retries < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
  }
  
  return false
}

// Connection restart function
export async function restartRedisConnection(): Promise<boolean> {
  try {
    console.log('🔄 Restarting Redis connection...')
    
    if (redis.status !== 'end') {
      await redis.disconnect(false)
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000))
    await redis.connect()
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    return await checkRedisHealth()
  } catch (error) {
    console.error('Redis restart failed:', error)
    return false
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Shutting down Redis connection...')
  await redis.quit()
  process.exit(0)
})

export default redis 