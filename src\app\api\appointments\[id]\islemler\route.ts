import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const randevuId = resolvedParams.id

    // Randevu işlemlerini getir
    const islemler = await prisma.randevuIslem.findMany({
      where: { randevu_id: randevuId },
      include: {
        islem_turu: true,
        teknisyen: {
          select: {
            id: true,
            ad: true,
            soyad: true,
            email: true,
            resim: true,
          }
        }
      },
      orderBy: {
        olusturulma_tarihi: "asc"
      }
    })

    return NextResponse.json(islemler)
  } catch (error) {
    console.error("Error fetching randevu işlemleri:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const randevuId = resolvedParams.id
    const body = await request.json()
    
    const { 
      islem_turu_id,
      teknisyen_id,
      aciklama,
      baslangic_saat,
      bitis_saat,
      durum,
      notlar,
      resimler
    } = body

    // Randevunun var olduğunu kontrol et
    const randevu = await prisma.randevu.findUnique({
      where: { id: randevuId }
    })

    if (!randevu) {
      return NextResponse.json(
        { error: "Randevu bulunamadı" },
        { status: 404 }
      )
    }

    // Yeni işlem kaydı oluştur
    const yeniIslem = await prisma.randevuIslem.create({
      data: {
        randevu_id: randevuId,
        islem_turu_id,
        teknisyen_id,
        aciklama,
        baslangic_saat: baslangic_saat ? new Date(baslangic_saat) : null,
        bitis_saat: bitis_saat ? new Date(bitis_saat) : null,
        durum: durum || "PLANLI",
        notlar,
        resimler: resimler || [],
      },
      include: {
        islem_turu: true,
        teknisyen: {
          select: {
            id: true,
            ad: true,
            soyad: true,
            email: true,
            resim: true,
          }
        }
      }
    })

    return NextResponse.json(yeniIslem)
  } catch (error) {
    console.error("Error creating randevu işlemi:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
