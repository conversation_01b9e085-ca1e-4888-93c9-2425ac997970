import { Metada<PERSON> } from "next"
import { Suspense } from "react"
import { ChevronRight } from "lucide-react"
import Link from "next/link"

import { FaultsList } from "@/components/faults/faults-list"
import { FaultsFilters } from "@/components/faults/faults-filters"
import { FaultsStats } from "@/components/faults/faults-stats"

export const metadata: Metadata = {
  title: "Arıza Yönetimi | Konut Arıza Takip Sistemi",
  description: "Arızaları görüntüleyin, filtreleyin ve yönetin",
}

interface FaultsPageProps {
  searchParams: Promise<{
    durum?: string | string[]
    oncelik?: string | string[]
    kategori?: string
    teknisyen?: string
    daire?: string
    blok?: string
    proje?: string
    baslangic?: string
    bitis?: string
    arama?: string
    sayfa?: string
    limit?: string
  }>
}

export default async function FaultsPage({ searchParams }: FaultsPageProps) {
  const resolvedSearchParams = await searchParams
  
  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Ana Sayfa
        </Link>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground">Arıza Yönetimi</span>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Arıza Yönetimi</h2>
          <p className="text-muted-foreground">
            Tüm arızaları görüntüleyin, filtreleyin ve yönetin
          </p>
        </div>
      </div>

      {/* İstatistikler */}
      <Suspense fallback={<div>İstatistikler yükleniyor...</div>}>
        <FaultsStats />
      </Suspense>

      <div className="grid gap-4 md:grid-cols-4">
        {/* Filtreler */}
        <div className="md:col-span-1">
          <FaultsFilters />
        </div>
        {/* Arıza Listesi */}
        <div className="md:col-span-3">
          <Suspense fallback={<div>Arızalar yükleniyor...</div>}>
            <FaultsList searchParams={resolvedSearchParams} />
          </Suspense>
        </div>
      </div>
    </div>
  )
} 