import { notFound } from "next/navigation";
import { headers } from "next/headers";
import EditProjectClient from "./EditProjectClient";

export default async function EditProjectPage({ params }: { params: Promise<{ projeSlug: string }> }) {
  const { projeSlug } = await params;
  // Dinamik baseUrl oluştur
  const headersList = await headers();
  const host = headersList.get('host');
  const protocol = host?.startsWith('localhost') ? 'http' : 'https';
  const baseUrl = `${protocol}://${host}`;
  const res = await fetch(`${baseUrl}/api/projects/${projeSlug}`, { cache: 'no-store' });
  if (!res.ok) return notFound();
  const project = await res.json();

  return (
    <div className="w-full">
      <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2 mt-8">
        <PERSON><PERSON><PERSON>
      </h1>
      <p className="text-muted-foreground text-base md:text-lg mb-8">
        Proje bilgilerini eksiksiz doldurun ve görsel seçin.
      </p>
      <EditProjectClient project={project} />
    </div>
  );
} 