"use client"

import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { 
  Package, 
  TrendingUp, 
  Download, 
  AlertTriangle, 
  BarChart3,
  DollarSign,
  Truck,
  Archive,
  Calendar
} from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from "recharts"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Breadcrumb } from "@/components/layout/breadcrumb"

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899']

const materialsFetcher = async () => {
  const res = await fetch("/api/materials")
  return res.json()
}

const appointmentMaterialsFetcher = async () => {
  const res = await fetch("/api/appointments")
  return res.json()
}

export default function MaterialReportsPage() {
  const [dateFilter, setDateFilter] = useState("this_month")
  
  const { data: materials, isLoading: materialsLoading } = useQuery({
    queryKey: ["materials"],
    queryFn: materialsFetcher
  })

  const { data: appointments, isLoading: appointmentsLoading } = useQuery({
    queryKey: ["appointments"],
    queryFn: appointmentMaterialsFetcher
  })

  if (materialsLoading || appointmentsLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-white/50 rounded-xl animate-pulse"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
        </div>
      </div>
    )
  }

  const materialList = materials?.materials || []
  const appointmentList = appointments || []

  // Mock material usage data - In production, this would come from usage tracking
  const materialUsage = materialList.map((material: any) => ({
    id: material.id,
    name: material.ad,
    used: Math.floor(Math.random() * 100) + 10,
    stock: Math.floor(Math.random() * 200) + 50,
    unit: material.birim,
    cost: (Math.random() * 1000 + 100).toFixed(2)
  }))

  // Most used materials
  const mostUsedMaterials = materialUsage
    .sort((a, b) => b.used - a.used)
    .slice(0, 10)

  // Stock status
  const lowStockMaterials = materialUsage.filter(m => m.stock < 30)
  const totalMaterials = materialList.length
  const totalUsed = materialUsage.reduce((sum, m) => sum + m.used, 0)
  const totalCost = materialUsage.reduce((sum, m) => sum + parseFloat(m.cost), 0)

  // Chart data
  const usageData = mostUsedMaterials.map(m => ({
    name: m.name.length > 15 ? m.name.substring(0, 15) + '...' : m.name,
    kullanim: m.used,
    stok: m.stock
  }))

  const stockStatusData = [
    { name: 'Yeterli', value: materialUsage.filter(m => m.stock >= 50).length, color: COLORS[1] },
    { name: 'Düşük', value: materialUsage.filter(m => m.stock >= 30 && m.stock < 50).length, color: COLORS[2] },
    { name: 'Kritik', value: materialUsage.filter(m => m.stock < 30).length, color: COLORS[3] }
  ]

  const monthlyUsageData = [
    { month: 'Oca', kullanim: 850, maliyet: 12500 },
    { month: 'Şub', kullanim: 920, maliyet: 13200 },
    { month: 'Mar', kullanim: 780, maliyet: 11800 },
    { month: 'Nis', kullanim: 1100, maliyet: 15600 },
    { month: 'May', kullanim: 980, maliyet: 14200 },
    { month: 'Haz', kullanim: 1200, maliyet: 16800 },
  ]

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
            📦 Malzeme Raporları
          </h1>
          <p className="text-muted-foreground mt-1">
            Malzeme kullanımı, stok durumu ve maliyet analizi
          </p>
        </div>
        <div className="flex gap-3">
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Zaman aralığı" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="this_week">Bu Hafta</SelectItem>
              <SelectItem value="this_month">Bu Ay</SelectItem>
              <SelectItem value="last_month">Geçen Ay</SelectItem>
              <SelectItem value="last_3_months">Son 3 Ay</SelectItem>
            </SelectContent>
          </Select>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Toplam Malzeme</p>
                <p className="text-2xl font-bold text-blue-900">{totalMaterials}</p>
                <p className="text-xs text-blue-600 mt-1">Kayıtlı malzeme</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Bu Ay Kullanım</p>
                <p className="text-2xl font-bold text-green-900">{totalUsed}</p>
                <p className="text-xs text-green-600 mt-1">Adet/birim</p>
              </div>
              <Truck className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Toplam Maliyet</p>
                <p className="text-2xl font-bold text-orange-900">₺{totalCost.toFixed(0)}</p>
                <p className="text-xs text-orange-600 mt-1">Bu ay</p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-red-50 to-red-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Düşük Stok</p>
                <p className="text-2xl font-bold text-red-900">{lowStockMaterials.length}</p>
                <p className="text-xs text-red-600 mt-1">Kritik seviyede</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Charts */}
        <div className="lg:col-span-2 space-y-6">
          {/* Usage Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-600" />
                En Çok Kullanılan Malzemeler
              </CardTitle>
              <CardDescription>
                Kullanım miktarı ve mevcut stok durumu
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={usageData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="kullanim" fill="#10B981" name="Kullanım" />
                    <Bar dataKey="stok" fill="#3B82F6" name="Stok" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Monthly Trend */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                Aylık Kullanım ve Maliyet Trendi
              </CardTitle>
              <CardDescription>
                Son 6 ayın kullanım ve maliyet analizi
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={monthlyUsageData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="kullanim" fill="#8B5CF6" name="Kullanım" />
                    <Line yAxisId="right" type="monotone" dataKey="maliyet" stroke="#F59E0B" strokeWidth={3} name="Maliyet (₺)" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Stock Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5 text-blue-600" />
                Stok Durumu
              </CardTitle>
              <CardDescription>
                Malzeme stok seviyelerinin dağılımı
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      dataKey="value"
                      data={stockStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                    >
                      {stockStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Low Stock Alert */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                Düşük Stok Uyarısı
              </CardTitle>
              <CardDescription>
                Stok seviyesi kritik olan malzemeler
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {lowStockMaterials.slice(0, 5).map((material, index) => (
                <div key={material.id} className="flex items-center justify-between p-3 rounded-lg bg-red-50 border border-red-200">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-red-900">{material.name}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Progress value={(material.stock / 100) * 100} className="h-2 flex-1" />
                      <span className="text-xs text-red-600">{material.stock} {material.unit}</span>
                    </div>
                  </div>
                </div>
              ))}
              {lowStockMaterials.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <Package className="mx-auto h-8 w-8 mb-2 opacity-50" />
                  <p className="text-sm">Tüm malzemeler yeterli stokta</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-purple-600" />
                Hızlı İşlemler
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Package className="mr-2 h-4 w-4" />
                Stok Raporu İndir
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <DollarSign className="mr-2 h-4 w-4" />
                Maliyet Analizi
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Kritik Stok Listesi
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 