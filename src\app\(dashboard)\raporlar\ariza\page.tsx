"use client"

import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { 
  Alert<PERSON>riangle, 
  TrendingUp, 
  Download, 
  Calendar, 
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Filter,
  FileText
} from "lucide-react"
import { <PERSON><PERSON><PERSON> as RechartsPieChart, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Pie } from "recharts"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Breadcrumb } from "@/components/layout/breadcrumb"

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']

const faultStatsFetcher = async () => {
  const res = await fetch("/api/faults/stats")
  return res.json()
}

const faultsFetcher = async () => {
  const res = await fetch("/api/faults?limit=100")
  return res.json()
}

const categoriesFetcher = async () => {
  const res = await fetch("/api/categories")
  return res.json()
}

export default function FaultReportsPage() {
  const [dateFilter, setDateFilter] = useState("this_month")
  
  const { data: faultStats, isLoading: statsLoading } = useQuery({
    queryKey: ["faults", "stats"],
    queryFn: faultStatsFetcher
  })

  const { data: faultsData, isLoading: faultsLoading } = useQuery({
    queryKey: ["faults", "list"],
    queryFn: faultsFetcher
  })

  const { data: categoriesData, isLoading: categoriesLoading } = useQuery({
    queryKey: ["categories"],
    queryFn: categoriesFetcher
  })

  if (statsLoading || faultsLoading || categoriesLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-white/50 rounded-xl animate-pulse"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
        </div>
      </div>
    )
  }

  const faults = faultsData?.faults || []
  const categories = categoriesData?.categories || []

  // Prepare chart data
  const statusData = [
    { name: 'Beklemede', value: faultStats?.beklemede || 0, color: COLORS[0] },
    { name: 'Devam Ediyor', value: faultStats?.devamEdiyor || 0, color: COLORS[1] },
    { name: 'Tamamlandı', value: faultStats?.tamamlandi || 0, color: COLORS[2] },
    { name: 'İptal', value: faultStats?.iptal || 0, color: COLORS[3] }
  ]

  const categoryData = categories.map((cat: any, index: number) => ({
    name: cat.ad,
    value: faults.filter((f: any) => f.tip?.id === cat.id).length,
    color: cat.renk || COLORS[index % COLORS.length]
  })).filter((item: any) => item.value > 0)

  const monthlyData = [
    { month: 'Oca', count: 12 },
    { month: 'Şub', count: 19 },
    { month: 'Mar', count: 15 },
    { month: 'Nis', count: 22 },
    { month: 'May', count: 18 },
    { month: 'Haz', count: 25 },
  ]

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
            📈 Arıza Raporları
          </h1>
          <p className="text-muted-foreground mt-1">
            Arıza istatistikleri, trend analizi ve performans raporları
          </p>
        </div>
        <div className="flex gap-3">
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Zaman aralığı" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="this_week">Bu Hafta</SelectItem>
              <SelectItem value="this_month">Bu Ay</SelectItem>
              <SelectItem value="last_month">Geçen Ay</SelectItem>
              <SelectItem value="last_3_months">Son 3 Ay</SelectItem>
            </SelectContent>
          </Select>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Toplam Arıza</p>
                <p className="text-2xl font-bold text-blue-900">{faultStats?.toplam || 0}</p>
                <p className="text-xs text-blue-600 mt-1">
                  {faultStats?.yuzdelik ? `%${faultStats.yuzdelik.toFixed(1)} artış` : 'Bu ay'}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Beklemede</p>
                <p className="text-2xl font-bold text-orange-900">{faultStats?.beklemede || 0}</p>
                <p className="text-xs text-orange-600 mt-1">Atanmayı bekliyor</p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Devam Ediyor</p>
                <p className="text-2xl font-bold text-purple-900">{faultStats?.devamEdiyor || 0}</p>
                <p className="text-xs text-purple-600 mt-1">Çözüm aşamasında</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Tamamlandı</p>
                <p className="text-2xl font-bold text-green-900">{faultStats?.tamamlandi || 0}</p>
                <p className="text-xs text-green-600 mt-1">Başarıyla çözüldü</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5 text-blue-600" />
              Durum Dağılımı
            </CardTitle>
            <CardDescription>
              Arızaların mevcut durum bazında dağılımı
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    dataKey="value"
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Category Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              Kategori Dağılımı
            </CardTitle>
            <CardDescription>
              Arıza kategorilerine göre dağılım
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={categoryData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#10B981" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              Aylık Trend
            </CardTitle>
            <CardDescription>
              Son 6 ayın arıza trendi
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="count" fill="#8B5CF6" name="Arıza Sayısı" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Recent Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-orange-600" />
              Son Raporlar
            </CardTitle>
            <CardDescription>
              En son oluşturulan arıza raporları
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { title: "Haziran Ayı Özeti", date: "29.06.2025", type: "PDF", status: "Hazır" },
              { title: "Kritik Arıza Analizi", date: "28.06.2025", type: "Excel", status: "İşleniyor" },
              { title: "Kategori Performansı", date: "27.06.2025", type: "PDF", status: "Hazır" },
            ].map((report, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors border">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-orange-100 text-orange-600">
                    <FileText className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{report.title}</p>
                    <p className="text-xs text-muted-foreground">{report.date} • {report.type}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={report.status === "Hazır" ? "default" : "secondary"}>
                    {report.status}
                  </Badge>
                  {report.status === "Hazır" && (
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 