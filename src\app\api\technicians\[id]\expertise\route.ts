import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { uzmanliklar = [] } = body;
    // Önce mevcut uzmanlıklar silinir
    await prisma.teknisyenUzmanlikAlani.deleteMany({ where: { teknisyen_id: id } });
    // Sonra yeni uzmanlıklar atanır
    await prisma.teknisyenUzmanlikAlani.createMany({
      data: uzmanliklar.map((uzmanlikId: string) => ({
        teknisyen_id: id,
        uzmanlik_alani_id: uzmanlikId,
      })),
    });
    // Güncel uzmanlıkları getir
    const updated = await prisma.teknisyenUzmanlikAlani.findMany({
      where: { teknisyen_id: id },
      include: { uzmanlik_alani: true },
    });
    return NextResponse.json(updated);
  } catch (error) {
    console.error("Teknisyen uzmanlık güncelleme hatası:", error);
    return NextResponse.json({ message: "Teknisyen uzmanlık güncelleme hatası" }, { status: 500 });
  }
} 