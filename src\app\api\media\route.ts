import { NextRequest, NextResponse } from "next/server";
import { readdir, stat } from "fs/promises";
import { join } from "path";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const customFolder = searchParams.get("customFolder") || "media";
    const folderPath = join(process.cwd(), "public", customFolder);
    let files: any[] = [];
    try {
      const fileNames = await readdir(folderPath);
      const filteredFileNames = fileNames.filter(name => !name.endsWith('.db'));

      console.log(`📁 Found ${filteredFileNames.length} files in ${folderPath}`);
      console.log(`📁 Files:`, filteredFileNames);

      files = await Promise.all(
        filteredFileNames.map(async (name) => {
          const fileStat = await stat(join(folderPath, name));
          return {
            id: name,
            filename: name,
            url: `/${customFolder}/${name}`,
            originalName: name,
            size: fileStat.size,
            mimeType: '',
            createdAt: fileStat.birthtime,
          };
        })
      );

      // Server-side deduplication by URL
      const uniqueFiles = files.filter((file, index, arr) =>
        arr.findIndex(f => f.url === file.url) === index
      );

      if (files.length !== uniqueFiles.length) {
        console.warn(`⚠️ Server-side deduplication: ${files.length} → ${uniqueFiles.length} files`);
      }

      files = uniqueFiles;

    } catch (e) {
      // Klasör yoksa boş liste döndür
      console.log(`📁 Folder ${folderPath} not found, returning empty list`);
      files = [];
    }
    return NextResponse.json({ data: files });
  } catch (error) {
    console.error("Media list error:", error);
    return NextResponse.json({ error: "Failed to list media" }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get("url");
    if (!url) {
      return NextResponse.json({ error: "Silinecek dosya url parametresi gerekli." }, { status: 400 });
    }
    // url: /media/xxx.jpg veya /images/projeler/xxx.jpg gibi gelir
    // Güvenlik için sadece public altındaki dosyaları sil
    const filePath = join(process.cwd(), "public", url.replace(/^\//, ""));
    const fs = await import("fs/promises");
    try {
      await fs.unlink(filePath);
      return NextResponse.json({ success: true });
    } catch (err) {
      if ((err as any).code === "ENOENT") {
        return NextResponse.json({ error: "Dosya bulunamadı." }, { status: 404 });
      }
      console.error("Dosya silme hatası:", err);
      return NextResponse.json({ error: "Dosya silinemedi." }, { status: 500 });
    }
  } catch (error) {
    console.error("Media delete error:", error);
    return NextResponse.json({ error: "Failed to delete media" }, { status: 500 });
  }
} 