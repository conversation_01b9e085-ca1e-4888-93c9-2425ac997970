import { NextRequest, NextResponse } from "next/server";
import { readdir, stat } from "fs/promises";
import { join } from "path";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const customFolder = searchParams.get("customFolder") || "media";
    const folderPath = join(process.cwd(), "public", customFolder);
    let files: any[] = [];
    try {
      const fileNames = await readdir(folderPath);
      files = await Promise.all(
        fileNames.filter(name => !name.endsWith('.db')).map(async (name) => {
          const fileStat = await stat(join(folderPath, name));
          return {
            id: name,
            filename: name,
            url: `/${customFolder}/${name}`,
            originalName: name,
            size: fileStat.size,
            mimeType: '',
            createdAt: fileStat.birthtime,
          };
        })
      );
    } catch (e) {
      // Klasör yoksa boş liste döndür
      files = [];
    }
    return NextResponse.json({ data: files });
  } catch (error) {
    console.error("Media list error:", error);
    return NextResponse.json({ error: "Failed to list media" }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get("url");
    if (!url) {
      return NextResponse.json({ error: "Silinecek dosya url parametresi gerekli." }, { status: 400 });
    }
    // url: /media/xxx.jpg veya /images/projeler/xxx.jpg gibi gelir
    // Güvenlik için sadece public altındaki dosyaları sil
    const filePath = join(process.cwd(), "public", url.replace(/^\//, ""));
    const fs = await import("fs/promises");
    try {
      await fs.unlink(filePath);
      return NextResponse.json({ success: true });
    } catch (err) {
      if ((err as any).code === "ENOENT") {
        return NextResponse.json({ error: "Dosya bulunamadı." }, { status: 404 });
      }
      console.error("Dosya silme hatası:", err);
      return NextResponse.json({ error: "Dosya silinemedi." }, { status: 500 });
    }
  } catch (error) {
    console.error("Media delete error:", error);
    return NextResponse.json({ error: "Failed to delete media" }, { status: 500 });
  }
} 