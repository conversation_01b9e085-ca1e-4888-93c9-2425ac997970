import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug } = await params;

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Arızaları getir
    const faults = await prisma.ariza.findMany({
      where: { daire_id: apartment.id, silindi_mi: false },
      include: {
        tip: true,
        durum: true,
        aciliyet: true,
        daire: {
          select: {
            id: true,
            slug: true,
            blok: {
              select: {
                id: true,
                ad: true,
                slug: true,
                proje: { select: { id: true, ad: true, slug: true } },
              },
            },
          },
        },
        olusturan: { select: { id: true, ad: true, soyad: true, resim: true } },
      },
      orderBy: { olusturulma_tarihi: "desc" },
    });

    return NextResponse.json({ faults, total: faults.length });
  } catch (error) {
    console.error("Daire arızaları getirilirken hata:", error);
    return NextResponse.json({ error: "Daire arızaları getirilemedi" }, { status: 500 });
  }
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug } = await params;
    const body = await request.json();

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Varsayılan "Açık" durumunu bul
    const defaultStatus = await prisma.arizaDurum.findFirst({
      where: {
        ad: "Açık",
        silindi_mi: false,
      },
    });

    if (!defaultStatus) {
      return NextResponse.json({ error: "Varsayılan durum bulunamadı" }, { status: 500 });
    }

    // Arıza oluştur
    // Slug üretici yardımcı fonksiyon
    function generateFaultSlug(baslik: string, daireId: string) {
      const base = baslik
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
        .substring(0, 40);
      return `${base}-${daireId}-${Date.now()}`;
    }

    const newFault = await prisma.ariza.create({
      data: {
        baslik: body.baslik,
        aciklama: body.aciklama,
        ariza_tip_id: body.ariza_tip_id,
        aciliyet_id: body.aciliyet_id,
        bildiren_ad_soyad: body.bildirenAdSoyad,
        bildiren_telefon: body.bildirenTelefon,
        resimler: body.fotograflar || [],
        daire_id: apartment.id,
        durum_id: defaultStatus.id,
        slug: generateFaultSlug(body.baslik, apartment.id),
      },
    });

    return NextResponse.json(newFault, { status: 201 });
  } catch (error) {
    console.error("Arıza eklenirken hata:", error);
    return NextResponse.json({ error: "Arıza eklenemedi" }, { status: 500 });
  }
}
