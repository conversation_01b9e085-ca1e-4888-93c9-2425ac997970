"use client"

import { useState, useEffect, useMemo } from "react"
import { Calendar as BigCalendar, momentLocalizer, View, Views } from "react-big-calendar"
import moment from "moment"
import "moment/locale/tr"
import { 
  Calendar, 
  User, 
  Search, 
  Filter,
  List,
  CalendarDays,
  Clock,
  MapPin,
  AlertCircle,
  Plus,
  Eye
} from "lucide-react"
import { toast } from "sonner"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  Di<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AppointmentStatus, APPOINTMENT_STATUS_LABELS, APPOINTMENT_STATUS_COLORS } from "@/lib/enums"
import { LoadingButton } from "@/components/ui/loading-button"
import { Skeleton } from "@/components/ui/skeleton"
import { ModernAppointmentDashboard } from "@/components/appointments/modern-appointment-dashboard"
import { Breadcrumb } from "@/components/layout/breadcrumb"

// Moment.js Türkçe lokalizasyonu
moment.locale("tr")
const localizer = momentLocalizer(moment)

// Calendar styles
import "react-big-calendar/lib/css/react-big-calendar.css"

interface AppointmentData {
  id: string
  randevu_tarihi: Date
  durum: keyof typeof AppointmentStatus
  aciklama: string | null
  devam_randevusu_mu?: boolean  // Devam randevusu mu?
  onceki_randevu_id?: string    // Önceki randevu ID'si
  ariza: {
    id: string
    numara: string
    baslik: string
    daire: {
      numara: string
      blok: {
        ad: string
        proje: {
          ad: string
        }
      }
    }
  }
  teknisyenler?: Array<{
    teknisyen?: {
      id: string
      ad: string
      soyad: string
    }
  }>
}

interface CalendarEvent {
  id: string
  title: string
  start: Date
  end: Date
  resource: AppointmentData
}

export default function AppointmentsPage() {
  const [appointments, setAppointments] = useState<AppointmentData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("ALL")
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentData | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [calendarView, setCalendarView] = useState<View>(Views.MONTH)

  useEffect(() => {
    loadAppointments()
  }, [])

  // URL parametresinden refresh kontrolü
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('refresh') === 'true') {
      // Parametreyi temizle
      window.history.replaceState({}, '', window.location.pathname)
      // Verileri yenile
      loadAppointments()
      toast.success("Randevular yenilendi")
    }
  }, [])

  const loadAppointments = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/appointments")
      if (response.ok) {
        const data = await response.json()
        const safeAppointments = data.filter((appointment: AppointmentData) => 
          appointment && 
          appointment.id && 
          appointment.ariza && 
          appointment.ariza.id &&
          appointment.ariza.daire &&
          appointment.ariza.daire.blok &&
          appointment.ariza.daire.blok.proje
        )
        setAppointments(safeAppointments)
      } else {
        toast.error("Randevular yüklenirken hata oluştu")
      }
    } catch (error) {
      console.error("Randevular yüklenirken hata:", error)
      toast.error("Randevular yüklenirken hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatTime = (dateString: string | Date) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString("tr-TR", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (!appointment || !appointment.ariza) return false
    
    const matchesSearch = 
      appointment.ariza.numara?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.ariza.baslik?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.ariza.daire?.numara?.toLowerCase().includes(searchTerm.toLowerCase())
    
    // Status/Date filtering
    let matchesStatus = true
    if (statusFilter === "TODAY") {
      const today = new Date()
      const appointmentDate = new Date(appointment.randevu_tarihi)
      matchesStatus = 
        today.getDate() === appointmentDate.getDate() &&
        today.getMonth() === appointmentDate.getMonth() &&
        today.getFullYear() === appointmentDate.getFullYear()
    } else if (statusFilter === "ALL") {
      matchesStatus = true
    } else {
      matchesStatus = appointment.durum === statusFilter
    }
    
    return matchesSearch && matchesStatus
  })

  // Calendar events
  const calendarEvents: CalendarEvent[] = useMemo(() => {
    return filteredAppointments.map(appointment => {
      const startDate = new Date(appointment.randevu_tarihi)
      const endDate = new Date(startDate.getTime() + 60 * 60 * 1000) // 1 saat varsayılan süre
      
      return {
        id: appointment.id,
        title: `${appointment.ariza.numara} - ${appointment.ariza.baslik}`,
        start: startDate,
        end: endDate,
        resource: appointment
      }
    })
  }, [filteredAppointments])

  const handleSelectEvent = (event: CalendarEvent) => {
    setSelectedAppointment(event.resource)
    setIsDetailDialogOpen(true)
  }

  const handleSelectSlot = (slotInfo: any) => {
    // Yeni randevu oluşturma için gelecekte implementasyon
    toast.info("Yeni randevu oluşturma özelliği yakında eklenecek")
  }

  const eventStyleGetter = (event: CalendarEvent) => {
    const appointment = event.resource
    const baseColor = APPOINTMENT_STATUS_COLORS[appointment.durum]
    
    return {
      style: {
        backgroundColor: baseColor,
        borderRadius: '5px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block',
        fontSize: '12px',
        padding: '2px 5px'
      }
    }
  }

  const getTechnicianNames = (appointment: AppointmentData) => {
    const technicians = appointment.teknisyenler?.filter(at => at.teknisyen) || []
    if (technicians.length === 0) return "Teknisyen atanmamış"
    return technicians.map(at => `${at.teknisyen!.ad} ${at.teknisyen!.soyad}`).join(", ")
  }

  const getStatusStats = () => {
    const stats = appointments.reduce((acc, appointment) => {
      acc[appointment.durum] = (acc[appointment.durum] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    return stats
  }

  const stats = getStatusStats()

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-96 mt-2" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        
        <div className="grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-96 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Modern Dashboard */}
      <ModernAppointmentDashboard 
        appointments={appointments}
        onRefresh={loadAppointments}
      />

      {/* Filtreler */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtreler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Arıza numarası, başlık veya daire numarası ile ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Durum filtrele" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">🔍 Tüm Durumlar</SelectItem>
                <SelectItem value="TODAY">📅 Bugünkü Randevular</SelectItem>
                {Object.entries(APPOINTMENT_STATUS_LABELS).map(([key, label]) => (
                  <SelectItem key={key} value={key}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Ana İçerik - Takvim ve Liste Görünümü */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Randevular ({filteredAppointments.length})</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="calendar" className="space-y-4">
            <TabsList>
              <TabsTrigger value="calendar" className="gap-2">
                <CalendarDays className="h-4 w-4" />
                Takvim Görünümü
              </TabsTrigger>
              <TabsTrigger value="list" className="gap-2">
                <List className="h-4 w-4" />
                Liste Görünümü
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="calendar" className="space-y-4">
              <div className="h-[600px]">
                <BigCalendar
                  localizer={localizer}
                  events={calendarEvents}
                  startAccessor="start"
                  endAccessor="end"
                  style={{ height: '100%' }}
                  onSelectEvent={handleSelectEvent}
                  onSelectSlot={handleSelectSlot}
                  selectable
                  eventPropGetter={eventStyleGetter}
                  view={calendarView}
                  onView={setCalendarView}
                  views={[Views.MONTH, Views.WEEK, Views.DAY]}
                  messages={{
                    next: "Sonraki",
                    previous: "Önceki",
                    today: "Bugün",
                    month: "Ay",
                    week: "Hafta",
                    day: "Gün",
                    agenda: "Ajanda",
                    date: "Tarih",
                    time: "Saat",
                    event: "Randevu",
                    noEventsInRange: "Bu aralıkta randevu bulunmuyor",
                    allDay: "Tüm gün"
                  }}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="list">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Arıza</TableHead>
                    <TableHead>Konum</TableHead>
                    <TableHead>Randevu Tarihi</TableHead>
                    <TableHead>Teknisyenler</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAppointments.map((appointment) => (
                    <TableRow 
                      key={appointment.id}
                      className={
                        appointment.durum === "TAMAMLANDI" 
                          ? "bg-green-50 hover:bg-green-100 border-l-4 border-l-green-500" 
                          : appointment.durum === "DEVAM_EDIYOR"
                          ? "bg-orange-50 hover:bg-orange-100 border-l-4 border-l-orange-500"
                          : appointment.durum === "IPTAL"
                          ? "bg-gray-50 hover:bg-gray-100 border-l-4 border-l-gray-500"
                          : "hover:bg-gray-50"
                      }
                    >
                      <TableCell>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{appointment.ariza.numara}</span>
                            {appointment.devam_randevusu_mu && (
                              <Badge 
                                className="bg-purple-600 hover:bg-purple-700 text-white font-bold text-xs px-2 py-1"
                              >
                                🔗 DEVAM
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {appointment.ariza.baslik}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <div className="text-sm">
                            <div>{appointment.ariza.daire.blok.proje.ad}</div>
                            <div className="text-muted-foreground">
                              {appointment.ariza.daire.blok.ad} - Daire {appointment.ariza.daire.numara}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{formatDate(appointment.randevu_tarihi).split(' ').slice(0, 3).join(' ')}</div>
                            <div className="text-sm text-muted-foreground">{formatTime(appointment.randevu_tarihi)}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {appointment.teknisyenler?.filter(at => at.teknisyen).map((at) => (
                            <div key={at.teknisyen!.id} className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarFallback className="text-xs">
                                  {at.teknisyen!.ad[0]}{at.teknisyen!.soyad[0]}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm">
                                {at.teknisyen!.ad} {at.teknisyen!.soyad}
                              </span>
                            </div>
                          )) || []}
                          {(!appointment.teknisyenler || appointment.teknisyenler.filter(at => at.teknisyen).length === 0) && (
                            <span className="text-sm text-muted-foreground">Teknisyen atanmamış</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-2">
                          {/* Durum Badge'i - Daha Belirgin */}
                          <div className="flex items-center gap-2">
                            {appointment.durum === "TAMAMLANDI" ? (
                              <Badge className="bg-green-600 hover:bg-green-700 text-white font-semibold px-3 py-1">
                                ✅ {APPOINTMENT_STATUS_LABELS[appointment.durum]}
                              </Badge>
                            ) : appointment.durum === "DEVAM_EDIYOR" ? (
                              <Badge className="bg-orange-600 hover:bg-orange-700 text-white font-semibold px-3 py-1">
                                🔄 {APPOINTMENT_STATUS_LABELS[appointment.durum]}
                              </Badge>
                            ) : appointment.durum === "PLANLI" ? (
                              <Badge className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-3 py-1">
                                📅 {APPOINTMENT_STATUS_LABELS[appointment.durum]}
                              </Badge>
                            ) : (
                              <Badge className="bg-gray-600 hover:bg-gray-700 text-white font-semibold px-3 py-1">
                                ❌ {APPOINTMENT_STATUS_LABELS[appointment.durum]}
                              </Badge>
                            )}
                          </div>
                          
                          {/* Devam Randevusu Badge'i */}
                          {appointment.devam_randevusu_mu && (
                            <Badge className="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-3 py-1 text-xs">
                              🔄 Devam Randevusu
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedAppointment(appointment)
                              setIsDetailDialogOpen(true)
                            }}
                            className="gap-1"
                          >
                            <Eye className="h-3 w-3" />
                            Detay
                          </Button>
                          <LoadingButton 
                            href={`/projeler/${appointment.daire.blok.proje.slug}/bloklar/${appointment.daire.blok.slug}/${appointment.daire.slug}/arizalar/${appointment.ariza_id}/randevular/${appointment.id}`}
                            variant="outline" 
                            size="sm"
                          >
                            Düzenle
                          </LoadingButton>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {filteredAppointments.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center gap-2">
                          <CalendarDays className="h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground">
                            {searchTerm || statusFilter !== "ALL" 
                              ? "Filtrelere uygun randevu bulunamadı"
                              : "Henüz randevu bulunmuyor"
                            }
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Randevu Detay Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Randevu Detayları</DialogTitle>
          </DialogHeader>
          {selectedAppointment && (
            <div className="space-y-6">
              {/* Arıza Bilgileri */}
              <div>
                <h3 className="font-semibold mb-3">Arıza Bilgileri</h3>
                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Arıza Numarası:</span>
                    <span className="font-medium">{selectedAppointment.ariza.numara}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Başlık:</span>
                    <span className="font-medium">{selectedAppointment.ariza.baslik}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Konum:</span>
                    <span className="font-medium">
                      {selectedAppointment.ariza.daire.blok.proje.ad} - {selectedAppointment.ariza.daire.blok.ad} - Daire {selectedAppointment.ariza.daire.numara}
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Randevu Bilgileri */}
              <div>
                <h3 className="font-semibold mb-3">Randevu Bilgileri</h3>
                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Tarih & Saat:</span>
                    <span className="font-medium">{formatDate(selectedAppointment.randevu_tarihi)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Durum:</span>
                    <div className="flex items-center gap-2">
                      {selectedAppointment.durum === "TAMAMLANDI" ? (
                        <Badge className="bg-green-600 hover:bg-green-700 text-white font-semibold">
                          ✅ {APPOINTMENT_STATUS_LABELS[selectedAppointment.durum]}
                        </Badge>
                      ) : selectedAppointment.durum === "DEVAM_EDIYOR" ? (
                        <Badge className="bg-orange-600 hover:bg-orange-700 text-white font-semibold">
                          🔄 {APPOINTMENT_STATUS_LABELS[selectedAppointment.durum]}
                        </Badge>
                      ) : selectedAppointment.durum === "PLANLI" ? (
                        <Badge className="bg-blue-600 hover:bg-blue-700 text-white font-semibold">
                          📅 {APPOINTMENT_STATUS_LABELS[selectedAppointment.durum]}
                        </Badge>
                      ) : (
                        <Badge className="bg-gray-600 hover:bg-gray-700 text-white font-semibold">
                          ❌ {APPOINTMENT_STATUS_LABELS[selectedAppointment.durum]}
                        </Badge>
                      )}
                      {selectedAppointment.devam_randevusu_mu && (
                        <Badge className="bg-purple-600 hover:bg-purple-700 text-white font-semibold text-xs">
                          🔗 DEVAM
                        </Badge>
                      )}
                    </div>
                  </div>
                  {selectedAppointment.aciklama && (
                    <div>
                      <span className="text-muted-foreground">Açıklama:</span>
                      <p className="mt-1 text-sm bg-muted p-3 rounded-md">{selectedAppointment.aciklama}</p>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Teknisyen Bilgileri */}
              <div>
                <h3 className="font-semibold mb-3">Atanan Teknisyenler</h3>
                {selectedAppointment.teknisyenler?.filter(at => at.teknisyen).length ? (
                  <div className="space-y-2">
                    {selectedAppointment.teknisyenler.filter(at => at.teknisyen).map((at) => (
                      <div key={at.teknisyen!.id} className="flex items-center gap-3 p-3 bg-muted rounded-md">
                        <Avatar>
                          <AvatarFallback>
                            {at.teknisyen!.ad[0]}{at.teknisyen!.soyad[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{at.teknisyen!.ad} {at.teknisyen!.soyad}</div>
                          <div className="text-sm text-muted-foreground">Teknisyen</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">Henüz teknisyen atanmamış</p>
                )}
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
                  Kapat
                </Button>
                <LoadingButton href={`/projeler/${selectedAppointment.daire.blok.proje.slug}/bloklar/${selectedAppointment.daire.blok.slug}/${selectedAppointment.daire.slug}/arizalar/${selectedAppointment.ariza_id}/randevular/${selectedAppointment.id}`}>
                  Düzenle
                </LoadingButton>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
