import { MediaCategory, MediaSelectorConfig, createMediaSelectorConfig } from "@/components/media/GenericMediaSelector";

// Blog/CMS specific metadata
interface BlogMediaMetadata {
  altText?: string;
  caption?: string;
  creditLine?: string;
  seoKeywords?: string[];
}

// E-commerce specific metadata
interface ProductMediaMetadata {
  productId?: string;
  variant?: string;
  colorCode?: string;
  angle?: 'front' | 'back' | 'side' | 'detail';
  isMain?: boolean;
}

// Real Estate specific metadata
interface PropertyMediaMetadata {
  propertyId?: string;
  roomType?: 'living' | 'bedroom' | 'kitchen' | 'bathroom' | 'exterior';
  squareFootage?: number;
  timestamp?: string;
}

// 🎨 BLOG/CMS CONFIGURATION
export const blogMediaCategories: MediaCategory<BlogMediaMetadata>[] = [
  {
    id: 'blog-featured',
    key: 'blog-featured',
    name: 'Featured Images',
    title: 'Select Featured Image',
    description: 'Choose a featured image for your blog post',
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    folder: 'blog/featured',
    maxSize: 2 * 1024 * 1024, // 2MB
    targetDimensions: { width: 1200, height: 630 }
  },
  {
    id: 'blog-inline',
    key: 'blog-inline',
    name: 'Inline Images',
    title: 'Select Inline Image',
    description: 'Add images within your blog content',
    acceptedTypes: ['image/*'],
    folder: 'blog/inline',
    maxSize: 1 * 1024 * 1024, // 1MB
    targetDimensions: { width: 800, height: 400 }
  },
  {
    id: 'blog-gallery',
    key: 'blog-gallery',
    name: 'Gallery Images',
    title: 'Select Gallery Images',
    description: 'Add images to your gallery',
    acceptedTypes: ['image/*'],
    folder: 'blog/gallery',
    maxSize: 3 * 1024 * 1024, // 3MB
    targetDimensions: { width: 1024, height: 768 }
  }
];

export const blogMediaConfig = createMediaSelectorConfig(blogMediaCategories, {
  defaultCategory: 'blog-featured',
  allowMultiSelect: true,
  showMetadata: true,
  translations: {
    selectButton: 'Choose Image',
    selectTitle: 'Blog Media Library',
    selectDescription: 'Select images for your blog post'
  }
});

// 🛒 E-COMMERCE CONFIGURATION
export const ecommerceMediaCategories: MediaCategory<ProductMediaMetadata>[] = [
  {
    id: 'product-main',
    key: 'product-main',
    name: 'Main Product Images',
    title: 'Select Main Product Image',
    description: 'Primary product photo for listings',
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    folder: 'products/main',
    maxSize: 5 * 1024 * 1024, // 5MB
    targetDimensions: { width: 1200, height: 1200 }
  },
  {
    id: 'product-gallery',
    key: 'product-gallery',
    name: 'Product Gallery',
    title: 'Select Gallery Images',
    description: 'Additional product photos',
    acceptedTypes: ['image/*'],
    folder: 'products/gallery',
    maxSize: 3 * 1024 * 1024,
    targetDimensions: { width: 800, height: 800 }
  },
  {
    id: 'product-variants',
    key: 'product-variants',
    name: 'Variant Images',
    title: 'Select Variant Images',
    description: 'Images for different product variants',
    acceptedTypes: ['image/*'],
    folder: 'products/variants',
    targetDimensions: { width: 600, height: 600 }
  }
];

export const ecommerceMediaConfig = createMediaSelectorConfig(ecommerceMediaCategories, {
  defaultCategory: 'product-main',
  allowMultiSelect: true,
  showPreview: true,
  maxFileSize: 5 * 1024 * 1024,
  translations: {
    selectButton: 'Add Product Image',
    selectTitle: 'Product Media Manager',
    selectDescription: 'Manage your product images'
  }
});

// 🏠 REAL ESTATE CONFIGURATION
export const realEstateMediaCategories: MediaCategory<PropertyMediaMetadata>[] = [
  {
    id: 'property-exterior',
    key: 'property-exterior',
    name: 'Exterior Photos',
    title: 'Select Exterior Photo',
    description: 'Property exterior and landscaping photos',
    acceptedTypes: ['image/*'],
    folder: 'properties/exterior',
    targetDimensions: { width: 1200, height: 800 }
  },
  {
    id: 'property-interior',
    key: 'property-interior',
    name: 'Interior Photos',
    title: 'Select Interior Photo',
    description: 'Room and interior space photos',
    acceptedTypes: ['image/*'],
    folder: 'properties/interior',
    targetDimensions: { width: 1000, height: 750 }
  },
  {
    id: 'property-floorplan',
    key: 'property-floorplan',
    name: 'Floor Plans',
    title: 'Select Floor Plan',
    description: 'Property layout and floor plan documents',
    acceptedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
    folder: 'properties/floorplans',
    targetDimensions: { width: 1000, height: 1200 }
  }
];

export const realEstateMediaConfig = createMediaSelectorConfig(realEstateMediaCategories, {
  defaultCategory: 'property-exterior',
  allowMultiSelect: true,
  showMetadata: true,
  translations: {
    selectButton: 'Add Property Photo',
    selectTitle: 'Property Media Library',
    selectDescription: 'Manage property photos and documents'
  }
});

// 📰 NEWS/MEDIA CONFIGURATION
export const newsMediaCategories: MediaCategory[] = [
  {
    id: 'news-featured',
    key: 'news-featured',
    name: 'Featured News Images',
    title: 'Select Featured Image',
    description: 'Main image for news article',
    acceptedTypes: ['image/*'],
    folder: 'news/featured',
    targetDimensions: { width: 1200, height: 630 }
  },
  {
    id: 'news-inline',
    key: 'news-inline',
    name: 'Inline Images',
    title: 'Select Inline Image',
    description: 'Images within news content',
    acceptedTypes: ['image/*'],
    folder: 'news/inline',
    targetDimensions: { width: 800, height: 450 }
  },
  {
    id: 'news-videos',
    key: 'news-videos',
    name: 'Video Content',
    title: 'Select Video',
    description: 'Video files for news stories',
    acceptedTypes: ['video/*'],
    folder: 'news/videos',
    maxSize: 50 * 1024 * 1024 // 50MB
  }
];

export const newsMediaConfig = createMediaSelectorConfig(newsMediaCategories, {
  defaultCategory: 'news-featured',
  allowMultiSelect: false,
  showPreview: true,
  translations: {
    selectButton: 'Choose Media',
    selectTitle: 'News Media Center',
    selectDescription: 'Select media for your news article'
  }
});

// 🎓 EDUCATION/COURSE CONFIGURATION
export const educationMediaCategories: MediaCategory[] = [
  {
    id: 'course-thumbnails',
    key: 'course-thumbnails',
    name: 'Course Thumbnails',
    title: 'Select Course Thumbnail',
    description: 'Course cover image',
    acceptedTypes: ['image/*'],
    folder: 'education/thumbnails',
    targetDimensions: { width: 1280, height: 720 }
  },
  {
    id: 'lesson-materials',
    key: 'lesson-materials',
    name: 'Lesson Materials',
    title: 'Select Lesson Material',
    description: 'Images, documents for lessons',
    acceptedTypes: ['image/*', 'application/pdf', 'application/msword'],
    folder: 'education/materials'
  },
  {
    id: 'course-videos',
    key: 'course-videos',
    name: 'Course Videos',
    title: 'Select Course Video',
    description: 'Video lessons and tutorials',
    acceptedTypes: ['video/*'],
    folder: 'education/videos',
    maxSize: 100 * 1024 * 1024 // 100MB
  }
];

export const educationMediaConfig = createMediaSelectorConfig(educationMediaCategories, {
  defaultCategory: 'course-thumbnails',
  allowMultiSelect: true,
  showMetadata: true,
  translations: {
    selectButton: 'Add Media',
    selectTitle: 'Educational Media Library',
    selectDescription: 'Manage your course media files'
  }
});

// 🏢 CORPORATE CONFIGURATION (Your current use case)
export const corporateMediaCategories: MediaCategory[] = [
  {
    id: 'content-images',
    key: 'content-images',
    name: 'Content Images',
    title: 'Select Content Image',
    description: 'Images for page content',
    acceptedTypes: ['image/*'],
    folder: 'corporate/content',
    targetDimensions: { width: 800, height: 600 }
  },
  {
    id: 'project-images',
    key: 'project-images',
    name: 'Project Images',
    title: 'Select Project Image',
    description: 'Project related images',
    acceptedTypes: ['image/*'],
    folder: 'corporate/projects',
    targetDimensions: { width: 1000, height: 750 }
  },
  {
    id: 'banner-images',
    key: 'banner-images',
    name: 'Banner Images',
    title: 'Select Banner Image',
    description: 'Banner and hero images',
    acceptedTypes: ['image/*'],
    folder: 'corporate/banners',
    targetDimensions: { width: 1920, height: 600 }
  }
];

export const corporateMediaConfig = createMediaSelectorConfig(corporateMediaCategories, {
  defaultCategory: 'content-images',
  allowMultiSelect: false,
  showPreview: true,
  translations: {
    selectButton: 'Medya Seç',
    selectTitle: 'Kurumsal Medya Kütüphanesi',
    selectDescription: 'Kurumsal medya dosyalarınızı yönetin'
  }
});

// Export all configurations
export {
  type BlogMediaMetadata,
  type ProductMediaMetadata,
  type PropertyMediaMetadata
}; 