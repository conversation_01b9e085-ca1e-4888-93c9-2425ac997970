import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string }> }
) {
  try {
    const { projeSlug, blokSlug } = await params;
    // Projeyi bul
    const project = await prisma.proje.findFirst({
      where: { slug: projeSlug, silindi_mi: false },
      select: { id: true },
    });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    // Bloku bul
    const block = await prisma.blok.findFirst({
      where: { slug: blokSlug, proje_id: project.id, silindi_mi: false },
      select: { id: true },
    });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    // Daireleri getir
    const apartments = await prisma.daire.findMany({
      where: { blok_id: block.id, silindi_mi: false },
      select: {
        id: true,
        numara: true,
        slug: true,
        kat: true,
        olusturulma_tarihi: true,
      },
      orderBy: { numara: "asc" },
    });
    return NextResponse.json({ apartments });
  } catch (error) {
    console.error("Daireler getirilirken hata:", error);
    return NextResponse.json({ error: "Daireler getirilemedi" }, { status: 500 });
  }
}
