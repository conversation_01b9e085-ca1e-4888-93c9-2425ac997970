"use client"

import React, { use<PERSON>emo, useCallback } from "react"
import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Clock, 
  Wrench, 
  CheckCircle, 
  AlertTriangle,
  User,
  Building
} from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { tr } from "date-fns/locale"

const recentFaultsFetcher = async () => {
  const res = await fetch("/api/faults?limit=10")
  return res.json()
}

const recentUsersFetcher = async () => {
  const res = await fetch("/api/users?limit=5")
  return res.json()
}

const DashboardRecentActivity = React.memo(() => {
  const { data: faultsData, isLoading: faultsLoading } = useQuery({
    queryKey: ["faults", "recent"],
    queryFn: recentFaultsFetcher
  })

  const { data: usersData, isLoading: usersLoading } = useQuery({
    queryKey: ["users", "recent"],
    queryFn: recentUsersFetcher
  })

  // Memoize color functions
  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case 'Beklemede':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Devam Ediyor':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'Tamamlandı':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'İptal':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }, [])

  const getPriorityColor = useCallback((priority: string) => {
    switch (priority) {
      case 'Düşük':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'Orta':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'Yüksek':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'Kritik':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }, [])

  const getActivityIcon = useCallback((type: string) => {
    switch (type) {
      case 'fault':
        return <Wrench className="h-4 w-4" />
      case 'user':
        return <User className="h-4 w-4" />
      case 'project':
        return <Building className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }, [])

  // Memoize processed data
  const processedData = useMemo(() => {
    if (faultsLoading || usersLoading || !faultsData) {
      return {
        faults: [],
        users: []
      }
    }

    return {
      faults: faultsData.faults.slice(0, 5),
      users: usersData?.users?.slice(0, 5) || []
    }
  }, [faultsData, usersData, faultsLoading, usersLoading])

  if (faultsLoading || usersLoading || !faultsData) {
    return (
      <div className="space-y-6">
        <div className="h-64 bg-white/50 rounded-xl animate-pulse"></div>
        <div className="h-64 bg-white/50 rounded-xl animate-pulse"></div>
      </div>
    )
  }

  const { faults, users } = processedData

  return (
    <div className="space-y-6">
      {/* Recent Faults */}
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
            <Wrench className="h-5 w-5 text-blue-600" />
            Son Arızalar
          </CardTitle>
          <p className="text-sm text-gray-600">
            Son bildirilen arızalar
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {faults.map((fault: any, index: number) => (
              <div
                key={`${fault.type}-${index}`}
                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex-shrink-0">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={fault.bildiren?.resim || ""} />
                    <AvatarFallback className="text-xs">
                      {fault.bildiren_ad_soyad?.split(' ').map((n: string) => n[0]).join('') || 'U'}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {fault.baslik}
                    </p>
                    <span className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(fault.olusturulma_tarihi), { 
                        addSuffix: true, 
                        locale: tr 
                      })}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 truncate">
                    {fault.daire?.blok?.proje?.ad} - {fault.daire?.blok?.ad} {fault.daire?.numara}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className={`text-xs ${getStatusColor(fault.durum?.ad)}`}>
                      {fault.durum?.ad}
                    </Badge>
                    <Badge variant="outline" className={`text-xs ${getPriorityColor(fault.aciliyet?.ad)}`}>
                      {fault.aciliyet?.ad}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Users */}
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
            <User className="h-5 w-5 text-green-600" />
            Son Kullanıcılar
          </CardTitle>
          <p className="text-sm text-gray-600">
            Son kayıt olan kullanıcılar
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user: any, index: number) => (
              <div
                key={`${user.type}-${index}`}
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.resim || ""} />
                  <AvatarFallback className="text-xs">
                    {user.ad?.[0]}{user.soyad?.[0]}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">
                      {user.ad} {user.soyad}
                    </p>
                    <span className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(user.olusturulma_tarihi), { 
                        addSuffix: true, 
                        locale: tr 
                      })}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 truncate">
                    {user.email}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {user.rol === 'ADMIN' ? 'Yönetici' : 
                       user.rol === 'TECHNICIAN' ? 'Teknisyen' : 
                       user.rol === 'MANAGER' ? 'Müdür' : 'Kullanıcı'}
                    </Badge>
                    {user.daire && (
                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                        {user.daire.blok?.proje?.ad} {user.daire.blok?.ad} {user.daire.numara}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

DashboardRecentActivity.displayName = "DashboardRecentActivity"

export default DashboardRecentActivity 