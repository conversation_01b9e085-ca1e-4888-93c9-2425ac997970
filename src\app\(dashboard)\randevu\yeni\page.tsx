"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { 
  AlertCircle, 
  Calendar, 
  Brain, 
  Check, 
  ArrowLeft, 
  ArrowRight, 
  Zap,
  Clock,
  MapPin,
  Plus
} from "lucide-react"
import { toast } from "sonner"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { SmartTechnicianAssignment } from "@/components/appointments/smart-technician-assignment"
import { AppointmentStatus } from "@/lib/enums"
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumb<PERSON>ist,
  <PERSON>readcrumbItem,
  <PERSON>readcrumbLink,
  <PERSON><PERSON><PERSON><PERSON>bPage,
  BreadcrumbSeparator 
} from "@/components/ui/breadcrumb"

interface FaultData {
  id: string
  numara: string
  baslik: string
  kategori?: string
  oncelik?: string
  durum?: {
    ad: string
    renk: string
  }
  tip?: {
    ad: string
    renk: string
  }
  aciliyet?: {
    ad: string
    renk: string
  }
  daire: {
    numara: string
    blok: {
      ad: string
      proje: {
        ad: string
      }
    }
  }
}

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
}

export default function NewAppointmentPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [faults, setFaults] = useState<FaultData[]>([])
  const [selectedFault, setSelectedFault] = useState<FaultData | null>(null)
  const [appointmentData, setAppointmentData] = useState({
    randevu_tarihi: "",
    saat: "",
    durum: "PLANLI" as keyof typeof AppointmentStatus,
    aciklama: ""
  })
  const [selectedTechnicians, setSelectedTechnicians] = useState<string[]>([])
  
  // Follow-up appointment detection
  const [isFollowUp, setIsFollowUp] = useState(false)
  const [followUpFaultId, setFollowUpFaultId] = useState<string | null>(null)
  const [followUpAppointmentId, setFollowUpAppointmentId] = useState<string | null>(null)

  useEffect(() => {
    // URL parametrelerini kontrol et
    const urlParams = new URLSearchParams(window.location.search)
    const followUp = urlParams.get('followUp')
    const arizaId = urlParams.get('arizaId')
    const appointmentId = urlParams.get('appointmentId')
    
    if (followUp === 'true') {
      setIsFollowUp(true)
      if (arizaId) {
        setFollowUpFaultId(arizaId)
      } else if (appointmentId) {
        setFollowUpAppointmentId(appointmentId)
        // Appointment'tan arıza bilgisini al
        loadAppointmentForFault(appointmentId)
      }
    }
    
    loadFaults()
  }, [])

  useEffect(() => {
    // Follow-up randevu için arızayı otomatik seç
    if (isFollowUp && followUpFaultId && faults.length > 0) {
      const targetFault = faults.find(f => f.id === followUpFaultId)
      if (targetFault) {
        setSelectedFault(targetFault)
        toast.success("🔄 Devam randevusu için arıza otomatik seçildi", {
          description: `${targetFault.numara} - ${targetFault.baslik}`
        })
        
        // 2 saniye sonra otomatik olarak bir sonraki adıma geç
        setTimeout(() => {
          if (currentStep === 1) {
            setCurrentStep(2)
            toast.info("📅 Randevu zamanını belirleyin", {
              description: "Devam randevusu için uygun tarih ve saati seçin"
            })
          }
        }, 2000)
      }
    }
  }, [faults, isFollowUp, followUpFaultId, currentStep])

  const loadFaults = async () => {
    try {
      // Doğru status isimleri: Yeni, Atandı, Devam Ediyor, Beklemede
      const response = await fetch("/api/faults?durum=Yeni&durum=Atandı&durum=Devam Ediyor&durum=Beklemede&limit=100")
      
      if (response.ok) {
        const data = await response.json()
        
        // API response'da faults array'i var
        const faultsData = data.faults || data
        
        // Tüm açık arızaları al
        setFaults(faultsData)
      } else {
        console.error("❌ API Error:", response.status, response.statusText)
      }
    } catch (error) {
      console.error("Error loading faults:", error)
      toast.error("Arızalar yüklenirken hata oluştu")
    }
  }

  const loadAppointmentForFault = async (appointmentId: string) => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}`)
      if (response.ok) {
        const appointmentData = await response.json()
        if (appointmentData.ariza?.id) {
          setFollowUpFaultId(appointmentData.ariza.id)
          toast.info("🔄 Önceki randevudan arıza bilgisi alınıyor...", {
            description: `Arıza #${appointmentData.ariza.numara} için devam randevusu`
          })
        }
      }
    } catch (error) {
      console.error("Error loading appointment for fault:", error)
      toast.error("Önceki randevu bilgisi alınamadı")
    }
  }

  const handleNext = () => {
    if (currentStep === 1 && !selectedFault) {
      toast.error("Lütfen bir arıza seçin")
      return
    }
    if (currentStep === 2 && (!appointmentData.randevu_tarihi || !appointmentData.saat)) {
      toast.error("Lütfen randevu tarih ve saatini girin")
      return
    }
    if (currentStep === 3 && selectedTechnicians.length === 0) {
      toast.error("Lütfen en az bir teknisyen seçin")
      return
    }
    
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleTechnicianAssignment = (technicians: Technician[]) => {
    const technicianIds = technicians.map(t => t.id)
    setSelectedTechnicians(technicianIds)
    setCurrentStep(4) // Skip to confirmation
  }

  const handleSubmit = async () => {
    if (!selectedFault) {
      toast.error("Arıza seçimi gerekli")
      return
    }

    try {
      setLoading(true)
      
      const appointmentDateTime = new Date(`${appointmentData.randevu_tarihi}T${appointmentData.saat}`)
      
      const response = await fetch("/api/appointments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ariza_id: selectedFault.id,
          randevu_tarihi: appointmentDateTime.toISOString(),
          durum: appointmentData.durum,
          aciklama: appointmentData.aciklama,
          teknisyen_ids: selectedTechnicians,
          // Devam randevusu bilgileri
          onceki_randevu_id: isFollowUp ? followUpAppointmentId : null,
          devam_randevusu_mu: isFollowUp
        })
      })

      if (response.ok) {
        const result = await response.json()
        
        if (isFollowUp && result._meta?.onceki_randevu_kapatildi) {
          toast.success("🔄 Devam randevusu başarıyla oluşturuldu!", {
            description: "✅ Önceki randevu otomatik olarak 'Tamamlandı' durumuna geçirildi",
            duration: 5000
          })
        } else if (isFollowUp) {
          toast.success("🔄 Devam randevusu başarıyla oluşturuldu!", {
            description: "Önceki çalışmanın devamı olarak randevu planlandı",
            duration: 4000
          })
        } else {
          toast.success("🎉 Randevu başarıyla oluşturuldu!", {
            description: "Teknisyen ataması ve arıza eşleştirmesi tamamlandı",
            duration: 4000
          })
        }
        
        // Debug: Console'da önceki randevu durumunu göster
        if (result._meta?.onceki_randevu_kapatildi) {
        }
        
        router.push("/randevu")
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || "Randevu oluşturulurken hata oluştu")
      }
    } catch (error) {
      console.error("Error creating appointment:", error)
      toast.error("Randevu oluşturulurken hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Steps configuration - moved after state variables
  const steps = [
    { 
      id: 1, 
      title: isFollowUp ? "📋 Devam Arızası" : "📋 Arıza Seçimi", 
      description: isFollowUp ? "Devam edilecek arıza otomatik seçildi" : "Randevu oluşturulacak arızayı seçin"
    },
    { 
      id: 2, 
      title: "📅 Randevu Planlama", 
      description: isFollowUp ? "Devam randevusu için uygun zaman seçin" : "Tarih ve saat belirleyin"
    },
    { 
      id: 3, 
      title: "👥 Teknisyen Ataması", 
      description: isFollowUp ? "Aynı veya yeni teknisyen atayın" : "Uygun teknisyenleri seçin"
    },
    { 
      id: 4, 
      title: "✅ Onay", 
      description: isFollowUp ? "Devam randevusunu onayla" : "Randevu bilgilerini kontrol edin"
    }
  ]

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50">
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                {isFollowUp ? "🔄 Devam Arızası Seçimi" : "📋 Arıza Seçimi"}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {isFollowUp 
                  ? "Devam randevusu için arıza otomatik seçildi. İsterseniz farklı bir arıza seçebilirsiniz."
                  : "Randevu oluşturmak istediğiniz açık arızayı seçin"
                }
              </p>
              {isFollowUp && selectedFault && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-800">Otomatik Seçildi</span>
                  </div>
                  <p className="text-xs text-green-700">
                    {selectedFault.numara} - {selectedFault.baslik}
                  </p>
                  <p className="text-xs text-green-600 mt-1">
                    ⏱️ 2 saniye sonra otomatik olarak sonraki adıma geçecek...
                  </p>
                </div>
              )}
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {faults.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <AlertCircle className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>Randevu oluşturulacak açık arıza bulunamadı</p>
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => router.push("/arizalar/yeni")}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Yeni Arıza Oluştur
                    </Button>
                  </div>
                ) : (
                  faults.map((fault) => (
                    <div
                      key={fault.id}
                      className={`p-4 border rounded-xl cursor-pointer transition-all duration-300 hover:shadow-md ${
                        selectedFault?.id === fault.id
                          ? "border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200"
                          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                      }`}
                      onClick={() => setSelectedFault(fault)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="font-semibold text-lg">{fault.numara}</span>
                            {fault.tip?.ad && (
                              <Badge variant="outline" className="text-xs">
                                {fault.tip.ad}
                              </Badge>
                            )}
                            {fault.aciliyet?.ad && (
                              <Badge 
                                variant={
                                  (fault.aciliyet.ad === "Yüksek" || fault.aciliyet.ad === "Kritik") 
                                    ? "destructive" 
                                    : "secondary"
                                }
                                className="text-xs"
                              >
                                {fault.aciliyet.ad}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm mb-3 font-medium">{fault.baslik}</p>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <MapPin className="h-3 w-3" />
                            {fault.daire.blok.proje.ad} - {fault.daire.blok.ad} - Daire {fault.daire.numara}
                          </div>
                        </div>
                        {selectedFault?.id === fault.id && (
                          <div className="ml-4">
                            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                              <Check className="w-4 h-4 text-white" />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        )

      case 2:
        return (
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Randevu Detayları
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Randevu tarih, saat ve ek bilgileri girin
              </p>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {selectedFault && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold mb-2 text-blue-800">📋 Seçilen Arıza:</h3>
                    <p className="text-sm text-blue-700">{selectedFault.numara} - {selectedFault.baslik}</p>
                    <p className="text-xs text-blue-600 mt-1">
                      {selectedFault.daire.blok.proje.ad} - {selectedFault.daire.blok.ad} - Daire {selectedFault.daire.numara}
                    </p>
                  </div>
                )}
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="randevu_tarihi" className="text-sm font-medium">
                      📅 Randevu Tarihi
                    </Label>
                    <Input
                      id="randevu_tarihi"
                      type="date"
                      value={appointmentData.randevu_tarihi}
                      onChange={(e) => setAppointmentData(prev => ({
                        ...prev,
                        randevu_tarihi: e.target.value
                      }))}
                      min={new Date().toISOString().split('T')[0]}
                      className="border-2 focus:border-blue-500"
                    />
                    <p className="text-xs text-muted-foreground">
                      En erken bugün seçilebilir
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="saat" className="text-sm font-medium">
                      🕐 Randevu Saati
                    </Label>
                    <Input
                      id="saat"
                      type="time"
                      value={appointmentData.saat}
                      onChange={(e) => setAppointmentData(prev => ({
                        ...prev,
                        saat: e.target.value
                      }))}
                      className="border-2 focus:border-blue-500"
                    />
                    <p className="text-xs text-muted-foreground">
                      Mesai saatleri: 08:00 - 18:00
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="durum" className="text-sm font-medium">
                    📊 Randevu Durumu
                  </Label>
                  <Select
                    value={appointmentData.durum}
                    onValueChange={(value) => setAppointmentData(prev => ({
                      ...prev,
                      durum: value as keyof typeof AppointmentStatus
                    }))}
                  >
                    <SelectTrigger className="border-2 focus:border-blue-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PLANLI">📋 Planlı</SelectItem>
                      <SelectItem value="DEVAM_EDIYOR">🔄 Devam Ediyor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="aciklama" className="text-sm font-medium">
                    📝 Ek Açıklama (Opsiyonel)
                  </Label>
                  <Textarea
                    id="aciklama"
                    placeholder="Randevu ile ilgili özel notlar, malzeme gereksinimleri veya dikkat edilmesi gereken konular..."
                    value={appointmentData.aciklama}
                    onChange={(e) => setAppointmentData(prev => ({
                      ...prev,
                      aciklama: e.target.value
                    }))}
                    rows={4}
                    className="border-2 focus:border-blue-500 resize-none"
                  />
                  <p className="text-xs text-muted-foreground">
                    Bu bilgiler teknisyenlere iletilecektir
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )

      case 3:
        return selectedFault ? (
          <SmartTechnicianAssignment
            faultCategory={selectedFault.tip?.ad || "Belirtilmemiş"}
            faultPriority={selectedFault.aciliyet?.ad || "Orta"}
            faultLocation={`${selectedFault.daire.blok.proje.ad} - ${selectedFault.daire.blok.ad}`}
            onTechniciansSelected={handleTechnicianAssignment}
            allowMultiple={true}
          />
        ) : null

      case 4:
        return (
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50">
              <CardTitle className="flex items-center gap-2">
                <Check className="h-5 w-5 text-green-600" />
                Randevu Özeti ve Onay
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Tüm bilgileri kontrol edin ve randevuyu oluşturun
              </p>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Check className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-green-800">✅ Randevu hazır!</span>
                  </div>
                  <p className="text-sm text-green-700">
                    Tüm bilgiler doğru mu? Onaylamak için "Randevu Oluştur" butonuna tıklayın.
                  </p>
                </div>

                {selectedFault && (
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      🔧 Arıza Bilgileri:
                    </h3>
                    <div className="bg-gray-50 p-4 rounded-lg space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Arıza No:</span>
                        <span className="font-medium">{selectedFault.numara}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Başlık:</span>
                        <span className="font-medium">{selectedFault.baslik}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Kategori:</span>
                        <Badge variant="outline">{selectedFault.kategori}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Öncelik:</span>
                        <Badge 
                          variant={selectedFault.oncelik === "YUKSEK" || selectedFault.oncelik === "KRITIK" ? "destructive" : "secondary"}
                        >
                          {selectedFault.oncelik}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Konum:</span>
                        <span className="font-medium text-right">
                          {selectedFault.daire.blok.proje.ad}<br />
                          {selectedFault.daire.blok.ad} - Daire {selectedFault.daire.numara}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                <Separator />

                <div>
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    📅 Randevu Bilgileri:
                  </h3>
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Tarih:</span>
                      <span className="font-medium">
                        {new Date(appointmentData.randevu_tarihi).toLocaleDateString("tr-TR", {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Saat:</span>
                      <span className="font-medium text-blue-600">{appointmentData.saat}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Durum:</span>
                      <Badge variant="secondary">{appointmentData.durum}</Badge>
                    </div>
                    {appointmentData.aciklama && (
                      <div>
                        <span className="text-muted-foreground">Açıklama:</span>
                        <p className="mt-1 p-3 bg-white rounded text-sm border">
                          {appointmentData.aciklama}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    👨‍🔧 Atanan Teknisyenler:
                  </h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <Badge variant="secondary" className="text-base py-2 px-4">
                      {selectedTechnicians.length} teknisyen seçildi
                    </Badge>
                    {selectedTechnicians.length === 0 && (
                      <p className="text-sm text-muted-foreground mt-2">
                        Teknisyen ataması sonradan yapılabilir
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )

      default:
        return null
    }
  }

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Ana Sayfa</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/randevu">Randevular</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>
              {isFollowUp ? "Devam Randevusu" : "Yeni Randevu"}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isFollowUp ? (
              <>
                🔄 Devam Randevusu Oluştur
                <Badge variant="secondary" className="ml-3">
                  Otomatik Planlama
                </Badge>
              </>
            ) : (
              "📅 Yeni Randevu Oluştur"
            )}
          </h1>
          <p className="text-muted-foreground mt-2">
            {isFollowUp 
              ? "Önceki randevu sonrası devam randevusu otomatik olarak planlanıyor"
              : "Arıza için teknisyen randevusu planlayın"
            }
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.back()}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Geri Dön
        </Button>
      </div>

      {/* Follow-up Info Banner */}
      {isFollowUp && (
        <div className="mb-6 p-4 bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="h-5 w-5 text-orange-600" />
            <span className="font-semibold text-orange-800">🔄 Devam Randevusu Planlama</span>
          </div>
          <p className="text-sm text-orange-700">
            Bu randevu, önceki çalışmanın devamı olarak otomatik planlanıyor. 
            Arıza bilgileri ve teknisyen tercihleri önceki randevudan alınacak.
          </p>
        </div>
      )}

      {/* Steps Indicator */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-blue-50 to-purple-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full transition-all ${
                    currentStep > step.id ? "bg-green-600 text-white shadow-lg" :
                    currentStep === step.id ? "bg-blue-600 text-white shadow-lg" :
                    "bg-gray-200 text-gray-600"
                  }`}>
                    {currentStep > step.id ? (
                      <Check className="w-6 h-6" />
                    ) : (
                      <Calendar className="w-6 h-6" />
                    )}
                  </div>
                  <div className="mt-3 text-center">
                    <p className={`text-sm font-medium ${
                      currentStep >= step.id ? "text-gray-900" : "text-gray-500"
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {step.description}
                    </p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`mx-8 flex-1 h-1 rounded transition-all ${
                    currentStep > step.id ? "bg-green-600" : "bg-gray-200"
                  }`} />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <div className="min-h-[500px]">
        {renderStepContent()}
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6 border-t">
        <Button
          variant="outline"
          onClick={handleBack}
          disabled={currentStep === 1}
          className="px-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Önceki
        </Button>

        {currentStep < steps.length ? (
          <Button 
            onClick={handleNext}
            className="px-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            Sonraki
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        ) : (
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="px-6 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
          >
            {loading ? (
              <>
                <Clock className="w-4 h-4 mr-2 animate-spin" />
                Oluşturuluyor...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4 mr-2" />
                🎉 Randevu Oluştur
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  )
} 