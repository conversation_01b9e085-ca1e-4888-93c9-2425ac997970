#!/bin/bash

echo "🚀 ML Fault Classifier Docker Container Başlatılıyor..."

# Container'ı çalıştır
echo "🐳 Container başlatılıyor..."
docker run -d \
  --name ml-fault-classifier \
  --restart unless-stopped \
  -p 3050:3050 \
  -e NODE_ENV=production \
  -e PORT=3050 \
  -e MAIN_APP_URL=http://host.docker.internal:3001 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/models:/app/models \
  ml-fault-classifier:latest

if [ $? -eq 0 ]; then
    echo "✅ Container başarıyla başlatıldı!"
    echo "   Container: ml-fault-classifier"
    echo "   Port: 3050"
    echo "   URL: http://localhost:3050"
    echo ""
    echo "📊 Durum kontrolü:"
    echo "   docker logs ml-fault-classifier"
    echo "   docker ps"
    echo ""
    echo "🛑 Durdurmak için:"
    echo "   docker stop ml-fault-classifier"
    echo "   docker rm ml-fault-classifier"
else
    echo "❌ Container başlatma hatası!"
    exit 1
fi 