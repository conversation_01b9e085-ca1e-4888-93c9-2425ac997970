import fetch from 'node-fetch'

const API_URL = 'http://localhost:3000/api'

async function main() {
  try {
    console.log('🔍 Test başlatılıyor...')

    // 1. Arıza kaydı oluştur
    const faultResponse = await fetch(`${API_URL}/faults`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        baslik: '[TEST] Arıza <PERSON>dı',
        aciklama: 'Bu bir test arızasıdır',
        daire_id: 'cmcgncjve0001uerkqcbzoo3p', // Test dairesi ID'si
        ariza_tip_id: 'cmcgkm9oi0004ue9s2iqbxwxw', // Elektrik arıza tipi ID'si
        aciliyet_id: 'cmcgkm9oi0008ue9s6aqbxwxw', // Normal aciliyet ID'si
        bildiren_ad_soyad: 'Test Kullanıcı',
        bildiren_telefon: '5551234567'
      })
    })

    if (!faultResponse.ok) {
      throw new Error(`Arıza oluşturma hatası: ${faultResponse.statusText}`)
    }

    const fault = await faultResponse.json()
    console.log(`✅ Test arızası oluşturuldu: ${fault.id}`)

    // 2. Randevu oluştur
    const appointmentResponse = await fetch(`${API_URL}/appointments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ariza_id: fault.id,
        randevu_tarihi: new Date().toISOString(),
        teknisyenler: ['cmcgkm9p50016ue9s8wqbxwxw'] // Test teknisyeni ID'si
      })
    })

    if (!appointmentResponse.ok) {
      throw new Error(`Randevu oluşturma hatası: ${appointmentResponse.statusText}`)
    }

    const appointment = await appointmentResponse.json()
    console.log(`✅ Test randevusu oluşturuldu: ${appointment.id}`)

    // 3. Randevuyu tamamla
    const completionResponse = await fetch(`${API_URL}/appointments/${appointment.id}/sonuc`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        durum: 'TAMAMEN_COZULDU',
        ariza_cozuldu_mu: true,
        tamamlanma_orani: 100,
        sonraki_randevu_gerekli: false,
        teknisyen_notlari: 'Test randevusu başarıyla tamamlandı',
        musteri_memnuniyet: 5,
        musteri_yorumu: 'Test yorumu',
        gercek_baslangic: new Date().toISOString(),
        gercek_bitis: new Date().toISOString(),
        toplam_sure_dk: 60
      })
    })

    if (!completionResponse.ok) {
      throw new Error(`Randevu tamamlama hatası: ${completionResponse.statusText}`)
    }

    console.log(`✅ Randevu sonucu kaydedildi`)

    // 4. Arıza durumunu kontrol et
    const faultCheckResponse = await fetch(`${API_URL}/faults/${fault.id}`)
    if (!faultCheckResponse.ok) {
      throw new Error(`Arıza kontrol hatası: ${faultCheckResponse.statusText}`)
    }

    const updatedFault = await faultCheckResponse.json()
    console.log(`\n📊 Test Sonuçları:`)
    console.log(`- Arıza Durumu: ${updatedFault.durum.ad}`)
    console.log(`- Beklenen: Çözüldü`)
    
    const testBasarili = updatedFault.durum.ad === 'Çözüldü'
    console.log(`\n${testBasarili ? '✅ TEST BAŞARILI' : '❌ TEST BAŞARISIZ'}`)

  } catch (error) {
    console.error('❌ Test sırasında hata oluştu:', error)
  }
}

main() 