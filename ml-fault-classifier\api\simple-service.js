const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const KeywordClassifier = require('../models/keyword-classifier');

const app = express();
const PORT = process.env.PORT || 3050;

// Middleware
app.use(cors());
app.use(express.json());

// Global classifier instance
let classifier = null;
let isModelLoaded = false;

// Model yükleme fonksiyonu
async function loadModel() {
  try {
    console.log('🤖 ML Model yükleniyor...');
    
    // Training data'yı yükle
    const trainingDataPath = path.join(__dirname, '../data/training-data.json');
    const trainingData = JSON.parse(fs.readFileSync(trainingDataPath, 'utf8'));
    
    // Classifier oluştur ve eğit
    classifier = new KeywordClassifier();
    classifier.setConfidenceThreshold(0.1);
    classifier.train(trainingData);
    
    isModelLoaded = true;
    console.log('✅ ML Model başarıyla yüklendi!');
    
    return true;
  } catch (error) {
    console.error('❌ Model yükleme hatası:', error.message);
    return false;
  }
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    modelLoaded: isModelLoaded,
    service: 'ML Fault Classifier API',
    version: '1.0.0',
    port: PORT
  });
});

// Model durumu endpoint
app.get('/status', (req, res) => {
  if (!isModelLoaded || !classifier) {
    return res.status(503).json({
      error: 'Model henüz yüklenmedi',
      status: 'loading'
    });
  }
  
  const status = classifier.getStatus();
  res.json({
    status: 'ready',
    model: status,
    timestamp: new Date().toISOString()
  });
});

// Kategori tahmini endpoint
app.post('/predict', async (req, res) => {
  try {
    if (!isModelLoaded || !classifier) {
      return res.status(503).json({
        error: 'Model henüz yüklenmedi',
        status: 'loading'
      });
    }
    
    const { title, description } = req.body;
    
    if (!title || !description) {
      return res.status(400).json({
        error: 'Başlık ve açıklama gereklidir',
        required: ['title', 'description']
      });
    }
    
    // Tahmin yap
    const prediction = classifier.predict(title, description);
    
    res.json({
      success: true,
      prediction: prediction,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Tahmin hatası:', error.message);
    res.status(500).json({
      error: 'Tahmin sırasında hata oluştu',
      message: error.message
    });
  }
});

// Mevcut kategoriler endpoint
app.get('/categories', (req, res) => {
  try {
    if (!isModelLoaded || !classifier) {
      return res.status(503).json({
        error: 'Model henüz yüklenmedi',
        status: 'loading'
      });
    }
    
    const status = classifier.getStatus();
    const categories = Object.keys(status.categories).map(category => ({
      name: category,
      id: classifier.categories.get(category),
      sampleCount: status.categories[category]
    }));
    
    res.json({
      success: true,
      categories: categories,
      total: categories.length,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Kategori listesi hatası:', error.message);
    res.status(500).json({
      error: 'Kategori listesi alınırken hata oluştu',
      message: error.message
    });
  }
});

// Test endpoint
app.get('/test', (req, res) => {
  res.json({
    message: 'ML Fault Classifier API çalışıyor!',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Server başlatma
async function startServer() {
  try {
    // Modeli yükle
    const modelLoaded = await loadModel();
    
    if (!modelLoaded) {
      console.error('❌ Model yüklenemedi, sunucu başlatılamıyor');
      process.exit(1);
    }
    
    // Server'ı başlat
    app.listen(PORT, () => {
      console.log(`🚀 ML Fault Classifier API başlatıldı!`);
      console.log(`   📍 Port: ${PORT}`);
      console.log(`   🔗 URL: http://localhost:${PORT}`);
      console.log(`   📊 Health Check: http://localhost:${PORT}/health`);
      console.log(`   🤖 Model Durumu: http://localhost:${PORT}/status`);
      console.log(`   🎯 Tahmin API: http://localhost:${PORT}/predict`);
      console.log(`   🧪 Test: http://localhost:${PORT}/test`);
      console.log('');
      console.log('✅ API hazır! Mevcut projeye entegre edilebilir.');
    });
    
  } catch (error) {
    console.error('❌ Server başlatma hatası:', error.message);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Server kapatılıyor...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Server kapatılıyor...');
  process.exit(0);
});

// Server'ı başlat
startServer(); 