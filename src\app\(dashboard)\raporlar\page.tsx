"use client"

import Link from "next/link"
import { 
  BarChart3, 
  FileText, 
  TrendingUp, 
  Users, 
  Package, 
  AlertTriangle, 
  Download,
  Calendar,
  Filter,
  ArrowRight
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Breadcrumb } from "@/components/layout/breadcrumb"

const reportModules = [
  {
    title: "Arıza Raporları",
    description: "Arıza istatistikleri, trend analizi ve performans raporları",
    href: "/raporlar/ariza",
    icon: AlertTriangle,
    color: "bg-red-500",
    stats: "124 toplam arıza",
    features: ["Aylık trend analizi", "Kategori bazlı dağılım", "Durum raporları"]
  },
  {
    title: "Teknisyen Raporları",
    description: "Teknisyen performansı, iş yükü ve verimlilik analizi",
    href: "/raporlar/teknisyen",
    icon: Users,
    color: "bg-blue-500",
    stats: "8 aktif teknisyen",
    features: ["Performans metrikleri", "İş yükü analizi", "Uzmanlık raporları"]
  },
  {
    title: "Malzeme Raporları",
    description: "Malzeme kullanımı, stok durumu ve maliyet analizi",
    href: "/raporlar/malzeme",
    icon: Package,
    color: "bg-green-500",
    stats: "45 farklı malzeme",
    features: ["Kullanım raporları", "Stok analizi", "Maliyet takibi"]
  }
]

const quickReports = [
  {
    title: "Günlük Özet",
    description: "Bugünkü arıza ve randevu özeti",
    type: "PDF",
    size: "1.2 MB",
    date: "Bugün"
  },
  {
    title: "Haftalık Analiz",
    description: "Bu haftanın performans raporu",
    type: "Excel", 
    size: "2.8 MB",
    date: "29.06.2025"
  },
  {
    title: "Aylık Dashboard",
    description: "Haziran ayı kapsamlı raporu",
    type: "PDF",
    size: "4.1 MB",
    date: "28.06.2025"
  }
]

export default function ReportsPage() {
  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            📊 Raporlar & Analiz
          </h1>
          <p className="text-muted-foreground mt-1">
            Detaylı raporlar oluşturun ve sistem analizlerini görüntüleyin
          </p>
        </div>
        <Button className="gap-2">
          <Download className="h-4 w-4" />
          Özel Rapor Oluştur
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Toplam Rapor</p>
                <p className="text-2xl font-bold text-blue-900">142</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Bu Ay</p>
                <p className="text-2xl font-bold text-green-900">28</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Otomatik</p>
                <p className="text-2xl font-bold text-orange-900">15</p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">İndirilen</p>
                <p className="text-2xl font-bold text-purple-900">89</p>
              </div>
              <Download className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Report Modules */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                Rapor Modülleri
              </CardTitle>
              <CardDescription>
                Detaylı analiz ve raporlama araçları
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {reportModules.map((module) => {
                const Icon = module.icon
                return (
                  <Link key={module.title} href={module.href}>
                    <Card className="hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-200 hover:border-blue-300">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4">
                            <div className={`p-3 rounded-lg ${module.color}`}>
                              <Icon className="h-6 w-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold text-lg mb-1">{module.title}</h3>
                              <p className="text-muted-foreground text-sm mb-3">{module.description}</p>
                              <div className="flex flex-wrap gap-2">
                                {module.features.map((feature, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {feature}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <ArrowRight className="h-5 w-5 text-muted-foreground mb-2" />
                            <p className="text-sm text-muted-foreground">{module.stats}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                )
              })}
            </CardContent>
          </Card>
        </div>

        {/* Quick Reports Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-green-600" />
                Hızlı Raporlar
              </CardTitle>
              <CardDescription>
                Hazır raporları hemen indirin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {quickReports.map((report, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer border"
                >
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                      <FileText className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">{report.title}</p>
                      <p className="text-xs text-muted-foreground">{report.description}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {report.type}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {report.size} • {report.date}
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-purple-600" />
                Hızlı Filtreler
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="mr-2 h-4 w-4" />
                Bu Hafta
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <TrendingUp className="mr-2 h-4 w-4" />
                Yüksek Performans
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <AlertTriangle className="mr-2 h-4 w-4" />
                Kritik Durum
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 