"use client"

import { useState } from "react"
import { Alert<PERSON>riangle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"

interface Status {
  id: string
  ad: string
  sira: number
  renk: string
  aciklama?: string
}

interface DeleteStatusDialogProps {
  status?: Status | null
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export function DeleteStatusDialog({ status, isOpen, onClose, onSuccess }: DeleteStatusDialogProps) {
  const [loading, setLoading] = useState(false)

  if (!status) return null

  const handleDelete = async () => {
    try {
      setLoading(true)

      const response = await fetch(`/api/statuses/${status.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Silme işlemi başarısız")
      }

      toast.success("Arıza durumu başarıyla silindi")
      onSuccess()
    } catch (error) {
      console.error("Status delete error:", error)
      toast.error(error instanceof Error ? error.message : "Silme işlemi başarısız")
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (color: string) => {
    return {
      backgroundColor: `${color}20`,
      borderColor: color,
      color: color
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Arıza Durumu Sil
          </DialogTitle>
          <DialogDescription>
            Bu işlem geri alınamaz. Arıza durumu kalıcı olarak silinecektir.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border p-4 bg-muted/50">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Sıra:</span>
                <span className="font-mono font-bold">{status.sira}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Ad:</span>
                <span className="font-medium">{status.ad}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Renk:</span>
                <Badge
                  variant="outline"
                  style={getStatusColor(status.renk)}
                  className="border-2"
                >
                  {status.renk}
                </Badge>
              </div>
              {status.aciklama && (
                <div className="space-y-1">
                  <span className="text-sm font-medium">Açıklama:</span>
                  <p className="text-sm text-muted-foreground">{status.aciklama}</p>
                </div>
              )}
            </div>
          </div>

          <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-destructive mt-0.5 flex-shrink-0" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-destructive">
                  Dikkat!
                </p>
                <p className="text-sm text-destructive/80">
                  Bu durumu kullanan arızalar veya geçmiş kayıtlar varsa silme işlemi başarısız olacaktır. 
                  Önce ilgili arızaların durumunu değiştirmeniz gerekir.
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            İptal
          </Button>
          <Button 
            type="button" 
            variant="destructive" 
            onClick={handleDelete}
            disabled={loading}
          >
            {loading ? "Siliniyor..." : "Sil"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 