# 🍞 Breadcrumb Sistemi Kullanım Kılavuzu

## 📋 Genel Bilgi

Bu proje, dinamik ve gelecek-uyu<PERSON>lu (future-proof) bir breadcrumb sistemi içerir. Sistem otomatik olarak URL'ye göre breadcrumb'ları oluşturur ve tüm sayfalarda tutarlı navigasyon sağlar.

## 🏗️ Sistem Mimarisi

### 1. **Otomatik Breadcrumb Sistemi**
- `useBreadcrumb` hook'u URL'yi analiz eder
- Türkçe etiket mapping'i yapar
- Dynamic content için API çağrıları yapar
- UUID pattern'ini tanıyarak entity isimlerini getirir

### 2. **DashboardLayout Entegrasyonu**  
- Tüm management sayfalarında otomatik breadcrumb
- Üst kısımda beyaz arka plan ile ayrılmış alan
- Responsive tasarım

### 3. **Dinamik İçerik Desteği**
- Proje, Blok, Daire, Ar<PERSON>za detayları için API çağrıları
- UUID detection ile otomatik entity ismi çözümleme
- Error handling ile fallback değerler

## 🚀 Kullanım Örnekleri

### Otomatik Breadcrumb (Önerilen)
```tsx
// DashboardLayout otomatik olarak breadcrumb ekler
export default function MyPage() {
  return (
    <DashboardLayout>
      <h1>İçerik</h1>
    </DashboardLayout>
  )
}
```

### Manuel Breadcrumb (Özel durumlar için)
```tsx
import { Breadcrumb } from "@/components/ui/breadcrumb"

export default function SpecialPage() {
  const customBreadcrumbs = [
    { label: "Dashboard", href: "/dashboard", isActive: false },
    { label: "Özel Sayfa", isActive: true }
  ]
  
  return (
    <div>
      <Breadcrumb customItems={customBreadcrumbs} />
      <h1>Özel sayfa içeriği</h1>
    </div>
  )
}
```

## 🗺️ URL Mapping Tablosu

| URL Segment | Türkçe Etiket |
|-------------|---------------|
| `dashboard` | Dashboard |
| `projeler` | Proje Yönetimi |
| `bloklar` | Blok Yönetimi |
| `daireler` | Daire Yönetimi |
| `ariza` | Arıza Yönetimi |
| `yeni` | Yeni Arıza |
| `duzenle` | Düzenle |

## 🔧 API Endpoint'leri

Sistem şu API endpoint'lerini kullanır:

- `/api/projects/[id]` → `data.ad`
- `/api/blocks/[id]` → `data.ad`
- `/api/apartments/[id]` → `data.numara`
- `/api/faults/[id]` → `data.baslik`

## 🎨 Stil Özelleştirmeleri

### Separator Türleri
```tsx
import { BreadcrumbSeparators } from "@/components/ui/breadcrumb"

// Chevron (varsayılan)
<Breadcrumb separator={BreadcrumbSeparators.chevron} />

// Slash
<Breadcrumb separator={BreadcrumbSeparators.slash} />

// Dot
<Breadcrumb separator={BreadcrumbSeparators.dot} />

// Arrow
<Breadcrumb separator={BreadcrumbSeparators.arrow} />
```

### Preset Components
```tsx
// Simple (ev ikonu yok)
<SimpleBreadcrumb />

// Icon (ev ikonu var)
<IconBreadcrumb />

// Slash separator
<SlashBreadcrumb />
```

## 🔄 Gelecek Sayfalar için Ekleme

Yeni bir sayfa eklediğinizde:

1. **URL segment'ini** `pathMapping` objesine ekleyin:
```typescript
// hooks/use-breadcrumb.ts
const pathMapping: Record<string, string> = {
  // ...mevcut mapping'ler
  'yeni-modul': 'Yeni Modül Adı'
}
```

2. **Dynamic content** varsa API case'ini ekleyin:
```typescript
// hooks/use-breadcrumb.ts  
switch (type) {
  // ...mevcut case'ler
  case 'yeni-modul':
    endpoint = `/api/yeni-modul/${id}`
    break
}
```

3. **DashboardLayout** kullanın → Breadcrumb otomatik eklenir!

## 📱 Responsive Özellikler

- Uzun etiketler mobilde kesilir (`max-w-[200px]`)
- Desktop'ta tam genişlik (`md:max-w-none`)
- Loading animation desteği
- Flexible separator sistemi

## 🚧 Bilinen Sınırlar

1. **Client-side Only**: SSR desteği şu anda yok
2. **API Dependency**: Dynamic content için API'lerin çalışır olması gerekli
3. **URL Structure**: Nested routing için ek konfigürasyon gerekebilir

## 🔮 Gelecek Özellikler

- [ ] SSR desteği
- [ ] Breadcrumb cache sistemi  
- [ ] Çoklu dil desteği
- [ ] Advanced customization options
- [ ] Analytics integration

---

**Not**: Bu sistem tamamen otomatik çalışır ve gelecekteki tüm sayfalarda otomatik olarak breadcrumb sağlar. Yeni modül eklerken sadece URL mapping'ini güncellemeniz yeterlidir!
