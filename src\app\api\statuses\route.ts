import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { CacheManager } from "@/lib/cache"

// Validation schema
const statusSchema = z.object({
  ad: z.string().min(1, "Ad alanı gereklidir"),
  sira: z.number().min(1, "Sıra 1'den büyük olmalıdır"),
  renk: z.string().regex(/^#[0-9A-F]{6}$/i, "Geçerli bir hex renk kodu giriniz"),
  aciklama: z.string().optional(),
})

// GET - List all statuses
export async function GET() {
  try {
    const cacheKey = CacheManager.generateKey("statuses", { all: "true" })

    // Try to get from cache first
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for statuses: ${cacheKey}`)
      return NextResponse.json(cached)
    }

    console.log(`Cache miss for statuses: ${cacheKey}`)

    const statuses = await prisma.arizaDurum.findMany({
      where: {
        silindi_mi: false,
      },
      orderBy: {
        sira: "asc",
      },
    })

    const result = {
      statuses,
      total: statuses.length,
    }

    // Cache the result for 15 minutes
    await CacheManager.set(cacheKey, result, 900)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching statuses:", error)
    return NextResponse.json(
      { message: "Arıza durumları yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

// POST - Create new status
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate data
    const validatedData = statusSchema.parse(body)

    // Check if order already exists
    const existingOrder = await prisma.arizaDurum.findFirst({
      where: {
        sira: validatedData.sira,
        silindi_mi: false,
      },
    })

    if (existingOrder) {
      return NextResponse.json(
        { message: `${validatedData.sira} sıra numarası zaten kullanılıyor` },
        { status: 400 }
      )
    }

    // Check if name already exists
    const existingName = await prisma.arizaDurum.findFirst({
      where: {
        ad: validatedData.ad,
        silindi_mi: false,
      },
    })

    if (existingName) {
      return NextResponse.json(
        { message: `"${validatedData.ad}" adı zaten kullanılıyor` },
        { status: 400 }
      )
    }

    const status = await prisma.arizaDurum.create({
      data: {
        ...validatedData,
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date(),
      },
    })

    // Invalidate statuses cache after creating new status
    await CacheManager.deletePattern("statuses:*")

    return NextResponse.json(status, { status: 201 })
  } catch (error) {
    console.error("Error creating status:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          message: "Geçersiz veri",
          errors: error.errors 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Arıza durumu oluşturulurken hata oluştu" },
      { status: 500 }
    )
  }
} 