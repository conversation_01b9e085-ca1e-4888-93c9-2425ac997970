{"name": "ml-fault-classifier", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node api/simple-service.js", "dev": "nodemon api/prediction-service.js", "train": "node scripts/train-model.js", "test-model": "node scripts/test-keyword-classifier.js", "test-integration": "node scripts/test-integration.js", "improve": "node scripts/improve-model.js", "generate-data": "node scripts/generate-training-data.js", "docker:build": "chmod +x scripts/docker-build.sh && ./scripts/docker-build.sh", "docker:run": "chmod +x scripts/docker-run.sh && ./scripts/docker-run.sh", "docker:compose": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:logs": "docker logs ml-fault-classifier -f", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {}, "devDependencies": {"nodemon": "^3.1.10"}}