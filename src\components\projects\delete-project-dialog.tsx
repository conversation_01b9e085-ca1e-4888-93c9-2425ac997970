"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Project {
  id: string
  ad: string
  _count?: {
    bloklar: number
    arizalar: number
  }
}

interface DeleteProjectDialogProps {
  open: boolean
  onClose: () => void
  project?: Project | null
}

export function DeleteProjectDialog({ open, onClose, project }: DeleteProjectDialogProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!project) return null

  const hasBlocks = project._count && project._count.bloklar > 0
  const hasFaults = project._count && project._count.arizalar > 0

  const handleDelete = async () => {
    try {      setLoading(true)
      setError(null)

      const response = await fetch(`/api/projects/${project.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Silme işlemi başarısız")
      }

      // Başarı bildirimi
      toast.success(`"${project.ad}" projesi başarıyla silindi!`, {
        title: "Silindi",
        duration: 4000
      })

      onClose()
    } catch (error) {
      console.error("Error deleting project:", error)
      const errorMessage = error instanceof Error ? error.message : "Silme işlemi başarısız"
      
      // Hata bildirimi
      toast.error(errorMessage, {
        title: "Silme Hatası",
        duration: 6000
      })
      
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Projeyi Sil
          </DialogTitle>
          <DialogDescription>
            Bu işlem geri alınamaz. Proje ve tüm ilişkili veriler silinecek.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Silinecek Proje:</h4>
            <p className="text-sm font-medium">{project.ad}</p>
            {project._count && (
              <div className="mt-2 text-sm text-muted-foreground">
                <p>• {project._count.bloklar} blok</p>
                <p>• {project._count.arizalar} arıza</p>
              </div>
            )}
          </div>

          {(hasBlocks || hasFaults) && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Bu proje silinirse:
                <ul className="mt-2 list-disc list-inside space-y-1">
                  {hasBlocks && (
                    <li>{project._count?.bloklar} blok ve tüm daireleri silinecek</li>
                  )}
                  {hasFaults && (
                    <li>{project._count?.arizalar} arıza kaydı silinecek</li>
                  )}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? "Siliniyor..." : "Sil"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
