import React from "react";
import { MediaSelector, MediaFile } from "./MediaSelector";
import { Button } from "@/components/ui/button";
import { Image } from "lucide-react";

interface GlobalMediaSelectorProps {
  onSelect: (media: MediaFile) => void;
  selectedMedia?: MediaFile | null;
  buttonText?: string;
  title?: string;
  description?: string;
  acceptedTypes?: string[];
  customFolder?: string;
}

export const GlobalMediaSelector: React.FC<GlobalMediaSelectorProps> = ({
  onSelect,
  selectedMedia,
  buttonText = "Medya Seç",
  title = "Medya Seç",
  description,
  acceptedTypes = ["image/*"],
  customFolder = "media",
}) => {
  const trigger = (
    <Button type="button" variant="outline" className="w-full">
      <Image className="w-4 h-4 mr-2" />
      {selectedMedia ? selectedMedia.originalName : buttonText}
    </Button>
  );

  return (
    <MediaSelector
      onSelect={onSelect}
      selectedMedia={selectedMedia}
      trigger={trigger}
      title={title}
      description={description}
      acceptedTypes={acceptedTypes}
      customFolder={customFolder}
    />
  );
}; 