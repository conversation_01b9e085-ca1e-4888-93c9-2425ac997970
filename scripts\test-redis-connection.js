const Redis = require('ioredis')

// Optimized Redis configuration matching our main app
const redisConfig = {
  host: 'localhost',
  port: 6379,
  db: 0,
  
  // Connection Management
  lazyConnect: true,
  enableReadyCheck: true,
  enableOfflineQueue: false, // Windows Docker için false
  maxRetriesPerRequest: 3,
  enableAutoPipelining: false, // Windows Docker için false
  
  // Timeout Settings
  connectTimeout: 5000,
  commandTimeout: 3000,
  lazyConnectTimeout: 5000,
  
  // Keep Alive Settings
  keepAlive: 30000,
  family: 4,
  
  // Retry Strategy
  retryStrategy(times) {
    if (times > 5) return null;
    return Math.min(times * 500, 3000);
  },
  
  // Reconnection Strategy
  reconnectOnError(err) {
    const targetErrors = ['READONLY', 'ECONNRESET', 'ENOTFOUND', 'ECONNREFUSED'];
    return targetErrors.some(targetError => err.message.includes(targetError));
  },
}

async function comprehensiveRedisTest() {
  console.log('🚀 Starting Comprehensive Redis Connection Test...\n')
  
  const redis = new Redis(redisConfig)
  let connectionAttempts = 0
  let successfulOperations = 0
  let errors = []
  
  // Event listeners
  redis.on('connect', () => {
    connectionAttempts++
    console.log(`✅ Connection #${connectionAttempts} established`)
  })
  
  redis.on('error', (err) => {
    errors.push({ timestamp: new Date(), error: err.message })
    console.log(`❌ Error: ${err.message}`)
  })
  
  redis.on('close', () => {
    console.log('🔌 Connection closed')
  })
  
  redis.on('reconnecting', () => {
    console.log('🔄 Reconnecting...')
  })
  
  try {
    console.log('📋 Test 1: Initial Connection')
    await redis.connect()
    console.log(`   Status: ${redis.status}`)
    
    console.log('\n📋 Test 2: Basic Operations (50 iterations)')
    for (let i = 0; i < 50; i++) {
      try {
        await redis.ping()
        await redis.set(`test:key:${i}`, `value${i}`, 'EX', 10)
        const value = await redis.get(`test:key:${i}`)
        if (value === `value${i}`) {
          successfulOperations++
        }
        
        if (i % 10 === 0) {
          console.log(`   Progress: ${i + 1}/50 operations completed`)
        }
        
        // Simulate real-world delay
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        errors.push({ timestamp: new Date(), error: error.message })
        console.log(`   ❌ Operation ${i + 1} failed: ${error.message}`)
      }
    }
    
    console.log('\n📋 Test 3: Concurrent Operations')
    const concurrentPromises = []
    for (let i = 0; i < 10; i++) {
      concurrentPromises.push(
        redis.set(`concurrent:${i}`, `value${i}`, 'EX', 5)
          .then(() => redis.get(`concurrent:${i}`))
          .catch(err => {
            errors.push({ timestamp: new Date(), error: err.message })
            throw err
          })
      )
    }
    
    const concurrentResults = await Promise.allSettled(concurrentPromises)
    const concurrentSuccesses = concurrentResults.filter(r => r.status === 'fulfilled').length
    console.log(`   Concurrent operations: ${concurrentSuccesses}/10 successful`)
    
    console.log('\n📋 Test 4: Stress Test (5 minutes)')
    const stressTestDuration = 5 * 60 * 1000 // 5 minutes
    const stressTestStart = Date.now()
    let stressOperations = 0
    let stressErrors = 0
    
    while (Date.now() - stressTestStart < stressTestDuration) {
      try {
        await redis.ping()
        await redis.incr('stress:counter')
        stressOperations++
        
        if (stressOperations % 100 === 0) {
          const elapsed = Math.floor((Date.now() - stressTestStart) / 1000)
          console.log(`   Stress test: ${stressOperations} ops in ${elapsed}s`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 50))
      } catch (error) {
        stressErrors++
        errors.push({ timestamp: new Date(), error: error.message })
      }
    }
    
    console.log(`   Stress test completed: ${stressOperations} operations, ${stressErrors} errors`)
    
    // Cleanup
    console.log('\n📋 Test 5: Cleanup')
    const keys = await redis.keys('test:*', 'concurrent:*', 'stress:*')
    if (keys.length > 0) {
      await redis.del(...keys)
      console.log(`   Cleaned up ${keys.length} test keys`)
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message)
    errors.push({ timestamp: new Date(), error: error.message })
  } finally {
    // Final status report
    console.log('\n' + '='.repeat(60))
    console.log('📊 FINAL REPORT')
    console.log('='.repeat(60))
    console.log(`Connection Attempts: ${connectionAttempts}`)
    console.log(`Successful Operations: ${successfulOperations}/50`)
    console.log(`Total Errors: ${errors.length}`)
    console.log(`Final Redis Status: ${redis.status}`)
    
    if (errors.length > 0) {
      console.log('\n❌ Error Summary:')
      const errorCounts = {}
      errors.forEach(err => {
        errorCounts[err.error] = (errorCounts[err.error] || 0) + 1
      })
      Object.entries(errorCounts).forEach(([error, count]) => {
        console.log(`   ${error}: ${count} times`)
      })
    }
    
    console.log('\n🔌 Closing connection...')
    await redis.quit()
    console.log('✅ Test completed!')
  }
}

// Run the test
comprehensiveRedisTest().catch(console.error) 