"use client"

import { useState, useEffect } from "react"
import { 
  Plus, Search, Edit, Trash2, Tag, Palette, Wrench, Zap, Droplets, Paintbrush, 
  Home, Car, Shield, Camera, Thermometer, Wifi, Phone, Mail, Clock, Star, 
  Heart, Settings, User, Lock, Eye, Bell, Calendar, MapPin, Globe, Building, 
  Truck, Hammer, Lightbulb, Plug, Scissors, Target, Filter, Box, Package, 
  FileText, Folder, Download, Upload, Play, Pause, Volume, Mic, Monitor, 
  Laptop, Smartphone, Battery, Cpu, Database, Cloud, Power,
  Flag, Users, Menu, Check, Minus, Info, HelpCircle, AlertCircle, AlertTriangle,
  Sun, Moon, Wind, Umbrella, Copy, Share, ShoppingCart, CreditCard, Wallet, 
  Award, Trophy
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"
import { LoadingButton } from "@/components/ui/loading-button"

interface Category {
  id: string
  ad: string
  aciklama?: string
  renk: string
  ikon?: string
  olusturulma_tarihi: string
  guncelleme_tarihi: string
}

const colorOptions = [
  { name: "Gri", value: "#6B7280" },
  { name: "Kirmizi", value: "#EF4444" },
  { name: "Turuncu", value: "#F97316" },
  { name: "Sari", value: "#EAB308" },
  { name: "Yesil", value: "#22C55E" },
  { name: "Mavi", value: "#3B82F6" },
  { name: "Mor", value: "#8B5CF6" },
  { name: "Pembe", value: "#EC4899" },
]

// Safe lucide icons mapping - expanded collection
const iconMap: { [key: string]: any } = {
  // Repair & Maintenance
  wrench: Wrench,
  hammer: Hammer,
  scissors: Scissors,
  settings: Settings,

  // Electrical
  zap: Zap,
  lightbulb: Lightbulb,
  plug: Plug,
  battery: Battery,
  power: Power,
  wifi: Wifi,
  monitor: Monitor,
  laptop: Laptop,
  smartphone: Smartphone,
  cpu: Cpu,
  database: Database,
  cloud: Cloud,

  // Painting
  paintbrush: Paintbrush,
  palette: Palette,

  // Plumbing
  droplets: Droplets,

  // Building
  home: Home,
  building: Building,
  truck: Truck,

  // Security
  shield: Shield,
  lock: Lock,
  eye: Eye,
  camera: Camera,
  bell: Bell,

  // General
  star: Star,
  heart: Heart,
  target: Target,
  flag: Flag,
  clock: Clock,
  calendar: Calendar,
  user: User,
  users: Users,
  mail: Mail,
  phone: Phone,
  globe: Globe,
  "map-pin": MapPin,
  search: Search,
  filter: Filter,
  menu: Menu,
  check: Check,
  plus: Plus,
  minus: Minus,
  info: Info,
  "help-circle": HelpCircle,
  "alert-circle": AlertCircle,
  "alert-triangle": AlertTriangle,
  "file-text": FileText,
  folder: Folder,
  download: Download,
  upload: Upload,
  copy: Copy,
  share: Share,
  "shopping-cart": ShoppingCart,
  "credit-card": CreditCard,
  wallet: Wallet,
  award: Award,
  trophy: Trophy,
  tag: Tag,
  package: Package,
  box: Box,
  edit: Edit,
  trash: Trash2,
  play: Play,
  pause: Pause,
  volume: Volume,
  mic: Mic,
  thermometer: Thermometer,
  
  // Weather
  sun: Sun,
  moon: Moon,
  wind: Wind,
  umbrella: Umbrella
}

// Organized icon categories for better UX
const iconCategories = [
  {
    name: "🔧 Onarım & Bakım",
    icons: [
      { name: "wrench", label: "İngiliz Anahtarı" },
      { name: "hammer", label: "Çekiç" },
      { name: "scissors", label: "Makas" },
      { name: "settings", label: "Ayarlar" },
    ]
  },
  {
    name: "⚡ Elektrik",
    icons: [
      { name: "zap", label: "Elektrik" },
      { name: "lightbulb", label: "Ampul" },
      { name: "plug", label: "Fiş" },
      { name: "battery", label: "Pil" },
      { name: "power", label: "Güç" },
      { name: "wifi", label: "WiFi" },
      { name: "monitor", label: "Monitör" },
      { name: "laptop", label: "Laptop" },
      { name: "smartphone", label: "Telefon" },
      { name: "cpu", label: "İşlemci" }
    ]
  },
  {
    name: "🎨 Boyama",
    icons: [
      { name: "paintbrush", label: "Fırça" },
      { name: "palette", label: "Palet" }
    ]
  },
  {
    name: "💧 Tesisat",
    icons: [
      { name: "droplets", label: "Su" }
    ]
  },
  {
    name: "🏗️ Yapı & İnşaat",
    icons: [
      { name: "home", label: "Ev" },
      { name: "building", label: "Bina" },
      { name: "truck", label: "Kamyon" }
    ]
  },
  {
    name: "🛡️ Güvenlik",
    icons: [
      { name: "shield", label: "Kalkan" },
      { name: "lock", label: "Kilit" },
      { name: "eye", label: "Göz" },
      { name: "camera", label: "Kamera" },
      { name: "bell", label: "Zil" }
    ]
  },
  {
    name: "⭐ Genel",
    icons: [
      { name: "star", label: "Yıldız" },
      { name: "heart", label: "Kalp" },
      { name: "target", label: "Hedef" },
      { name: "flag", label: "Bayrak" },
      { name: "clock", label: "Saat" },
      { name: "calendar", label: "Takvim" },
      { name: "user", label: "Kullanıcı" },
      { name: "users", label: "Kullanıcılar" },
      { name: "mail", label: "E-posta" },
      { name: "phone", label: "Telefon" },
      { name: "globe", label: "Dünya" },
      { name: "search", label: "Arama" },
      { name: "check", label: "Onay" },
      { name: "plus", label: "Artı" },
      { name: "info", label: "Bilgi" },
      { name: "award", label: "Ödül" },
      { name: "trophy", label: "Kupa" }
    ]
  },
  {
    name: "🌤️ Hava Durumu",
    icons: [
      { name: "sun", label: "Güneş" },
      { name: "moon", label: "Ay" },
      { name: "thermometer", label: "Termometre" },
      { name: "wind", label: "Rüzgar" },
      { name: "umbrella", label: "Şemsiye" }
    ]
  }
]

// All icons flattened for search
const allIcons = iconCategories.flatMap(category => category.icons)

// Popular icons for quick access
const popularIcons = [
  { name: "wrench", label: "Anahtar" },
  { name: "paintbrush", label: "Boya" },
  { name: "zap", label: "Elektrik" },
  { name: "droplets", label: "Su/Tesisat" },
  { name: "home", label: "Ev" },
  { name: "car", label: "Araç" },
  { name: "shield", label: "Güvenlik" },
  { name: "camera", label: "Kamera" },
  { name: "thermometer", label: "Isıtma/Soğutma" },
  { name: "wifi", label: "İnternet" },
  { name: "phone", label: "Telefon" },
  { name: "clock", label: "Zaman" },
  { name: "settings", label: "Ayarlar" },
  { name: "user", label: "Kullanıcı" },
  { name: "lock", label: "Kilit" },
  { name: "bell", label: "Bildirim" },
  { name: "calendar", label: "Takvim" },
  { name: "building", label: "Bina" },
  { name: "hammer", label: "Çekiç" },
  { name: "lightbulb", label: "Ampul" },
  { name: "plug", label: "Fiş" },
  { name: "target", label: "Hedef" },
  { name: "star", label: "Yıldız" },
  { name: "heart", label: "Kalp" }
]

// Component to render icon
const CategoryIcon = ({ iconName, className = "h-4 w-4" }: { iconName?: string, className?: string }) => {
  if (!iconName) return null
  
  // Check if it's a lucide icon
  const IconComponent = iconMap[iconName.toLowerCase()]
  if (IconComponent) {
    return <IconComponent className={className} />
  }
  
  // If not a lucide icon, treat as emoji or text
  return <span className={className}>{iconName}</span>
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [formData, setFormData] = useState({
    ad: "",
    aciklama: "",
    renk: "#6B7280",
    ikon: ""
  })
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/categories")
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories || [])
      } else {
        toast.error("Kategoriler yuklenirken hata olustu")
      }
    } catch (error) {
      console.error("Error fetching categories:", error)
      toast.error("Kategoriler yuklenirken hata olustu")
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.ad.trim()) {
      toast.error("Kategori adi gereklidir")
      return
    }

    setSubmitting(true)
    try {
      const url = editingCategory 
        ? `/api/categories/${editingCategory.id}`
        : "/api/categories"
      
      const method = editingCategory ? "PUT" : "POST"
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        fetchCategories()
        handleCloseDialog()
      } else {
        toast.error(data.message || "Islem basarisiz")
      }
    } catch (error) {
      console.error("Error submitting category:", error)
      toast.error("Islem sirasinda hata olustu")
    } finally {
      setSubmitting(false)
    }
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setFormData({
      ad: category.ad,
      aciklama: category.aciklama || "",
      renk: category.renk,
      ikon: category.ikon || ""
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (category: Category) => {
    try {
      const response = await fetch(`/api/categories/${category.id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        fetchCategories()
      } else {
        toast.error(data.message || "Silme islemi basarisiz")
      }
    } catch (error) {
      console.error("Error deleting category:", error)
      toast.error("Silme islemi sirasinda hata olustu")
    }
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingCategory(null)
    setFormData({
      ad: "",
      aciklama: "",
      renk: "#6B7280",
      ikon: ""
    })
  }

  const filteredCategories = categories.filter(category =>
    category.ad.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.aciklama?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Ariza Kategorileri</h1>
            <p className="text-muted-foreground">Ariza kategorilerini yonetin</p>
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Ariza Kategorileri</h1>
          <p className="text-muted-foreground">
            Ariza kategorilerini yonetin ve duzenleyin
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Yeni Kategori
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingCategory ? "Kategori Düzenle" : "Yeni Kategori Ekle"}
              </DialogTitle>
              <DialogDescription>
                {editingCategory ? "Kategori bilgilerini düzenleyin." : "Yeni bir ariza kategorisi oluşturun."}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column - Basic Info */}
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="ad">Kategori Adı</Label>
                      <Input
                        id="ad"
                        value={formData.ad}
                        onChange={(e) => setFormData(prev => ({ ...prev, ad: e.target.value }))}
                        placeholder="Kategori adı"
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="aciklama">Açıklama</Label>
                      <Textarea
                        id="aciklama"
                        value={formData.aciklama}
                        onChange={(e) => setFormData(prev => ({ ...prev, aciklama: e.target.value }))}
                        placeholder="Kategori açıklaması"
                        rows={3}
                      />
                    </div>
                    
                    <div>
                      <Label>Renk</Label>
                      <div className="flex gap-2 flex-wrap mt-2">
                        {colorOptions.map((color) => (
                          <button
                            key={color.value}
                            type="button"
                            className={`w-8 h-8 rounded-full border-2 ${
                              formData.renk === color.value ? "border-gray-800" : "border-gray-300"
                            }`}
                            style={{ backgroundColor: color.value }}
                            onClick={() => setFormData(prev => ({ ...prev, renk: color.value }))}
                            title={color.name}
                          />
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label>Seçili İkon</Label>
                      <div className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50 mt-2">
                        <CategoryIcon iconName={formData.ikon} className="h-6 w-6" />
                        <span className="font-medium">{formData.ikon || "İkon seçilmedi"}</span>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Icon Selector */}
                  <div className="space-y-4">
                    <Label>İkon Seçimi</Label>
                    
                    {/* Popular Icons */}
                    <div>
                      <div className="text-sm font-medium mb-2">⭐ Popüler İkonlar</div>
                      <div className="grid grid-cols-6 gap-2 p-3 border rounded-lg bg-gray-50">
                        {popularIcons.slice(0, 12).map((icon) => (
                          <button
                            key={icon.name}
                            type="button"
                            onClick={() => setFormData(prev => ({ ...prev, ikon: icon.name }))}
                            className={`p-2 rounded border transition-all ${
                              formData.ikon === icon.name 
                                ? 'border-blue-500 bg-blue-100' 
                                : 'border-gray-200 hover:border-gray-300 hover:bg-white'
                            }`}
                            title={icon.label}
                          >
                            <CategoryIcon iconName={icon.name} className="h-4 w-4 mx-auto" />
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Categorized Icons */}
                    <div className="space-y-3">
                      <div className="text-sm font-medium">📦 Kategoriler</div>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {iconCategories.map((category) => (
                          <div key={category.name} className="border rounded-lg p-2">
                            <div className="text-xs font-medium mb-2 text-gray-600">
                              {category.name}
                            </div>
                            <div className="grid grid-cols-5 gap-1">
                              {category.icons.map((icon) => (
                                <button
                                  key={icon.name}
                                  type="button"
                                  onClick={() => setFormData(prev => ({ ...prev, ikon: icon.name }))}
                                  className={`p-1.5 rounded border transition-all ${
                                    formData.ikon === icon.name 
                                      ? 'border-blue-500 bg-blue-100' 
                                      : 'border-gray-200 hover:border-gray-300'
                                  }`}
                                  title={icon.label}
                                >
                                  <CategoryIcon iconName={icon.name} className="h-3 w-3 mx-auto" />
                                </button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Manual Input */}
                    <div>
                      <div className="text-sm font-medium mb-2">✏️ Manuel Giriş</div>
                      <div className="space-y-2">
                        <Input
                          value={formData.ikon}
                          onChange={(e) => setFormData(prev => ({ ...prev, ikon: e.target.value }))}
                          placeholder="paintbrush, wrench, palette..."
                          className="text-sm"
                        />
                        
                        {/* Icon Validation & Preview */}
                        {formData.ikon && (
                          <div className="flex items-center gap-2 p-2 bg-gray-50 rounded border">
                            <CategoryIcon iconName={formData.ikon} className="h-4 w-4" />
                            <span className="text-xs">
                              {iconMap[formData.ikon.toLowerCase()] ? (
                                <span className="text-green-600 font-medium">✓ Geçerli ikon</span>
                              ) : (
                                <span className="text-orange-600 font-medium">⚠ Emoji olarak gösterilecek</span>
                              )}
                            </span>
                          </div>
                        )}
                        
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div><strong>Boyama için:</strong> paintbrush, palette</div>
                          <div><strong>Onarım için:</strong> wrench, hammer, scissors</div>
                          <div><strong>Elektrik için:</strong> zap, lightbulb, plug</div>
                          <div><strong>Ya da emoji:</strong> 🔧 ⚡ 🎨 💧</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  Iptal
                </Button>
                <LoadingButton type="submit" loading={submitting}>
                  {editingCategory ? "Guncelle" : "Ekle"}
                </LoadingButton>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kategori</CardTitle>
            <Tag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Kategori</CardTitle>
            <Palette className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Kategori ara..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* Categories Table */}
      <Card>
        <CardHeader>
          <CardTitle>Kategoriler</CardTitle>
          <CardDescription>
            Tum ariza kategorilerini buradan yonetebilirsiniz.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>İkon</TableHead>
                <TableHead>Ad</TableHead>
                <TableHead>Aciklama</TableHead>
                <TableHead>Renk</TableHead>
                <TableHead>Olusturulma</TableHead>
                <TableHead className="text-right">Islemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCategories.map((category) => (
                <TableRow key={category.id}>
                  <TableCell>
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-muted">
                      <CategoryIcon iconName={category.ikon} className="h-5 w-5" />
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {category.ad}
                  </TableCell>
                  <TableCell>
                    <div className="max-w-[200px] truncate">
                      {category.aciklama || "-"}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded-full border"
                        style={{ backgroundColor: category.renk }}
                      />
                      <Badge style={{ backgroundColor: category.renk, color: "white" }}>
                        {category.ad}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(category.olusturulma_tarihi)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(category)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm" className="text-red-600">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Kategoriyi Sil</AlertDialogTitle>
                            <AlertDialogDescription>
                              Bu kategoriyi silmek istediginizden emin misiniz? Bu islem geri alinamaz.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Iptal</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(category)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Sil
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredCategories.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Kategori bulunamadi.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 