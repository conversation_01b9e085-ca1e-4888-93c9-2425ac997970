"use client";

import { useState, useEffect, useCallback } from "react";
import { getBuildInfo, formatVersionDisplay, getVersionFeatures } from "@/lib/version";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger 
} from "@/components/ui/dialog";
import { RefreshCw, Sparkles, CheckCircle, Loader2, Circle } from "lucide-react";

export function AppFooter() {
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [redisStatus, setRedisStatus] = useState<'loading' | 'ok' | 'error'>('loading');
  const [isClient, setIsClient] = useState(false);
  const buildInfo = getBuildInfo();
  const versionFeatures = getVersionFeatures();
  
  // Renk sınıflarını dinamik olarak belirle
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'purple':
        return 'text-purple-600 bg-purple-50';
      case 'blue':
        return 'text-blue-600 bg-blue-50';
      case 'green':
        return 'text-green-600 bg-green-50';
      case 'orange':
        return 'text-orange-600 bg-orange-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };
  
  // Redis health check
  const checkRedis = useCallback(async () => {
    setRedisStatus('loading');
    try {
      const res = await fetch("/api/health/redis");
      if (res.ok) {
        setRedisStatus('ok');
      } else {
        setRedisStatus('error');
      }
    } catch {
      setRedisStatus('error');
    }
  }, []);

  // Redis restart function
  const restartRedis = useCallback(async () => {
    setRedisStatus('loading');
    try {
      const res = await fetch("/api/health/redis", { method: 'POST' });
      if (res.ok) {
        setRedisStatus('ok');
      } else {
        setRedisStatus('error');
      }
    } catch {
      setRedisStatus('error');
    }
  }, []);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;
    
    checkRedis();
    const interval = setInterval(checkRedis, 10000); // 10 saniyede bir kontrol
    return () => clearInterval(interval);
  }, [checkRedis, isClient]);
  
  return (
    <footer className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        {/* Desktop Layout */}
        <div className="hidden md:flex h-12 items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <span>© 2025 Bakım Onarım Sistemi.</span>
            <span className="text-xs text-muted-foreground/70">Tüm hakları saklıdır.</span>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Custom Feature Tag */}
            <span className={`text-xs font-medium px-2 py-1 rounded-full ${getColorClasses(versionFeatures.color)}`}>
              {versionFeatures.tag}
            </span>
            
            {/* Version Display */}
            <div className="flex items-center gap-2">
              <span className="text-xs">Sürüm:</span>
              <span className="font-mono font-bold text-foreground bg-green-100 px-2 py-1 rounded text-green-800 text-xs">
                {formatVersionDisplay(buildInfo.version)}
              </span>
            </div>
            
            {/* Build Date */}
            <div className="flex items-center gap-2 text-xs">
              <span>Build:</span>
              <span 
                className="font-mono text-blue-600 hover:text-blue-800 cursor-help transition-colors" 
                title={`Detaylı zaman: ${buildInfo.buildTimeFormatted}`}
              >
                {buildInfo.buildDate}
              </span>
            </div>

            {/* Redis Health Indicator */}
            <div 
              className="flex items-center gap-1 cursor-pointer hover:bg-gray-100 px-2 py-1 rounded transition-colors" 
              title={
                !isClient ? 'Redis Status' :
                redisStatus === 'ok' ? 'Redis Connected - Click to refresh, Double-click to restart' : 
                redisStatus === 'error' ? 'Redis Connection Error - Click to retry, Double-click to restart' : 
                'Kontrol ediliyor...'
              }
              onClick={isClient ? checkRedis : undefined}
              onDoubleClick={isClient ? restartRedis : undefined}
            >
              {!isClient ? (
                <Circle className="h-4 w-4 text-gray-400" />
              ) : redisStatus === 'loading' ? (
                <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
              ) : redisStatus === 'ok' ? (
                <Circle className="h-4 w-4 text-green-500" fill="#22c55e" />
              ) : (
                <Circle className="h-4 w-4 text-red-500" fill="#ef4444" />
              )}
              <span className="text-xs select-none">Redis</span>
            </div>

            {/* Update Button */}
            <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
              <DialogTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-8 px-2 text-xs hover:bg-green-50 hover:text-green-700"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Güncellemeler
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-green-600" />
                    Sürüm {formatVersionDisplay(buildInfo.version)} Güncellemeleri
                  </DialogTitle>
                  <DialogDescription>
                    Bu sürümde eklenen yeni özellikler ve iyileştirmeler
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={getColorClasses(versionFeatures.color)}>
                      {versionFeatures.tag}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    {versionFeatures.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                  <div className="pt-2 border-t text-xs text-muted-foreground">
                    <p>Build: {buildInfo.buildDate}</p>
                    <p>Detaylı zaman: {buildInfo.buildTimeFormatted}</p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden py-3 space-y-2 text-xs text-muted-foreground">
          <div className="text-center">
            <span>© 2025 Bakım Onarım Sistemi</span>
          </div>
          <div className="flex justify-center items-center gap-3 flex-wrap">
            <span className={`font-medium px-2 py-1 rounded-full ${getColorClasses(versionFeatures.color)}`}>
              {versionFeatures.tag} {formatVersionDisplay(buildInfo.version)}
            </span>
            <span className="font-mono font-bold bg-green-100 px-2 py-1 rounded text-green-800">
              {formatVersionDisplay(buildInfo.version)}
            </span>
            <span 
              className="font-mono text-blue-600"
              title={buildInfo.buildTimeFormatted}
            >
              {buildInfo.buildDate}
            </span>
          </div>
          
          {/* Mobile Update Button */}
          <div className="flex justify-center">
            <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
              <DialogTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-8 px-3 text-xs hover:bg-green-50 hover:text-green-700"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Güncellemeler
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-green-600" />
                    Sürüm {formatVersionDisplay(buildInfo.version)} Güncellemeleri
                  </DialogTitle>
                  <DialogDescription>
                    Bu sürümde eklenen yeni özellikler ve iyileştirmeler
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={getColorClasses(versionFeatures.color)}>
                      {versionFeatures.tag}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    {versionFeatures.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                  <div className="pt-2 border-t text-xs text-muted-foreground">
                    <p>Build: {buildInfo.buildDate}</p>
                    <p>Detaylı zaman: {buildInfo.buildTimeFormatted}</p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default AppFooter; 