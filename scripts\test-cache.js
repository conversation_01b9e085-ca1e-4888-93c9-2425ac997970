const Redis = require('ioredis')
const redis = new Redis()

async function testCacheManager() {
  console.log('🔍 Testing CacheManager...')
  
  try {
    // Test 1: Basic set/get
    console.log('\n1. Testing basic set/get...')
    await redis.setex('test:basic', 60, JSON.stringify({ message: 'Hello Cache!' }))
    const basicValue = await redis.get('test:basic')
    const basicResult = basicValue ? JSON.parse(basicValue) : null
    console.log('✅ Basic set/get:', basicResult)
    
    // Test 2: Key generation simulation
    console.log('\n2. Testing key generation...')
    const params = { page: 1, limit: 10, search: 'test' }
    const sortedParams = Object.keys(params).sort().map(key => `${key}=${params[key]}`).join(':')
    const key = `api:users:${sortedParams}`
    console.log('✅ Generated key:', key)
    
    // Test 3: Pattern deletion
    console.log('\n3. Testing pattern deletion...')
    await redis.set('api:users:list:page=1', 'user data 1')
    await redis.set('api:users:list:page=2', 'user data 2')
    await redis.set('api:faults:stats', 'stats data')
    
    const beforeKeys = await redis.keys('api:*')
    console.log('Keys before deletion:', beforeKeys)
    
    const userKeys = await redis.keys('api:users:*')
    if (userKeys.length > 0) {
      await redis.del(...userKeys)
    }
    
    const afterKeys = await redis.keys('api:*')
    console.log('Keys after deletion:', afterKeys)
    console.log('✅ Pattern deletion successful')
    
    // Test 4: Cache convenience function
    console.log('\n4. Testing cache.apiResponse...')
    let callCount = 0
    const mockFetchFn = async () => {
      callCount++
      return { data: `Fetched data ${callCount}`, timestamp: Date.now() }
    }
    
    const key1 = cache.apiKey('test:api', { id: 1 })
    
    // First call - should fetch
    const result1 = await cache.apiResponse(key1, mockFetchFn, CACHE_TTL.DEFAULT)
    console.log('First call result:', result1)
    
    // Second call - should use cache
    const result2 = await cache.apiResponse(key1, mockFetchFn, CACHE_TTL.DEFAULT)
    console.log('Second call result:', result2)
    
    console.log('Call count:', callCount) // Should be 1
    console.log('✅ Cache.apiResponse working correctly')
    
    // Test 5: Cache stats
    console.log('\n5. Testing cache stats...')
    const stats = await CacheManager.getStats()
    console.log('Cache stats:', stats)
    console.log('✅ Cache stats working')
    
    // Test 6: Health check
    console.log('\n6. Testing health check...')
    const isHealthy = await CacheManager.healthCheck()
    console.log('Health check result:', isHealthy)
    console.log('✅ Health check working')
    
    // Cleanup
    console.log('\n🧹 Cleaning up...')
    await CacheManager.deletePattern('test:*')
    await CacheManager.deletePattern('api:*')
    
    console.log('\n🎉 All cache tests passed!')
    
  } catch (error) {
    console.error('❌ Cache test failed:', error)
  }
}

testCacheManager() 