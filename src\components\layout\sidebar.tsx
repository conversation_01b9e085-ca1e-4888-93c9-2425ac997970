// LoadingLink completely removed - Force reload
"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { signOut } from "next-auth/react"
import { useLoading } from "@/contexts/loading-context"

import { 
  Home, 
  Settings, 
  ChevronLeft, 
  ChevronDown,
  Wrench,
  Building,
  Users,
  Package,
  BarChart3,
  LogOut,
  Menu,
  X,
  Calendar,
  AlertTriangle,
  UserCheck,
  FolderOpen,
  Shield,
  Cog
} from "lucide-react"

interface SidebarProps {
  user?: {
    name?: string | null
    email?: string | null
    image?: string | null
    role?: string
  }
}

interface MenuItem {
  id: string
  label: string
  icon: any
  href?: string
  description?: string
  children?: {
    label: string
    href: string
    description?: string
  }[]
}

interface MenuSection {
  id: string
  label: string
  items: MenuItem[]
}

const menuSections: MenuSection[] = [
  {
    id: "main",
    label: "Ana Menü",
    items: [
      {
        id: "dashboard",
        label: "Dashboard",
        href: "/dashboard",
        icon: Home,
        description: "Genel bakış ve istatistikler"
      }
    ]
  },
  {
    id: "fault-management",
    label: "Arıza Yönetimi",
    items: [
      {
        id: "ariza",
        label: "Arızalar",
        href: "/arizalar",
        icon: AlertTriangle,
        description: "Tüm arızaları görüntüle"
      },
      {
        id: "randevu",
        label: "Randevular",
        href: "/randevu",
        icon: Calendar,
        description: "Teknisyen randevuları"
      }
    ]
  },
  {
    id: "project-management",
    label: "Proje Yönetimi",
    items: [
      {
        id: "projeler",
        label: "Projeler",
        href: "/projeler",
        icon: FolderOpen,
        description: "Proje, blok ve daire yönetimi"
      }
    ]
  },
  {
    id: "resource-management",
    label: "Kaynak Yönetimi",
    items: [
      {
        id: "teknisyenler",
        label: "Teknisyenler",
        href: "/teknisyenler",
        icon: UserCheck,
        description: "Teknisyen yönetimi"
      },
      {
        id: "malzemeler",
        label: "Malzemeler",
        href: "/malzemeler",
        icon: Package,
        description: "Malzeme stoğu yönetimi"
      }
    ]
  },
  {
    id: "reports",
    label: "Raporlar & Analiz",
    items: [
      {
        id: "raporlar",
        label: "Raporlar",
        icon: BarChart3,
        href: "/raporlar",
        description: "Raporlar ana sayfası",
        children: [
          { label: "Arıza Raporları", href: "/raporlar/ariza", description: "Arıza istatistikleri" },
          { label: "Teknisyen Raporları", href: "/raporlar/teknisyen", description: "Teknisyen performansı" },
          { label: "Malzeme Raporları", href: "/raporlar/malzeme", description: "Malzeme kullanım raporları" }
        ]
      }
    ]
  },
  {
    id: "user-management",
    label: "Kullanıcı Yönetimi",
    items: [
      {
        id: "kullanicilar",
        label: "Kullanıcılar",
        href: "/kullanicilar",
        icon: Users,
        description: "Sistem kullanıcıları"
      },
      {
        id: "roller",
        label: "Rol Yönetimi",
        href: "/roller",
        icon: Shield,
        description: "Kullanıcı rolleri ve yetkileri"
      }
    ]
  },
  {
    id: "system",
    label: "Sistem Ayarları",
    items: [
      {
        id: "ayarlar",
        label: "Ayarlar",
        icon: Settings,
        children: [
          { label: "Arıza Kategorileri", href: "/ayarlar/kategoriler", description: "Arıza kategori yönetimi" },
          { label: "Öncelik Seviyeleri", href: "/ayarlar/oncelik-seviyeleri", description: "Arıza öncelik tanımları" },
          { label: "Durum Tanımları", href: "/ayarlar/durum-tanimlari", description: "Arıza durum tanımları" }
        ]
      }
    ]
  }
]

export function Sidebar({ user }: SidebarProps) {
  const router = useRouter()
  const { startLoading } = useLoading()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [mounted, setMounted] = useState(false)
  const pathname = usePathname()
  
  // Hydration hatalarını önlemek için client kontrolü
  useEffect(() => {
    setMounted(true)
    // Client'da mount olduktan sonra default expanded items'ı set et
    setExpandedItems(["raporlar"])
  }, [])

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed)
    setExpandedItems([]) // Collapse all dropdowns when sidebar collapses
  }

  const toggleMobile = () => {
    setIsMobileOpen(!isMobileOpen)
  }

  const isItemActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/')
  }

  const isSectionActive = (section: any) => {
    return section.items.some((item: any) => {
      if (item.href) {
        return isItemActive(item.href)
      }
      if (item.children) {
        return item.children.some((child: any) => isItemActive(child.href))
      }
      return false
    })
  }

  const handleNavigation = (href: string) => {
    startLoading()
    router.push(href)
  }

  return (
    <div>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden fixed top-4 left-4 z-50"
        onClick={toggleMobile}
      >
        {isMobileOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={toggleMobile}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "bg-white shadow-md flex flex-col h-full transition-all duration-300 ease-in-out",
        "fixed md:relative z-50",
        isCollapsed ? "w-[70px]" : "w-72",
        isMobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
      )}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center">
            <Shield className="text-blue-600 text-2xl" />
            {!isCollapsed && (
              <div className="ml-2">
                <span className="text-xl font-bold text-gray-900">Master Plan</span>
                <p className="text-xs text-gray-500">Bakım & Onarım</p>
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleCollapse}
            className="hidden md:flex"
          >
            <ChevronLeft className={cn(
              "h-4 w-4 transition-transform duration-200",
              isCollapsed && "rotate-180"
            )} />
          </Button>
        </div>

        {/* User Info */}
        {!isCollapsed && (
          <div className="p-4 border-b bg-gray-50">
            <div className="flex items-center">
              <Avatar className="h-12 w-12">
                <AvatarImage src={user?.image || ""} />
                <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                  {user?.name?.split(" ").map(n => n[0]).join("") || "U"}
                </AvatarFallback>
              </Avatar>
              <div className="ml-3">
                <p className="font-semibold text-gray-900">{user?.name || "Admin User"}</p>
                <p className="text-sm text-gray-600">
                  {user?.role === "ADMIN" ? "Sistem Yöneticisi" : 
                   user?.role === "TECHNICIAN" ? "Teknisyen" : 
                   user?.role === "MANAGER" ? "Proje Müdürü" : "Kullanıcı"}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Sidebar Menu */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-2">
            {menuSections.map((section, sectionIndex) => (
              <div key={section.id} className="mb-6">
                {/* Section Header */}
                {!isCollapsed && (
                  <div className="px-3 mb-2">
                    <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                      {section.label}
                    </h3>
                  </div>
                )}
                
                {/* Section Items */}
                <div className="space-y-1">
                  {section.items.map((item) => (
                    <div key={item.id}>
                      {item.children ? (
                        <div>
                          {/* Main item with dropdown toggle */}
                          <Button
                            variant="ghost"
                            className={cn(
                              "w-full justify-start p-3 text-gray-700 hover:bg-gray-100 hover:text-gray-900",
                              isCollapsed && "justify-center px-2",
                              (item.href && isItemActive(item.href)) && "bg-blue-50 text-blue-700",
                              mounted && expandedItems.includes(item.id) && "bg-gray-50"
                            )}
                            onClick={() => {
                              if (!isCollapsed) {
                                toggleExpanded(item.id)
                              }
                              // If has href, also navigate
                              if (item.href) {
                                handleNavigation(item.href)
                              }
                            }}
                          >
                            <item.icon className={cn(
                              "h-5 w-5 text-gray-600",
                              (item.href && isItemActive(item.href)) && "text-blue-600",
                              mounted && expandedItems.includes(item.id) && "text-blue-600"
                            )} />
                            {!isCollapsed && (
                              <>
                                <div className="ml-3 flex-1 text-left">
                                  <span className={cn(
                                    "font-medium",
                                    (item.href && isItemActive(item.href)) && "font-semibold"
                                  )}>{item.label}</span>
                                </div>
                                <ChevronDown className={cn(
                                  "ml-auto h-4 w-4 transition-transform duration-200 text-gray-400",
                                  mounted && expandedItems.includes(item.id) && "rotate-180 text-blue-600"
                                )} />
                              </>
                            )}
                          </Button>
                          
                          {!isCollapsed && mounted && expandedItems.includes(item.id) && (
                            <div className="ml-8 mt-1 py-1 space-y-1 border-l-2 border-gray-100 pl-4">
                              {item.children.map((child) => (
                                <button
                                  key={child.href}
                                  onClick={() => handleNavigation(child.href)}
                                  className={cn(
                                    "block w-full text-left px-3 py-2 text-sm rounded-md transition-colors",
                                    isItemActive(child.href) 
                                      ? "bg-blue-50 text-blue-700 border-l-2 border-blue-500 font-medium" 
                                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                                  )}
                                >
                                  {child.label}
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      ) : (
                        <button 
                          onClick={() => handleNavigation(item.href!)}
                          className={cn(
                            "w-full flex items-center justify-start p-3 text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-md transition-colors",
                            isItemActive(item.href!) && "bg-blue-50 text-blue-700 border-r-2 border-blue-500",
                            isCollapsed && "justify-center px-2"
                          )}
                        >
                          <item.icon className={cn(
                            "h-5 w-5",
                            isItemActive(item.href!) ? "text-blue-600" : "text-gray-600"
                          )} />
                          {!isCollapsed && (
                            <div className="ml-3 flex-1 text-left">
                              <span className={cn(
                                "font-medium",
                                isItemActive(item.href!) && "font-semibold"
                              )}>{item.label}</span>
                            </div>
                          )}
                        </button>
                      )}
                    </div>
                  ))}
                </div>
                
                {/* Section Separator */}
                {!isCollapsed && sectionIndex < menuSections.length - 1 && (
                  <Separator className="mt-4" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Sidebar Footer */}
        <div className="p-4 border-t bg-gray-50">
          <button
            type="button"
            className={cn(
              "w-full flex items-center justify-start p-3 text-gray-700 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors",
              isCollapsed && "justify-center"
            )}
            onClick={() => {
              signOut({ callbackUrl: "/auth/signin" });
            }}
          >
            <LogOut className="h-5 w-5" />
            {!isCollapsed && <span className="ml-3 font-medium">Çıkış Yap</span>}
          </button>
        </div>
      </div>
    </div>
  )
}
