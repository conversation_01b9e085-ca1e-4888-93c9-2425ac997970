const fs = require('fs');
const path = require('path');

class KeywordClassifier {
  constructor() {
    this.categories = new Map();
    this.keywords = new Map();
    this.confidenceThreshold = 0.3;
    this.isTrained = false;
    this.trainingStats = {
      totalSamples: 0,
      categories: {},
      trainingTime: 0
    };
  }

  /**
   * Modeli eğit
   * @param {Array} trainingData - Eğitim verileri
   */
  train(trainingData) {
    console.log('🔍 Keyword Classifier eğitimi başlıyor...');
    const startTime = Date.now();
    
    if (!Array.isArray(trainingData) || trainingData.length === 0) {
      throw new Error('Training data geçersiz veya boş');
    }

    // Kategori mapping'i oluştur
    trainingData.forEach(item => {
      this.categories.set(item.kategori, item.kategori_id);
    });

    // Her kategori için keyword'leri topla
    trainingData.forEach(item => {
      const category = item.kategori;
      const text = `${item.baslik} ${item.aciklama}`.toLowerCase();
      
      // Kelimeleri ayır ve temizle
      const words = text.split(/\s+/).filter(word => word.length > 2);
      
      if (!this.keywords.has(category)) {
        this.keywords.set(category, new Map());
      }
      
      const categoryKeywords = this.keywords.get(category);
      words.forEach(word => {
        categoryKeywords.set(word, (categoryKeywords.get(word) || 0) + 1);
      });
    });

    // Eğitim istatistiklerini kaydet
    this.trainingStats = {
      totalSamples: trainingData.length,
      categories: this.getCategoryDistribution(trainingData),
      trainingTime: Date.now() - startTime,
      totalKeywords: Array.from(this.keywords.values()).reduce((sum, cat) => sum + cat.size, 0)
    };

    this.isTrained = true;
    
    console.log(`✅ Keyword Classifier eğitimi tamamlandı!`);
    console.log(`   📊 Toplam örnek: ${this.trainingStats.totalSamples}`);
    console.log(`   ⏱️  Eğitim süresi: ${this.trainingStats.trainingTime}ms`);
    console.log(`   🏷️  Kategori sayısı: ${this.categories.size}`);
    console.log(`   🔑 Toplam keyword: ${this.trainingStats.totalKeywords}`);
  }

  /**
   * Kategori tahmini yap
   * @param {string} title - Arıza başlığı
   * @param {string} description - Arıza açıklaması
   * @returns {Object} Tahmin sonucu
   */
  predict(title, description) {
    if (!this.isTrained) {
      throw new Error('Model henüz eğitilmemiş');
    }

    if (!title || !description) {
      return {
        success: false,
        error: 'Başlık ve açıklama gereklidir',
        confidence: 0
      };
    }

    const text = `${title} ${description}`.toLowerCase();
    const words = text.split(/\s+/).filter(word => word.length > 2);
    
    const scores = new Map();
    const totalWords = words.length;

    // Her kategori için skor hesapla
    this.keywords.forEach((categoryKeywords, category) => {
      let score = 0;
      let matchedWords = 0;
      
      words.forEach(word => {
        if (categoryKeywords.has(word)) {
          score += categoryKeywords.get(word);
          matchedWords++;
        }
      });
      
      // Normalize et
      const normalizedScore = matchedWords > 0 ? (score / matchedWords) / totalWords : 0;
      scores.set(category, {
        score: normalizedScore,
        matchedWords: matchedWords,
        totalWords: totalWords
      });
    });

    // En yüksek skorlu kategoriyi bul
    let bestCategory = null;
    let bestScore = 0;
    
    scores.forEach((data, category) => {
      if (data.score > bestScore) {
        bestScore = data.score;
        bestCategory = category;
      }
    });

    // Güven eşiğini kontrol et
    if (bestScore < this.confidenceThreshold) {
      // Alternatifleri sırala
      const alternatives = Array.from(scores.entries())
        .sort((a, b) => b[1].score - a[1].score)
        .slice(0, 3)
        .map(([category, data]) => ({
          category: category,
          category_id: this.categories.get(category),
          confidence: data.score,
          matchedWords: data.matchedWords,
          totalWords: data.totalWords
        }));

      return {
        success: false,
        message: `Güven seviyesi düşük (${(bestScore * 100).toFixed(1)}%)`,
        confidence: bestScore,
        alternatives: alternatives
      };
    }

    // Alternatifleri hazırla
    const alternatives = Array.from(scores.entries())
      .filter(([category]) => category !== bestCategory)
      .sort((a, b) => b[1].score - a[1].score)
      .slice(0, 3)
      .map(([category, data]) => ({
        category: category,
        category_id: this.categories.get(category),
        confidence: data.score,
        matchedWords: data.matchedWords,
        totalWords: data.totalWords
      }));

    return {
      success: true,
      category: bestCategory,
      category_id: this.categories.get(bestCategory),
      confidence: bestScore,
      matchedWords: scores.get(bestCategory).matchedWords,
      totalWords: scores.get(bestCategory).totalWords,
      alternatives: alternatives,
      all_scores: Array.from(scores.entries()).map(([category, data]) => ({
        category: category,
        category_id: this.categories.get(category),
        confidence: data.score,
        matchedWords: data.matchedWords,
        totalWords: data.totalWords
      }))
    };
  }

  /**
   * Model performansını test et
   * @param {Array} testData - Test verileri
   * @returns {Object} Test sonuçları
   */
  test(testData) {
    if (!this.isTrained) {
      throw new Error('Model henüz eğitilmemiş');
    }

    console.log('🧪 Keyword Classifier testi başlıyor...');
    
    let correct = 0;
    let total = 0;
    const categoryResults = {};

    testData.forEach((item, index) => {
      const prediction = this.predict(item.baslik, item.aciklama);
      
      if (prediction.success && prediction.category === item.kategori) {
        correct++;
      }
      
      total++;

      // Kategori bazında sonuçları kaydet
      if (!categoryResults[item.kategori]) {
        categoryResults[item.kategori] = { correct: 0, total: 0 };
      }
      
      if (prediction.success && prediction.category === item.kategori) {
        categoryResults[item.kategori].correct++;
      }
      categoryResults[item.kategori].total++;

      // İlerleme göster
      if ((index + 1) % 20 === 0) {
        console.log(`  🧪 ${index + 1}/${testData.length} test tamamlandı`);
      }
    });

    const accuracy = (correct / total) * 100;
    this.trainingStats.accuracy = accuracy;

    console.log(`✅ Test tamamlandı!`);
    console.log(`   📊 Doğruluk: ${accuracy.toFixed(2)}%`);
    console.log(`   ✅ Doğru: ${correct}/${total}`);

    return {
      accuracy: accuracy,
      correct: correct,
      total: total,
      categoryResults: categoryResults
    };
  }

  /**
   * Kategori dağılımını hesapla
   * @param {Array} data - Veri seti
   * @returns {Object} Kategori dağılımı
   */
  getCategoryDistribution(data) {
    const distribution = {};
    data.forEach(item => {
      distribution[item.kategori] = (distribution[item.kategori] || 0) + 1;
    });
    return distribution;
  }

  /**
   * Model durumunu al
   * @returns {Object} Model durumu
   */
  getStatus() {
    return {
      isTrained: this.isTrained,
      totalSamples: this.trainingStats.totalSamples,
      categories: this.trainingStats.categories,
      accuracy: this.trainingStats.accuracy,
      trainingTime: this.trainingStats.trainingTime,
      totalKeywords: this.trainingStats.totalKeywords,
      confidenceThreshold: this.confidenceThreshold
    };
  }

  /**
   * Güven eşiğini ayarla
   * @param {number} threshold - Yeni eşik değeri (0-1 arası)
   */
  setConfidenceThreshold(threshold) {
    if (threshold < 0 || threshold > 1) {
      throw new Error('Güven eşiği 0-1 arasında olmalıdır');
    }
    this.confidenceThreshold = threshold;
    console.log(`🎯 Güven eşiği ${threshold} olarak ayarlandı`);
  }

  /**
   * En önemli keyword'leri göster
   * @param {string} category - Kategori adı
   * @param {number} limit - Gösterilecek keyword sayısı
   * @returns {Array} Keyword listesi
   */
  getTopKeywords(category, limit = 10) {
    if (!this.keywords.has(category)) {
      return [];
    }

    const categoryKeywords = this.keywords.get(category);
    return Array.from(categoryKeywords.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([word, count]) => ({ word, count }));
  }

  /**
   * Online learning - Yeni örnek ekle
   * @param {string} title - Arıza başlığı
   * @param {string} description - Arıza açıklaması
   * @param {string} category - Doğru kategori
   * @returns {Object} Öğrenme sonucu
   */
  learnFromExample(title, description, category) {
    if (!this.isTrained) {
      throw new Error('Model henüz eğitilmemiş');
    }

    console.log(`🧠 Yeni örnek öğreniliyor: "${title}" → ${category}`);

    // Kategoriyi kontrol et
    if (!this.categories.has(category)) {
      this.categories.set(category, this.categories.size + 1);
    }

    const text = `${title} ${description}`.toLowerCase();
    const words = text.split(/\s+/).filter(word => word.length > 2);
    
    // Kategori için keyword'leri güncelle
    if (!this.keywords.has(category)) {
      this.keywords.set(category, new Map());
    }
    
    const categoryKeywords = this.keywords.get(category);
    words.forEach(word => {
      categoryKeywords.set(word, (categoryKeywords.get(word) || 0) + 1);
    });

    // İstatistikleri güncelle
    this.trainingStats.totalSamples++;
    this.trainingStats.categories[category] = (this.trainingStats.categories[category] || 0) + 1;
    this.trainingStats.totalKeywords = Array.from(this.keywords.values()).reduce((sum, cat) => sum + cat.size, 0);

    console.log(`✅ Örnek başarıyla öğrenildi!`);
    console.log(`   📊 Toplam örnek: ${this.trainingStats.totalSamples}`);
    console.log(`   🔑 Toplam keyword: ${this.trainingStats.totalKeywords}`);

    return {
      success: true,
      message: 'Örnek başarıyla öğrenildi',
      category: category,
      wordsAdded: words.length,
      totalSamples: this.trainingStats.totalSamples,
      totalKeywords: this.trainingStats.totalKeywords
    };
  }

  /**
   * Modeli sıfırla
   */
  reset() {
    this.categories.clear();
    this.keywords.clear();
    this.isTrained = false;
    this.trainingStats = {
      totalSamples: 0,
      categories: {},
      trainingTime: 0
    };
    console.log('🔄 Keyword Classifier sıfırlandı');
  }
}

module.exports = KeywordClassifier; 