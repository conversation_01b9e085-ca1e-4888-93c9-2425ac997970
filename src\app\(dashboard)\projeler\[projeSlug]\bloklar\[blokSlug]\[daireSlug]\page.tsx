import { notFound } from "next/navigation";
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Home, PlusCircle, ChevronRight } from "lucide-react";
import { headers } from 'next/headers';
import { Suspense } from "react";
import { FaultsStats } from "@/components/faults/faults-stats";
import { FaultsList } from "@/components/faults/faults-list";
import { BreadcrumbWithLoading } from "@/components/ui/breadcrumb-with-loading";
import { NewFaultButton } from "@/components/ui/new-fault-button";
import { getInternalApiBaseUrl } from "@/lib/utils";

async function getApartmentBySlug(projeSlug: string, blokSlug: string, daireSlug: string) {
  const baseUrl = await getInternalApiBaseUrl();
  const res = await fetch(`${baseUrl}/api/projects/${projeSlug}/blocks/${blokSlug}/apartments/${daireSlug}`, { cache: 'no-store' });
  if (!res.ok) return null;
  return res.json();
}

export default async function DaireDetailPage({ params, searchParams }: { params: any, searchParams: any }) {
  const { projeSlug, blokSlug, daireSlug } = await params;
  const resolvedSearchParams = await searchParams;
  const daire = await getApartmentBySlug(projeSlug, blokSlug, daireSlug);
  if (!daire) return notFound();

  // Arıza filtreleri için searchParams'a daire id'sini ekle
  const plainSearchParams = Object.fromEntries(Object.entries(resolvedSearchParams || {}));
  const enhancedSearchParams = {
    ...plainSearchParams,
    daire: daire.id,
    projeSlug,
    blokSlug,
    daireSlug
  };

  const breadcrumbItems = [
    { label: "Ana Sayfa", href: "/dashboard" },
    { label: "Projeler", href: "/projeler" },
    { label: daire.proje_ad, href: `/projeler/${daire.proje_slug}` },
    { label: daire.blok_ad, href: `/projeler/${daire.proje_slug}/bloklar/${daire.blok_slug}` },
    { label: `Daire ${daire.numara}`, isActive: true }
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <BreadcrumbWithLoading items={breadcrumbItems} />

      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {daire.proje_ad} - {daire.blok_ad} - Daire {daire.numara}
          </h2>
          <p className="text-muted-foreground">
            Bu daireye ait arızaları görüntüleyin, filtreleyin ve yönetin
          </p>
        </div>
        <NewFaultButton projeSlug={projeSlug} blokSlug={blokSlug} daireSlug={daireSlug} />
      </div>
      {/* İstatistikler */}
      <Suspense fallback={<div>İstatistikler yükleniyor...</div>}>
        <FaultsStats apartmentId={daire.id} />
      </Suspense>
      <div className="grid gap-4">
        {/* Arıza Listesi */}
        <div>
          <Suspense fallback={<div>Arızalar yükleniyor...</div>}>
            <FaultsList searchParams={enhancedSearchParams} />
          </Suspense>
        </div>
      </div>
    </div>
  );
} 