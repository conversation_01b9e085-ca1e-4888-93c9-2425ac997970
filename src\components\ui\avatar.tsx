"use client"

import * as React from "react"
import * as AvatarPrimitive from "@radix-ui/react-avatar"
import Image from "next/image"

import { cn } from "@/lib/utils"

function Avatar({
  className,
  ...props
}: React.ComponentProps<typeof AvatarPrimitive.Root>) {
  return (
    <AvatarPrimitive.Root
      data-slot="avatar"
      className={cn(
        "relative flex size-8 shrink-0 overflow-hidden rounded-full",
        className
      )}
      {...props}
    />
  )
}

function AvatarImage({ className, src, alt = "Avatar", ...props }: React.ComponentProps<typeof AvatarPrimitive.Image> & { src?: string, alt?: string }) {
  // WebP varsa onu kullan
  let imageUrl = src || "";
  if (imageUrl.endsWith('.jpg') || imageUrl.endsWith('.png')) {
    imageUrl = imageUrl.replace(/\.(jpg|png)$/i, '.webp');
  }
  return (
    <Image
      src={imageUrl || "/images/project-placeholder.svg"}
      alt={alt}
      width={64}
      height={64}
      className={cn("aspect-square size-full rounded-full object-cover", className)}
      quality={80}
      placeholder="empty"
      onError={(e) => {
        const target = e.target as HTMLImageElement;
        target.src = "/images/project-placeholder.svg";
      }}
      {...props}
    />
  )
}

function AvatarFallback({
  className,
  ...props
}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {
  return (
    <AvatarPrimitive.Fallback
      data-slot="avatar-fallback"
      className={cn(
        "bg-muted flex size-full items-center justify-center rounded-full",
        className
      )}
      {...props}
    />
  )
}

export { Avatar, AvatarImage, AvatarFallback }
