"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "@/hooks/use-toast"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

const apartmentSchema = z.object({
  numara: z.string().min(1, "Daire numarası gereklidir").max(20, "Daire numarası çok uzun"),
  blok_id: z.string().min(1, "Blok seçimi gereklidir"),
  kat: z.string().optional(),
  aciklama: z.string().optional(),
})

type ApartmentFormData = z.infer<typeof apartmentSchema>

interface Project {
  id: string
  ad: string
}

interface Block {
  id: string
  ad: string
  proje_id: string
}

interface Apartment {
  id: string
  numara: string
  blok_id: string
  kat?: number
  tipi?: string
  metrekare?: number
  malik_ad?: string
  malik_telefon?: string
  malik_email?: string
  aciklama?: string
  blok?: {
    id: string
    ad: string
    slug: string
    proje: {
      id: string
      ad: string
      slug: string
    }
  }
}

interface ApartmentDialogProps {
  open: boolean
  onClose: () => void
  apartment?: Apartment | null
  preselectedProjectId?: string | null
  preselectedBlockId?: string | null
}

export function ApartmentDialog({ 
  open, 
  onClose, 
  apartment, 
  preselectedProjectId,
  preselectedBlockId 
}: ApartmentDialogProps) {
  const [loading, setLoading] = useState(false)
  const [projects, setProjects] = useState<Project[]>([])
  const [blocks, setBlocks] = useState<Block[]>([])
  const [selectedProject, setSelectedProject] = useState<string>("")
  const [selectedBlockInfo, setSelectedBlockInfo] = useState<Block | null>(null)
  const [selectedProjectInfo, setSelectedProjectInfo] = useState<Project | null>(null)
  const isEditing = !!apartment
  const form = useForm<ApartmentFormData>({
    resolver: zodResolver(apartmentSchema),
    defaultValues: {
      numara: "",
      blok_id: preselectedBlockId || "",
      kat: "",
      aciklama: "",
    },
  })

  // Fetch projects
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await fetch("/api/projects?mode=dropdown")
        if (!response.ok) throw new Error("Failed to fetch projects")
        const data = await response.json()
        setProjects(data.projects)
      } catch (error) {
        console.error("Error fetching projects:", error)
      }
    }
    fetchProjects()
  }, [])

  // Fetch blocks when project changes
  useEffect(() => {
    const fetchBlocks = async () => {
      if (!selectedProject) {
        setBlocks([])
        return
      }
      try {
        const response = await fetch(`/api/projects/${selectedProject}/blocks`)
        if (!response.ok) throw new Error("Failed to fetch blocks")
        const data = await response.json()
        setBlocks(data.blocks.map((block: { id: string; ad: string; proje_id: string }) => ({
          id: block.id,
          ad: block.ad,
          proje_id: block.proje_id
        })))
      } catch (error) {
        console.error("Error fetching blocks:", error)
      }
    }
    fetchBlocks()  }, [selectedProject])

  // Handle preselected values from URL parameters
  useEffect(() => {
    if (preselectedProjectId && !apartment) {
      setSelectedProject(preselectedProjectId)
    }
  }, [preselectedProjectId, apartment])

  useEffect(() => {
    if (preselectedBlockId && !apartment) {
      form.setValue("blok_id", preselectedBlockId)
    }
  }, [preselectedBlockId, apartment, form])

  // Fetch preselected block and project info
  useEffect(() => {
    if ((preselectedBlockId || preselectedProjectId) && !apartment) {
      const fetchPreselectedInfo = async () => {
        try {
          // Fetch block info if preselectedBlockId exists
          if (preselectedBlockId) {
            const blockResponse = await fetch(`/api/blocks/${preselectedBlockId}`)
            if (blockResponse.ok) {
              const blockData = await blockResponse.json()
              setSelectedBlockInfo(blockData)
              
              // Fetch project info from block data
              if (blockData.proje_id) {
                const projectResponse = await fetch(`/api/projects/${blockData.proje_id}`)
                if (projectResponse.ok) {
                  const projectData = await projectResponse.json()
                  setSelectedProjectInfo(projectData)
                }
              }
            }
          }
          // Fetch project info if only preselectedProjectId exists (without block)
          else if (preselectedProjectId && !preselectedBlockId) {
            const projectResponse = await fetch(`/api/projects/${preselectedProjectId}`)
            if (projectResponse.ok) {
              const projectData = await projectResponse.json()
              setSelectedProjectInfo(projectData)
            }
          }
        } catch (error) {
          console.error("Error fetching preselected info:", error)
        }
      }
      fetchPreselectedInfo()
    }
  }, [preselectedBlockId, preselectedProjectId, apartment])

  // Debug log to check values
  useEffect(() => {
    console.log("Apartment Dialog Debug:", {
      preselectedBlockId,
      formBlokId: form.getValues("blok_id"),
      isEditing: !!apartment
    })
  }, [preselectedBlockId, form])

  useEffect(() => {
    if (apartment) {
      // Find project for this apartment's block (slug tabanlı)
      const findProjectForBlock = async () => {
        try {
          // Eğer apartment.blok bilgisi varsa, slug tabanlı API kullan
          if (apartment.blok?.proje?.slug && apartment.blok?.slug) {
            const response = await fetch(`/api/projects/${apartment.blok.proje.slug}/blocks/${apartment.blok.slug}`)
            if (response.ok) {
              const blockData = await response.json()
              setSelectedProject(blockData.proje.slug)
            }
          } else {
            // Fallback: eski id tabanlı API (geçici)
            const response = await fetch(`/api/blocks/${apartment.blok_id}`)
            if (response.ok) {
              const blockData = await response.json()
              setSelectedProject(blockData.proje_id)
            }
          }
        } catch (error) {
          console.error("Error fetching block details:", error)
        }
      }
      findProjectForBlock()
      
      form.reset({
        numara: apartment.numara,
        blok_id: apartment.blok_id,
        kat: apartment.kat ? apartment.kat.toString() : "",
        aciklama: apartment.aciklama || "",
      })
    } else {
      setSelectedProject("")
      form.reset({
        numara: "",
        blok_id: preselectedBlockId || "",
        kat: "",
        aciklama: "",
      })
    }
  }, [apartment, form])

  const handleSubmit = async (data: ApartmentFormData) => {
    console.log("Form Values:", form.getValues())
    
    try {
      setLoading(true)

      const payload = {
        numara: data.numara,
        blok_id: data.blok_id,
        kat: data.kat && data.kat.trim() !== "" ? parseInt(data.kat) : null,
        aciklama: data.aciklama || null,
      }


      const url = isEditing ? `/api/apartments/${apartment.id}` : "/api/apartments"
      const method = isEditing ? "PUT" : "POST"
      

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "İşlem başarısız")
      }

      // Başarı bildirimi
      toast.success(
        isEditing ? "Daire başarıyla güncellendi!" : "Yeni daire başarıyla oluşturuldu!",
        {
          title: "Başarılı",
          duration: 4000
        }
      )

      onClose()
    } catch (error) {
      console.error("Error saving apartment:", error)
      
      // Hata bildirimi  
      toast.error(
        error instanceof Error ? error.message : "Beklenmeyen bir hata oluştu",
        {
          title: "Hata",
          duration: 6000
        }
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Daireyi Düzenle" : "Yeni Daire Ekle"}
          </DialogTitle>
        </DialogHeader>        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* Project and Block Selection */}
            <FormField
              control={form.control}
              name="blok_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Proje *</FormLabel>
                  <div className="py-2 px-3 bg-muted rounded text-sm font-medium">
                    {selectedProjectInfo?.ad ||
                     (Array.isArray(projects)
                      ? projects.find(p => p.id === preselectedProjectId)?.ad || "Proje"
                      : "Proje"
                     )
                    }
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="blok_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Blok *</FormLabel>
                  <div className="py-2 px-3 bg-muted rounded text-sm font-medium">
                    {selectedBlockInfo?.ad ||
                     (Array.isArray(blocks)
                      ? blocks.find(b => b.id === preselectedBlockId)?.ad || "Blok"
                      : "Blok"
                     )
                    }
                  </div>
                  <FormControl>
                    <input type="hidden" {...field} value={preselectedBlockId || field.value || ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Apartment Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="numara"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Daire Numarası *</FormLabel>
                    <FormControl>
                      <Input placeholder="Örn: 1, A1, 2B" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="kat"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kat</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="Kat numarası"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div><FormField
              control={form.control}
              name="aciklama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Daire açıklaması (opsiyonel)"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                İptal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Kaydediliyor..." : isEditing ? "Güncelle" : "Kaydet"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
