"use client"

import React, { useReducer, useEffect, useRef, useState } from 'react';
import { Trash2, Upload, X } from 'lucide-react';
import <PERSON>rop<PERSON>, { Area } from 'react-easy-crop';

export interface MediaFile {
  url: string;
  originalName: string;
  size: number;
  alt?: string;
}

interface MediaState {
  items: MediaFile[];
  loading: boolean;
  error: string | null;
  selectedItem: MediaFile | null;
  recentlyUploaded: string | null;
}

type MediaAction = 
  | { type: 'LOAD_START' }
  | { type: 'LOAD_SUCCESS'; payload: MediaFile[] }
  | { type: 'LOAD_ERROR'; payload: string }
  | { type: 'ADD_ITEM'; payload: MediaFile }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'SELECT_ITEM'; payload: MediaFile | null }
  | { type: 'MARK_RECENT'; payload: string }
  | { type: 'CLEAR_RECENT' }
  | { type: 'CLEAR_ERROR' };

const initialState: MediaState = {
  items: [],
  loading: false,
  error: null,
  selectedItem: null,
  recentlyUploaded: null,
};

const mediaReducer = (state: MediaState, action: MediaAction): MediaState => {
  console.log(`🔄 REDUCER: ${action.type}`, action);
  
  switch (action.type) {
    case 'LOAD_START':
      return { ...state, loading: true, error: null };
      
    case 'LOAD_SUCCESS':
      // Prevent duplicates at the reducer level
      const uniqueItems = action.payload.filter((newItem, index, arr) => 
        arr.findIndex(item => item.url === newItem.url) === index
      );
      console.log(`📊 REDUCER: Loaded ${action.payload.length} items, ${uniqueItems.length} unique`);
      return { ...state, items: uniqueItems, loading: false, error: null };
      
    case 'LOAD_ERROR':
      return { ...state, loading: false, error: action.payload };
      
    case 'ADD_ITEM':
      // Prevent duplicates by URL
      const exists = state.items.some(item => item.url === action.payload.url);
      if (exists) {
        console.log(`🚫 REDUCER: Duplicate prevented for ${action.payload.url}`);
        return state;
      }
      console.log(`✅ REDUCER: Added new item ${action.payload.url}`);
      return { ...state, items: [action.payload, ...state.items] };
      
    case 'REMOVE_ITEM':
      return { 
        ...state, 
        items: state.items.filter(item => item.url !== action.payload),
        selectedItem: state.selectedItem?.url === action.payload ? null : state.selectedItem
      };
      
    case 'SELECT_ITEM':
      return { ...state, selectedItem: action.payload };
      
    case 'MARK_RECENT':
      return { ...state, recentlyUploaded: action.payload };
      
    case 'CLEAR_RECENT':
      return { ...state, recentlyUploaded: null };
      
    case 'CLEAR_ERROR':
      return { ...state, error: null };
      
    default:
      return state;
  }
};

interface NewMediaSelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (media: MediaFile | undefined) => void;
  selectedMedia?: MediaFile | null;
  title?: string;
  description?: string;
  folder?: string;
  acceptedTypes?: string[];
  maxSizeMB?: number;
}

export const NewMediaSelector: React.FC<NewMediaSelectorProps> = ({
  open,
  onClose,
  onSelect,
  selectedMedia,
  title = 'Select Media',
  description = '',
  folder = 'media',
  acceptedTypes = ['image/*'],
  maxSizeMB = 5,
}) => {
  const [state, dispatch] = useReducer(mediaReducer, initialState);
  const [tab, setTab] = useState<'gallery' | 'upload'>('gallery');
  const [search, setSearch] = useState('');
  
  // Crop state
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [cropImageUrl, setCropImageUrl] = useState<string | null>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load media when modal opens and gallery tab is active
  useEffect(() => {
    if (open && tab === 'gallery') {
      loadMedia();
    }
  }, [open, tab, folder]);

  // Clear recent indicator after 3 seconds
  useEffect(() => {
    if (state.recentlyUploaded) {
      const timer = setTimeout(() => {
        dispatch({ type: 'CLEAR_RECENT' });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [state.recentlyUploaded]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      dispatch({ type: 'SELECT_ITEM', payload: null });
      dispatch({ type: 'CLEAR_RECENT' });
      dispatch({ type: 'CLEAR_ERROR' });
      setSearch('');
      setCropModalOpen(false);
      setCropImageUrl(null);
      setPendingFile(null);
    }
  }, [open]);

  const loadMedia = async () => {
    console.log('🔄 Loading media from API');
    dispatch({ type: 'LOAD_START' });

    try {
      const res = await fetch(`/api/media?customFolder=${folder}`);
      const data = await res.json();
      const mediaList = data.data || [];

      console.log(`📥 API Response: ${mediaList.length} items`);
      console.log('📥 API Response URLs:', mediaList.map((m: any) => m.url));

      // Check for duplicates in API response
      const urls = mediaList.map((m: any) => m.url);
      const uniqueUrls = new Set(urls);
      if (urls.length !== uniqueUrls.size) {
        console.error('🚨 API RETURNING DUPLICATES!', {
          total: urls.length,
          unique: uniqueUrls.size,
          duplicates: urls.filter((url: string, index: number) => urls.indexOf(url) !== index)
        });
      }

      dispatch({ type: 'LOAD_SUCCESS', payload: mediaList });
    } catch (error) {
      console.error('❌ Load error:', error);
      dispatch({ type: 'LOAD_ERROR', payload: 'Failed to load media' });
    }
  };

  const uploadFile = async (file: File): Promise<MediaFile | null> => {
    console.log('📤 Uploading file:', file.name);
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', folder);
    
    try {
      const res = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      const data = await res.json();
      
      if (data && data.media) {
        console.log('✅ Upload successful:', data.media.url);
        return data.media;
      } else {
        throw new Error(data.error || 'Upload failed');
      }
    } catch (error) {
      console.error('❌ Upload error:', error);
      throw error;
    }
  };

  const handleFileSelect = (file: File) => {
    if (file.size > maxSizeMB * 1024 * 1024) {
      dispatch({ type: 'LOAD_ERROR', payload: `File too large. Maximum size: ${maxSizeMB}MB` });
      return;
    }
    
    if (acceptedTypes[0] !== 'image/*' && !acceptedTypes.some(type => file.type === type)) {
      dispatch({ type: 'LOAD_ERROR', payload: 'Unsupported file format' });
      return;
    }

    // Auto-open crop modal for upload tab
    setPendingFile(file);
    setCropImageUrl(URL.createObjectURL(file));
    setCropModalOpen(true);
  };

  const handleCropComplete = async () => {
    if (!cropImageUrl || !croppedAreaPixels || !pendingFile) return;
    
    try {
      const croppedBlob = await getCroppedImg(cropImageUrl, croppedAreaPixels);
      const croppedFile = new File([croppedBlob], `cropped_${Date.now()}.jpg`, { type: 'image/jpeg' });
      
      // Upload the cropped file
      const uploadedMedia = await uploadFile(croppedFile);
      
      if (uploadedMedia) {
        // Add to state through reducer
        dispatch({ type: 'ADD_ITEM', payload: uploadedMedia });
        dispatch({ type: 'SELECT_ITEM', payload: uploadedMedia });
        dispatch({ type: 'MARK_RECENT', payload: uploadedMedia.url });
        
        // Switch to gallery tab
        setTab('gallery');
      }
      
      // Clean up
      setCropModalOpen(false);
      setCropImageUrl(null);
      setPendingFile(null);
      
    } catch (error) {
      console.error('❌ Crop/Upload error:', error);
      dispatch({ type: 'LOAD_ERROR', payload: 'Failed to process image' });
    }
  };

  // Crop helper function
  const getCroppedImg = async (imageSrc: string, crop: Area): Promise<Blob> => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement('canvas');
    canvas.width = crop.width;
    canvas.height = crop.height;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) throw new Error('Failed to get canvas context');
    
    ctx.drawImage(
      image,
      crop.x,
      crop.y,
      crop.width,
      crop.height,
      0,
      0,
      crop.width,
      crop.height
    );
    
    return new Promise(resolve => {
      canvas.toBlob(blob => {
        if (blob) resolve(blob);
      }, 'image/jpeg', 0.95);
    });
  };

  const createImage = (url: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new window.Image();
      img.addEventListener('load', () => resolve(img));
      img.addEventListener('error', error => reject(error));
      img.setAttribute('crossOrigin', 'anonymous');
      img.src = url;
    });
  };

  const handleDelete = async (mediaUrl: string) => {
    try {
      const res = await fetch(`/api/media?url=${encodeURIComponent(mediaUrl)}`, { 
        method: 'DELETE' 
      });
      
      if (res.ok) {
        dispatch({ type: 'REMOVE_ITEM', payload: mediaUrl });
      } else {
        throw new Error('Delete failed');
      }
    } catch (error) {
      console.error('❌ Delete error:', error);
      dispatch({ type: 'LOAD_ERROR', payload: 'Failed to delete media' });
    }
  };

  const filteredMedia = search
    ? state.items.filter(m => m.originalName.toLowerCase().includes(search.toLowerCase()))
    : state.items;

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-3xl w-full min-h-[400px] flex flex-col relative">

        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-bold">{title}</h2>
            {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition"
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex gap-2 mb-4">
          <button
            onClick={() => setTab('gallery')}
            className={`px-4 py-2 rounded transition ${
              tab === 'gallery'
                ? 'bg-blue-100 text-blue-700 font-medium'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Gallery
          </button>
          <button
            onClick={() => setTab('upload')}
            className={`px-4 py-2 rounded transition ${
              tab === 'upload'
                ? 'bg-blue-100 text-blue-700 font-medium'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Upload
          </button>
        </div>

        {/* Error Display */}
        {state.error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            {state.error}
            <button
              onClick={() => dispatch({ type: 'CLEAR_ERROR' })}
              className="ml-2 text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Content */}
        <div className="flex-1">
          {tab === 'gallery' && (
            <div>
              {/* Search */}
              <input
                type="text"
                placeholder="Search files..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full p-2 border rounded mb-4"
              />

              {/* Loading */}
              {state.loading && (
                <div className="text-center py-8">
                  <div className="animate-spin w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading media...</p>
                </div>
              )}

              {/* Gallery Grid */}
              {!state.loading && (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
                  {filteredMedia.length === 0 && (
                    <div className="col-span-full text-center text-gray-400 py-8">
                      No files found
                    </div>
                  )}
                  {filteredMedia.map((media) => (
                    <div
                      key={media.url}
                      className={`relative border rounded cursor-pointer p-2 transition-all ${
                        state.selectedItem?.url === media.url
                          ? 'border-blue-500 ring-2 ring-blue-300'
                          : state.recentlyUploaded === media.url
                          ? 'border-green-500 ring-2 ring-green-300 bg-green-50'
                          : 'border-gray-200 hover:border-blue-300'
                      }`}
                      onClick={() => dispatch({ type: 'SELECT_ITEM', payload: media })}
                    >
                      <img
                        src={media.url}
                        alt={media.originalName}
                        className="w-full h-24 object-cover rounded mb-2"
                      />
                      <div className="text-xs text-center truncate">{media.originalName}</div>

                      {/* Recent badge */}
                      {state.recentlyUploaded === media.url && (
                        <div className="absolute top-2 left-2 px-1 py-0.5 bg-green-500 text-white text-xs rounded">
                          New
                        </div>
                      )}

                      {/* Delete button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(media.url);
                        }}
                        className="absolute top-2 right-2 p-1 bg-white rounded-full shadow hover:bg-red-100 transition"
                        title="Delete"
                      >
                        <Trash2 className="w-3 h-3 text-red-500" />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Selected Item Display */}
              {state.selectedItem && (
                <div className="mt-6 p-4 border rounded-lg bg-gray-50">
                  <h3 className="font-medium mb-2">Selected File</h3>
                  <div className="flex items-center gap-3">
                    <img
                      src={state.selectedItem.url}
                      alt={state.selectedItem.originalName}
                      className="w-16 h-16 object-cover rounded border"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-sm">{state.selectedItem.originalName}</div>
                      <div className="text-xs text-gray-500">{(state.selectedItem.size / 1024).toFixed(2)} KB</div>
                    </div>
                    <button
                      onClick={() => dispatch({ type: 'SELECT_ITEM', payload: null })}
                      className="p-2 hover:bg-gray-200 rounded transition"
                      title="Deselect"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {tab === 'upload' && (
            <div>
              {/* Upload Area */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
                onDrop={(e) => {
                  e.preventDefault();
                  const file = e.dataTransfer.files[0];
                  if (file) handleFileSelect(file);
                }}
                onDragOver={(e) => e.preventDefault()}
              >
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-700 mb-2">
                  Drop files here or click to upload
                </p>
                <p className="text-sm text-gray-500">
                  Supported formats: {acceptedTypes.join(', ')} | Max size: {maxSizeMB}MB
                </p>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept={acceptedTypes.join(',')}
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileSelect(file);
                    e.target.value = '';
                  }}
                  className="hidden"
                />
              </div>

              {/* Pending File Display */}
              {pendingFile && (
                <div className="mt-6 p-4 border rounded-lg bg-blue-50">
                  <h3 className="font-medium mb-2">Processing File</h3>
                  <div className="flex items-center gap-3">
                    <img
                      src={cropImageUrl || ''}
                      alt={pendingFile.name}
                      className="w-16 h-16 object-cover rounded border"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-sm">{pendingFile.name}</div>
                      <div className="text-xs text-gray-500">{(pendingFile.size / 1024).toFixed(2)} KB</div>
                      <div className="text-xs text-blue-600 mt-1">Complete cropping to upload</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer Actions */}
        {state.selectedItem && (
          <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                onSelect(state.selectedItem || undefined);
                onClose();
              }}
              className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            >
              Select Media
            </button>
          </div>
        )}
      </div>

      {/* Crop Modal */}
      {cropModalOpen && cropImageUrl && (
        <div className="fixed inset-0 z-60 flex items-center justify-center bg-black bg-opacity-60">
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold">Crop Image</h3>
              <button
                onClick={() => setCropModalOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="relative w-full h-96 bg-gray-100 rounded mb-4">
              <Cropper
                image={cropImageUrl}
                crop={crop}
                zoom={zoom}
                aspect={16 / 9}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={(_, croppedPixels) => setCroppedAreaPixels(croppedPixels)}
              />
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <label className="text-sm font-medium">Zoom:</label>
                <input
                  type="range"
                  min={1}
                  max={3}
                  step={0.1}
                  value={zoom}
                  onChange={(e) => setZoom(Number(e.target.value))}
                  className="w-32"
                />
                <span className="text-sm text-gray-600">{zoom.toFixed(1)}x</span>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => setCropModalOpen(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCropComplete}
                  className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
                >
                  Crop & Upload
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
