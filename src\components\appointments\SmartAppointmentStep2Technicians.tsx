import React, { useState, useEffect } from "react"

interface Step2Props {
  data: any
  onNext: (data: any) => void
  onBack: () => void
}

interface Technician {
  id: string
  ad: string
  soyad: string
  uzmanlikAlanlari: { id: string; ad: string; renk: string }[]
}

export default function Step2TechnicianSelection({ data, onNext, onBack }: Step2Props) {
  const [technicians, setTechnicians] = useState<Technician[]>([])
  const [selected, setSelected] = useState<string[]>(data.technicians || [])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    setLoading(true)
    fetch("/api/technicians")
      .then(res => res.json())
      .then(list => setTechnicians(list))
      .finally(() => setLoading(false))
  }, [])

  const handleSelect = (id: string) => {
    setSelected((prev) => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id])
  }

  const handleNext = () => {
    onNext({ technicians: selected })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-2">
        <button type="button" className="bg-gray-100 dark:bg-zinc-800 text-gray-600 dark:text-zinc-300 px-4 py-2 rounded-lg border border-gray-200 dark:border-zinc-700 hover:bg-gray-200 dark:hover:bg-zinc-700 transition" onClick={onBack}>Geri</button>
        <h2 className="text-lg font-bold">Teknisyen Seçimi</h2>
      </div>
      {loading ? (
        <div className="text-center py-8 text-gray-400">Yükleniyor...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {technicians.map(t => (
            <div
              key={t.id}
              className={`flex items-center gap-4 p-4 rounded-xl border-2 transition cursor-pointer shadow-sm
                ${selected.includes(t.id) ? 'border-blue-600 bg-blue-50 dark:bg-blue-900/30' : 'border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 hover:border-blue-400'}
              `}
              onClick={() => handleSelect(t.id)}
            >
              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg
                ${selected.includes(t.id) ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-zinc-800 text-gray-500'}`}
              >
                {t.ad.charAt(0)}{t.soyad.charAt(0)}
              </div>
              <div className="flex-1">
                <div className="font-semibold text-gray-800 dark:text-zinc-100">{t.ad} {t.soyad}</div>
                <div className="flex flex-wrap gap-1 mt-1">
                  {t.uzmanlikAlanlari.map(u => (
                    <span key={u.id} className="text-xs px-2 py-0.5 rounded bg-gray-100 dark:bg-zinc-800 text-gray-600 dark:text-zinc-300 border border-gray-200 dark:border-zinc-700">{u.ad}</span>
                  ))}
                </div>
              </div>
              <input
                type="checkbox"
                checked={selected.includes(t.id)}
                readOnly
                className="w-5 h-5 accent-blue-600 border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              />
            </div>
          ))}
        </div>
      )}
      <div className="flex justify-end pt-2">
        <button
          type="button"
          onClick={handleNext}
          disabled={selected.length === 0}
          className="bg-green-600 hover:bg-green-700 text-white font-semibold px-8 py-2 rounded-lg shadow transition disabled:opacity-50"
        >
          Özete Geç
        </button>
      </div>
    </div>
  )
} 