# Enum Yönetim Sistemi Dokümantasyonu

## Merkezi Enum Sistemi

Bu projede tüm enum değerleri merkezi olarak `/src/lib/enums.ts` dosyasından yönetilmektedir.

## Arıza <PERSON> (Fault Status)

### Durum Akışı
```
Açık → Devam Ediyor → Beklemede → Çözüldü
  ↓                      ↓           ↑
 İptal ←------------------←----------←
```

### Durum Açıklamaları

#### 🔴 **Açık (ACIK)**
- **Açıklama**: <PERSON><PERSON> bild<PERSON>len, henüz işleme alınmamış arızalar
- **Renk**: Kırmızı (`#EF4444`)
- **Kullanım**: İlk bildirim anında otomatik olarak atanır
- **Aksiyonlar**: Teknisyen ataması, öncelik belirleme

#### 🔵 **<PERSON><PERSON> (DEVAM_EDIYOR)**
- **Açıklama**: Üzerinde aktif olarak çalışılan arızalar
- **Renk**: Mavi (`#3B82F6`)
- **Kullanım**: Teknisyen çalışmaya başladığında
- **Aksiyonlar**: İlerleme raporları, malzeme talebi

#### 🟡 **Beklemede (BEKLEMEDE)**
- **Açıklama**: Malzeme, onay veya dış faktör bekleyen arızalar
- **Renk**: Sarı (`#F59E0B`)
- **Kullanım**: Beklenen durumu belirtmek için
- **Örnekler**: 
  - Malzeme siparişi bekleniyor
  - Yönetici onayı bekleniyor
  - Hava koşulları bekleniyor
  - Üçüncü taraf servis bekleniyor

#### 🟢 **Çözüldü (COZULDU)**
- **Açıklama**: Tamamen çözülmüş ve kapatılmış arızalar
- **Renk**: Yeşil (`#10B981`)
- **Kullanım**: Arıza tamamen bittiğinde
- **Özellikler**: 
  - Son durum - daha değiştirilemez
  - Kullanıcı onayı alınmış
  - Test edilmiş ve doğrulanmış

#### ⚫ **İptal (IPTAL)**
- **Açıklama**: İptal edilmiş veya geçersiz arızalar
- **Renk**: Gri (`#6B7280`)
- **Kullanım**: 
  - Yanlış bildirim
  - Dublicate kayıt
  - Kullanıcı isteği ile iptal
  - Teknik olarak mümkün değil

## Çözüldü vs Tamamlandı Farkı

### ❌ **Tamamlandı (Kaldırıldı)**
Bu durum sistemden kaldırılmıştır çünkü "Çözüldü" ile aynı anlama geliyordu.

### ✅ **Çözüldü (Mevcut)**
- Arızanın teknik olarak çözümlendiğini ifade eder
- Son kullanıcı tarafından test edilmiş ve onaylanmış
- Herhangi bir ek iş gerektirmez
- Kapalı bilet anlamına gelir

## Aciliyet Seviyeleri (Priority)

### 🟢 **Düşük (DUSUK)** - Seviye 1
- Günlük yaşamı etkilemeyen
- 1-2 hafta içinde çözülebilir
- Örnek: Boyar dökülmesi, küçük kozmetik sorunlar

### 🟡 **Orta (ORTA)** - Seviye 2  
- Günlük yaşamı kısmen etkileyen
- 3-7 gün içinde çözülmeli
- Örnek: Aydınlatma sorunları, küçük sızıntılar

### 🟠 **Yüksek (YUKSEK)** - Seviye 3
- Günlük yaşamı ciddi etkileyen
- 1-3 gün içinde çözülmeli  
- Örnek: Elektrik kesintisi, ısınma sorunu

### 🔴 **Kritik (KRITIK)** - Seviye 4
- Acil müdahale gerektiren
- 24 saat içinde çözülmeli
- Örnek: Su kaçağı, asansör arızası, güvenlik sorunu

## Kullanım Örnekleri

### Frontend'de Enum Kullanımı
```typescript
import { FaultStatus, FAULT_STATUS_LABELS, FAULT_STATUS_COLORS } from '@/lib/enums'

// Status label gösterimi
const statusLabel = FAULT_STATUS_LABELS[FaultStatus.ACIK] // "Açık"

// Status rengi
const statusColor = FAULT_STATUS_COLORS[FaultStatus.ACIK] // "bg-red-100..."

// Select options oluşturma
const statusOptions = enumToSelectOptions(FaultStatus, FAULT_STATUS_LABELS)
```

### API'de Enum Kullanımı
```typescript
import { FaultStatus } from '@/lib/enums'

// Filtreleme
where: {
  durum: {
    ad: { in: [FAULT_STATUS_LABELS[FaultStatus.ACIK]] }
  }
}
```

## Enum Güncelleme Rehberi

1. **Yeni Enum Ekleme**: `/src/lib/enums.ts` dosyasını güncelleyin
2. **Veritabanı Güncelleme**: Migration script oluşturun
3. **Frontend Güncelleme**: Validation şemalarını güncelleyin
4. **Test**: Tüm kullanım alanlarını test edin

## Veritabanı Senkronizasyonu

Enum değerleri veritabanı ile senkronize tutulmak için:

```bash
# Enum güncelleme
npx tsx scripts/update-enums.ts

# Dublicate temizleme
npx tsx scripts/cleanup-duplicate-statuses.ts
```

## İyi Pratikler

1. ✅ **Merkezi enum kullanın**: Doğrudan string yerine enum sabitleri kullanın
2. ✅ **Label mapping kullanın**: UI'da sabitleri değil, label'ları gösterin  
3. ✅ **Type safety**: TypeScript enum tiplerini kullanın
4. ✅ **Tutarlılık**: Aynı enum değerini her yerde aynı şekilde kullanın
5. ❌ **Hard-coded string'ler kullanmayın**: "Açık" yerine `FaultStatus.ACIK` kullanın

## Örnek: Doğru Kullanım

```typescript
// ❌ Yanlış
if (fault.durum === "Açık") { ... }

// ✅ Doğru  
if (fault.durum === FAULT_STATUS_LABELS[FaultStatus.ACIK]) { ... }

// ✅ Daha da iyi
if (fault.status === FaultStatus.ACIK) { ... }
```
