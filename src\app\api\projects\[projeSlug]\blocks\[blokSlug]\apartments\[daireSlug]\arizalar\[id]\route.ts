import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug, id: faultSlug } = await params;

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Arızayı getir (slug ile)
    const fault = await prisma.ariza.findFirst({
      where: { 
        slug: faultSlug,
        daire_id: apartment.id, 
        silindi_mi: false 
      },
      include: {
        daire: {
          select: {
            id: true,
            slug: true,
            blok: {
              select: {
                id: true,
                ad: true,
                slug: true,
                proje: {
                  select: {
                    id: true,
                    ad: true,
                    slug: true
                  }
                }
              }
            }
          }
        },
        tip: true,
        durum: true,
        aciliyet: true,
        randevular: {
          where: {
            silindi_mi: false
          },
          include: {
            teknisyenler: {
              include: {
                teknisyen: {
                  select: {
                    id: true,
                    ad: true,
                    soyad: true,
                    email: true,
                    telefon: true,
                    resim: true
                  }
                }
              }
            }
          },
          orderBy: {
            randevu_tarihi: 'asc'
          }
        }
      }
    });

    if (!fault) {
      return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 });
    }

    // Randevulara slug bilgilerini ekle
    const randevularWithSlugs = (fault.randevular || []).map((r) => ({
      ...r,
      daire: {
        slug: fault.daire.slug,
        blok: {
          slug: fault.daire.blok.slug,
          proje: {
            slug: fault.daire.blok.proje.slug
          }
        }
      },
      ariza_id: fault.id
    }))
    
    const response = NextResponse.json({
      ...fault,
      randevular: randevularWithSlugs
    });
    // Prevent caching to ensure fresh data
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
  } catch (error) {
    console.error("Arıza detayı alınırken hata:", error);
    return NextResponse.json(
      { error: "Sunucu hatası" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug, id: faultSlug } = await params;
    const body = await request.json();

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Arızayı bul (slug ile)
    const existingFault = await prisma.ariza.findFirst({
      where: {
        slug: faultSlug,
        daire_id: apartment.id,
        silindi_mi: false
      }
    });

    if (!existingFault) {
      return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 });
    }

    // Arızayı güncelle
    const updatedFault = await prisma.ariza.update({
      where: { id: existingFault.id },
      data: {
        baslik: body.baslik,
        aciklama: body.aciklama,
        ariza_tip_id: body.ariza_tip_id,
        durum_id: body.durum_id || existingFault.durum_id,
        aciliyet_id: body.aciliyet_id || existingFault.aciliyet_id,
        guncelleyen_id: "system", // Use system or session user
      },
      include: {
        daire: {
          select: {
            id: true,
            slug: true,
            blok: {
              select: {
                id: true,
                ad: true,
                slug: true,
                proje: {
                  select: {
                    id: true,
                    ad: true,
                    slug: true
                  }
                }
              }
            }
          }
        },
        tip: true,
        durum: true,
        aciliyet: true,
        randevular: {
          where: {
            silindi_mi: false
          },
          include: {
            teknisyenler: {
              include: {
                teknisyen: {
                  select: {
                    id: true,
                    ad: true,
                    soyad: true,
                    email: true,
                    telefon: true,
                    resim: true
                  }
                }
              }
            }
          },
          orderBy: {
            randevu_tarihi: 'asc'
          }
        }
      }
    });

    return NextResponse.json(updatedFault);
  } catch (error) {
    console.error("Arıza güncelleme hatası:", error);
    return NextResponse.json(
      { error: "Arıza güncellenirken bir hata oluştu" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string }> }
) {
  try {
    const { projeSlug, blokSlug, daireSlug, id: faultSlug } = await params;

    // Proje, blok ve daireyi bul
    const project = await prisma.proje.findFirst({ where: { slug: projeSlug, silindi_mi: false } });
    if (!project) return NextResponse.json({ error: "Proje bulunamadı" }, { status: 404 });
    const block = await prisma.blok.findFirst({ where: { slug: blokSlug, proje_id: project.id, silindi_mi: false } });
    if (!block) return NextResponse.json({ error: "Blok bulunamadı" }, { status: 404 });
    const apartment = await prisma.daire.findFirst({ where: { slug: daireSlug, blok_id: block.id, silindi_mi: false } });
    if (!apartment) return NextResponse.json({ error: "Daire bulunamadı" }, { status: 404 });

    // Arızayı bul (slug ile)
    const existingFault = await prisma.ariza.findFirst({
      where: {
        slug: faultSlug,
        daire_id: apartment.id,
        silindi_mi: false
      }
    });

    if (!existingFault) {
      return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 });
    }

    // Arızayı sil (soft delete)
    await prisma.ariza.update({
      where: { id: existingFault.id },
      data: {
        silindi_mi: true,
        silinme_tarihi: new Date(),
        silen_id: "system" // Use system or session user
      }
    });

    return NextResponse.json({ message: "Arıza başarıyla silindi" });
  } catch (error) {
    console.error("Arıza silme hatası:", error);
    return NextResponse.json(
      { error: "Arıza silinirken bir hata oluştu" },
      { status: 500 }
    );
  }
} 