import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

function slugify(text: string) {
  return text.toString().toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '').replace(/-+/g, '-').replace(/^-+|-+$/g, '');
}

async function main() {
  console.log('🌱 Clean seeding database...');

  // 1. Tab<PERSON>lar<PERSON> temizle (bağımlıdan bağımsıza)
  await prisma.randevuTeknisyen.deleteMany();
  await prisma.randevu.deleteMany();
  await prisma.ariza.deleteMany();
  await prisma.daire.deleteMany();
  await prisma.blok.deleteMany();
  await prisma.proje.deleteMany();
  await prisma.user.deleteMany();

  // 2. Admin ve teknisyen kullanıcı ekle
  const admin = await prisma.user.create({
    data: {
      ad: 'Admin',
      soyad: 'User',
      email: `admin+${Date.now()}@example.com`,
      sifre: await bcrypt.hash('admin123', 12),
      rol: 'ADMIN',
      durum: 'ACTIVE',
      telefon: '+90 ************',
      emailVerified: new Date()
    }
  });
  const technician = await prisma.user.create({
    data: {
      ad: 'Teknisyen',
      soyad: 'Demo',
      email: `teknisyen+${Date.now()}@example.com`,
      sifre: await bcrypt.hash('teknisyen123', 12),
      rol: 'TECHNICIAN',
      durum: 'ACTIVE',
      telefon: '+90 ************',
      emailVerified: new Date()
    }
  });

  // 3. 2 Proje, her birine 2 blok, her bloğa 2 daire ekle
  const now = Date.now();
  const projects = await Promise.all([
    prisma.proje.create({
      data: {
        ad: 'Test Sitesi',
        slug: slugify('Test Sitesi') + '-' + now,
        aciklama: 'Demo test projesi',
        adres: 'Test Mah. Test Cad. No:1',
        baslangic_tarihi: new Date('2023-01-01'),
        olusturan_id: admin.id
      }
    }),
    prisma.proje.create({
      data: {
        ad: 'Demo Konut',
        slug: slugify('Demo Konut') + '-' + (now + 1),
        aciklama: 'Demo konut projesi',
        adres: 'Demo Mah. Demo Cad. No:2',
        baslangic_tarihi: new Date('2023-06-01'),
        olusturan_id: admin.id
      }
    })
  ]);

  const blocks = [];
  for (const [i, project] of projects.entries()) {
    blocks.push(await prisma.blok.create({
      data: {
        ad: 'A Blok',
        slug: slugify(project.ad + '-A-Blok') + '-' + now + '-' + i,
        aciklama: 'Demo blok',
        proje_id: project.id,
        olusturan_id: admin.id
      }
    }));
    blocks.push(await prisma.blok.create({
      data: {
        ad: 'B Blok',
        slug: slugify(project.ad + '-B-Blok') + '-' + now + '-' + i,
        aciklama: 'Demo blok',
        proje_id: project.id,
        olusturan_id: admin.id
      }
    }));
  }

  const apartments = [];
  for (const [i, block] of blocks.entries()) {
    for (let num = 1; num <= 2; num++) {
      apartments.push(await prisma.daire.create({
        data: {
          numara: `${num}`,
          slug: slugify(block.slug + '-' + num) + '-' + now + '-' + i,
          kat: 1,
          blok_id: block.id,
          olusturan_id: admin.id
        }
      }));
    }
  }

  // 4. 2 kullanıcı ekle
  const user1 = await prisma.user.create({
    data: {
      ad: 'Ali',
      soyad: 'Veli',
      email: `ali+${Date.now()}@example.com`,
      sifre: await bcrypt.hash('user123', 12),
      rol: 'USER',
      durum: 'ACTIVE',
      telefon: '+90 ************',
      daire_id: apartments[0].id,
      emailVerified: new Date()
    }
  });
  const user2 = await prisma.user.create({
    data: {
      ad: 'Ayşe',
      soyad: 'Fatma',
      email: `ayse+${Date.now()}@example.com`,
      sifre: await bcrypt.hash('user123', 12),
      rol: 'USER',
      durum: 'ACTIVE',
      telefon: '+90 ************',
      daire_id: apartments[1].id,
      emailVerified: new Date()
    }
  });

  // 5. Arıza tipleri, aciliyet ve durumlar varsa kullan, yoksa dummy ekle
  let faultType = await prisma.arizaTip.findFirst();
  if (!faultType) {
    faultType = await prisma.arizaTip.create({
      data: { ad: 'Su Tesisatı', renk: '#2196f3' }
    });
  }
  let priority = await prisma.aciliyetSeviye.findFirst();
  if (!priority) {
    priority = await prisma.aciliyetSeviye.create({
      data: { ad: 'Normal', seviye: 1, renk: '#ff9800' }
    });
  }
  let status = await prisma.arizaDurum.findFirst();
  if (!status) {
    status = await prisma.arizaDurum.create({
      data: { ad: 'Açık', renk: '#2196f3', sira: 1 }
    });
  }

  // 6. 2 arıza ekle
  const faults = [];
  for (const [i, apt] of apartments.entries()) {
    faults.push(await prisma.ariza.create({
      data: {
        slug: slugify('ariza-' + apt.slug + '-' + i) + '-' + now,
        baslik: 'Banyo musluğu damlatıyor',
        aciklama: 'Banyo musluğunda sürekli damlama var.',
        daire_id: apt.id,
        ariza_tip_id: faultType.id,
        aciliyet_id: priority.id,
        durum_id: status.id,
        bildiren_ad_soyad: user1.ad + ' ' + user1.soyad,
        bildiren_telefon: user1.telefon || '',
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date(),
        resimler: [],
        olusturan_id: admin.id
      }
    }));
  }

  // 7. 1 randevu ekle
  await prisma.randevu.create({
    data: {
      ariza_id: faults[0].id,
      randevu_tarihi: new Date(Date.now() + 86400000),
      durum: 'PLANLI',
      aciklama: 'Banyo musluğu için randevu',
      olusturan_id: admin.id
    }
  });
  // 8. 1 randevu teknisyen ataması
  const appointment = await prisma.randevu.findFirst({ where: { ariza_id: faults[0].id } });
  if (appointment) {
    await prisma.randevuTeknisyen.create({
      data: {
        randevu_id: appointment.id,
        teknisyen_id: technician.id
      }
    });
  }

  // 9. Özet çıktı
  console.log('✅ Clean seed completed!');
  console.log(`👤 Admin: ${admin.email}`);
  console.log(`🔧 Technician: ${technician.email}`);
  console.log(`🏢 Projects: ${projects.length}`);
  console.log(`🏢 Blocks: ${blocks.length}`);
  console.log(`🏠 Apartments: ${apartments.length}`);
  console.log(`👥 Users: 2`);
  console.log(`🔧 Faults: ${faults.length}`);
  console.log(`📅 Appointments: 1`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 