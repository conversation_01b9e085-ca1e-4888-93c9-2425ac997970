import { useState } from 'react'

interface MLPrediction {
  success: boolean
  selectedCategory?: {
    id: string
    ad: string
    confidence: number
    matchedWords: number
    totalWords: number
  }
  alternatives?: Array<{
    id: string
    ad: string
    confidence: number
  }>
  message?: string
  originalPrediction?: any
  serviceUnavailable?: boolean
}

interface UseMLPredictionProps {
  mlServiceUrl?: string
}

export function useMLPrediction({ mlServiceUrl = process.env.NEXT_PUBLIC_ML_SERVICE_URL || 'http://localhost:3050' }: UseMLPredictionProps = {}) {
  const [isPredicting, setIsPredicting] = useState(false)
  const [prediction, setPrediction] = useState<MLPrediction | null>(null)
  const [isLearning, setIsLearning] = useState(false)

  // ML tahmini yap
  const predictCategory = async (title: string, description: string, categories: any[]) => {
    if (!title || !description || categories.length === 0) {
      return null
    }

    setIsPredicting(true)
    setPrediction(null)

    try {
      // Timeout ile güvenli fetch
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 saniye timeout

      const response = await fetch(`${mlServiceUrl}/predict`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          categories: categories.map(cat => ({
            id: cat.id,
            ad: cat.ad,
            aciklama: cat.aciklama,
            renk: cat.renk,
            ikon: cat.ikon
          }))
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`ML servisi hatası: ${response.status}`)
      }

      const result = await response.json()
      setPrediction(result)
      return result
    } catch (error) {
      console.warn('ML servisi kullanılamıyor:', error)
      
      // Servis çalışmıyorsa sessizce devam et
      setPrediction({
        success: false,
        message: 'ML servisi şu anda kullanılamıyor',
        serviceUnavailable: true
      })
      return null
    } finally {
      setIsPredicting(false)
    }
  }

  // Online learning - yeni örnek öğret
  const teachModel = async (title: string, description: string, correctCategory: string) => {
    setIsLearning(true)

    try {
      // Timeout ile güvenli fetch
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 saniye timeout

      const response = await fetch(`${mlServiceUrl}/learn`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          correctCategory
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`ML servisi hatası: ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.warn('ML servisi öğrenme için kullanılamıyor:', error)
      // Servis çalışmıyorsa sessizce devam et, hata fırlatma
      return null
    } finally {
      setIsLearning(false)
    }
  }

  // Tahmin sonucunu temizle
  const clearPrediction = () => {
    setPrediction(null)
  }

  // ML servisinin çalışıp çalışmadığını kontrol et
  const checkServiceHealth = async () => {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000) // 3 saniye timeout

      const response = await fetch(`${mlServiceUrl}/health`, {
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      return response.ok
    } catch (error) {
      console.warn('ML servisi health check başarısız:', error)
      return false
    }
  }

  return {
    // State
    isPredicting,
    prediction,
    isLearning,
    
    // Actions
    predictCategory,
    teachModel,
    clearPrediction,
    checkServiceHealth,
    
    // Helpers
    hasLowConfidence: prediction && !prediction.success && !prediction.serviceUnavailable,
    hasHighConfidence: prediction && prediction.success && (prediction.selectedCategory?.confidence || 0) > 0.7,
    suggestedCategory: prediction?.selectedCategory,
    isServiceAvailable: !prediction?.serviceUnavailable
  }
} 