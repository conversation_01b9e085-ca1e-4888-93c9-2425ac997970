// Default placeholder image URL
export const DEFAULT_PROJECT_IMAGE = "/images/project-placeholder.svg";

// Helper function to get project image URL with fallback and cache busting
export function getProjectImageUrl(imageUrl?: string | null, bustCache: boolean = false): string {
  if (!imageUrl) {
    return DEFAULT_PROJECT_IMAGE;
  }
  
  // Cache busting için timestamp ekle
  if (bustCache) {
    const separator = imageUrl.includes('?') ? '&' : '?';
    return `${imageUrl}${separator}v=${Date.now()}`;
  }
  
  return imageUrl;
} 