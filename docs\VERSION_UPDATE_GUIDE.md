# 📦 Sür<PERSON><PERSON> Güncelleme Rehberi

Bu rehber, projenin sürüm numarasını ve footer'daki title değerini güncellemek için kullanılır.

## 🚀 Otomatik Sürüm Güncelleme

### Kullanım
```bash
npm run version:update <yeni-sürüm> <yeni-title>
```

### Örnek
```bash
npm run version:update 1.7.0 "Mobile Optimization"
```

## 📋 Script'in Yaptığı İşlemler

### 1. **package.json** Güncelleme
- Mevcut sürüm numarasını yeni sürüm ile değiştirir
- Örnek: `1.6.0` → `1.7.0`

### 2. **Footer Component** Güncelleme
- Desktop layout tag'ini günceller: `🔧 [Yeni Title]`
- Mobile layout tag'ini günceller: `🔧 [Yeni Title] v[Yeni <PERSON>ü<PERSON>]`

### 3. **README.md** Changelog Template
- Yeni sürüm için changelog template'i oluşturur
- <PERSON><PERSON>h otomatik eklenir
- Boş kategoriler hazırlanır (Yeni Ö<PERSON>ler, Düzeltmeler, İyileştirmeler)

## 🔄 Sürüm Güncelleme Süreci

### Adım 1: Script'i Çalıştır
```bash
npm run version:update 1.7.0 "Performance Boost"
```

### Adım 2: Changelog'u Tamamla
README.md'de oluşturulan template'i doldurun:
```markdown
### v1.7.0 (27.01.2025)
#### 🚀 Yeni Özellikler
- Performans optimizasyonları
- Yeni cache sistemi

#### 🐛 Düzeltmeler  
- Memory leak sorunu çözüldü
- UI responsive hataları düzeltildi

#### ✨ İyileştirmeler
- Daha hızlı sayfa yükleme
- Gelişmiş hata yönetimi
```

### Adım 3: Test Et
```bash
npm run dev
```
- Footer'da yeni sürüm ve title'ı kontrol edin
- Uygulamanın çalıştığını doğrulayın

### Adım 4: Commit Et
```bash
git add .
git commit -m "chore: bump version to v1.7.0 - Performance Boost"
```

## 🏷️ Sürüm Title Örnekleri

### Özellik Odaklı
- `"Smart Dashboard"` - Dashboard iyileştirmeleri
- `"Mobile First"` - Mobil optimizasyonları
- `"Real-time Updates"` - Gerçek zamanlı özellikler

### Teknik Odaklı
- `"Performance Boost"` - Performans iyileştirmeleri
- `"Enhanced Security"` - Güvenlik güncellemeleri
- `"Bug Fixes & Stability"` - Hata düzeltmeleri

### Kullanıcı Odaklı
- `"Better UX"` - Kullanıcı deneyimi iyileştirmeleri
- `"Simplified Workflow"` - İş akışı basitleştirmeleri
- `"Enhanced Navigation"` - Navigasyon iyileştirmeleri

## 📊 Sürüm Numaralama Kuralları

### Semantic Versioning (SemVer)
```
MAJOR.MINOR.PATCH
```

- **MAJOR** (1.x.x): Büyük değişiklikler, breaking changes
- **MINOR** (x.1.x): Yeni özellikler, backward compatible
- **PATCH** (x.x.1): Hata düzeltmeleri, küçük iyileştirmeler

### Örnekler
- `1.6.0` → `1.6.1`: Hata düzeltmesi
- `1.6.0` → `1.7.0`: Yeni özellik eklendi
- `1.6.0` → `2.0.0`: Büyük değişiklik (breaking change)

## 🚨 Dikkat Edilmesi Gerekenler

1. **Versiyon formatı**: Mutlaka `x.y.z` formatında olmalı
2. **Title uzunluğu**: Footer'da görünür olması için kısa tutun (max 20 karakter)
3. **Özel karakterler**: Title'da özel karakterler kullanmaktan kaçının
4. **Test etme**: Her sürüm güncellemesinden sonra mutlaka test edin

## 🔧 Manuel Güncelleme

Script kullanmak istemiyorsanız, manuel olarak şu dosyaları güncelleyin:

1. `package.json` → `version` field
2. `src/components/ui/app-footer.tsx` → Feature tag'leri
3. `README.md` → Changelog entry'si

## 📞 Yardım

Script hata veriyorsa:
1. Node.js versiyonunuzu kontrol edin (18+ gerekli)
2. Dosya yollarının doğru olduğunu kontrol edin
3. Yazım hatalarını kontrol edin

```bash
# Script'i doğrudan çalıştırma
node scripts/update-version.js 1.7.0 "Test Title"
``` 