"use client"

import { ReactNode, useState } from "react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { Sidebar } from "./sidebar"
import { TopNav } from "./top-nav"
import { AppFooter } from "@/components/ui/app-footer"
import { LoadingProvider } from "@/contexts/loading-context"
import { LoadingOverlay } from "@/components/ui/loading-overlay"

interface DashboardLayoutProps {
  children: ReactNode
  user?: {
    name?: string | null
    email?: string | null
    image?: string | null
    role?: string
  }
}

export function DashboardLayout({ children, user }: DashboardLayoutProps) {
  const [queryClient] = useState(() => new QueryClient())
  
  return (
    <LoadingProvider>
      <QueryClientProvider client={queryClient}>
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar */}
          <Sidebar user={user} />
          
          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Top Navigation */}
            <TopNav user={user} />
            
            {/* Main Content Area */}
            <main className="flex-1 flex flex-col overflow-hidden bg-gray-50">
              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto">
                {/* Page Content */}
                <div className="p-4">
                  {children}
                </div>
              </div>
              
              {/* Footer */}
              <AppFooter />
            </main>
          </div>
        </div>
        
        {/* Loading Overlay */}
        <LoadingOverlay />
      </QueryClientProvider>
    </LoadingProvider>
  )
}
