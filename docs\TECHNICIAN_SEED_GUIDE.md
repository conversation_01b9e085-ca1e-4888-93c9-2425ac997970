# 👨‍🔧 Teknisyen ve Uzmanlık Alanları Seed Rehberi

Bu dosya, teknisyen ve uzmanlık alanları için test verilerini oluşturmak amacıyla hazırlanmıştır.

## 📋 İçerik

### 🎯 Uzmanlık Alanları (12 adet)
- **Elektrik** - Elektrik tesisatı, a<PERSON><PERSON><PERSON><PERSON>ma, priz ve anahtar sistemleri
- **Tesisatçılık** - Su, kanal<PERSON>syon, doğalgaz tesisatı
- **Boyacılık** - İç ve dış cephe boyama, badana işleri
- **Marangozluk** - Ahşap işleri, kap<PERSON>, pencere, dolap tamiri
- **Cam Ustası** - <PERSON>, pencere tamiri
- **Klima & Soğutma** - <PERSON><PERSON><PERSON> montaj, bakım ve onarım
- **İzolasyon** - Su, ses ve ısı izolasyonu
- **Seramik & Fayans** - <PERSON><PERSON><PERSON>, fayans dö<PERSON><PERSON><PERSON> ve onarım
- **Metal İşleri** - <PERSON><PERSON>, kor<PERSON><PERSON>, ka<PERSON><PERSON> tamiri
- **Bahçıvanlık** - <PERSON><PERSON>zaj, bitki bakımı, sulama sistemleri
- **Temizlik** - Genel temizlik, cam temizliği, halı yıkama
- **Güvenlik Sistemleri** - Kamera, alarm, interkom sistemleri

### 👨‍🔧 Teknisyenler (15 adet)
Her teknisyen 1-3 uzmanlık alanına sahiptir ve rastgele seviye atanır:
- **BASLANGIC** - Yeni başlayan
- **ORTA** - Deneyimli
- **ILERI** - Uzman
- **UZMAN** - Master seviye

## 🚀 Kullanım

### Seed Dosyasını Çalıştırma
```bash
npm run db:seed-technicians
```

### Manuel Çalıştırma
```bash
npx tsx scripts/seed-technicians-expertise.ts
```

## 📊 Oluşturulan Veriler

Seed işlemi tamamlandığında:
- ✅ 12 uzmanlık alanı
- ✅ 15 teknisyen
- ✅ ~30 uzmanlık ataması
- ✅ Renk kodları ile görsel ayrım
- ✅ Rastgele seviye atamaları

## 🔧 Özellikler

### Güvenli Çalıştırma
- **Upsert kullanımı**: Mevcut veriler güncellenir, yeniler eklenir
- **Email kontrolü**: Aynı teknisyen birden fazla oluşturulmaz
- **Hata yönetimi**: Her adımda hata kontrolü
- **Bağımlılık kontrolü**: Önce uzmanlık alanları, sonra teknisyenler

### Gerçekçi Veriler
- **Türkçe isimler**: Gerçek Türk isimleri
- **Telefon numaraları**: Türkiye formatında
- **Email adresleri**: Teknisyen özel domain
- **Renk kodları**: Her uzmanlık için farklı renk

## 📱 Test Senaryoları

Bu seed verilerini kullanarak test edebileceğiniz özellikler:

### Teknisyen Yönetimi
- ✅ Teknisyen listesi görüntüleme
- ✅ Yeni teknisyen ekleme
- ✅ Teknisyen düzenleme
- ✅ Teknisyen silme
- ✅ Uzmanlık atama/kaldırma

### Uzmanlık Alanları
- ✅ Uzmanlık listesi görüntüleme
- ✅ Renk yönetimi
- ✅ Yeni uzmanlık ekleme
- ✅ Uzmanlık düzenleme

### Arıza Ataması
- ✅ Teknisyen seçimi
- ✅ Uzmanlık bazlı filtreleme
- ✅ Seviye kontrolü

## 🔄 Tekrar Çalıştırma

Seed dosyası güvenle birden fazla kez çalıştırılabilir:
- Mevcut uzmanlık alanları güncellenir
- Aynı email'e sahip teknisyen oluşturulmaz
- Mevcut atamalar korunur

## 🛠️ Özelleştirme

Seed verilerini özelleştirmek için `scripts/seed-technicians-expertise.ts` dosyasını düzenleyin:

```typescript
// Yeni uzmanlık alanı eklemek
const expertiseAreas = [
  // ... mevcut alanlar
  {
    ad: "Yeni Uzmanlık",
    aciklama: "Açıklama",
    renk: "#FF0000"
  }
]

// Yeni teknisyen eklemek
const technicians = [
  // ... mevcut teknisyenler
  {
    ad: "Yeni",
    soyad: "Teknisyen",
    telefon: "0555 123 4567",
    uzmanliklar: ["Elektrik", "Tesisatçılık"]
  }
]
```

## ⚠️ Dikkat Edilecekler

1. **Database bağlantısı**: Prisma bağlantısının aktif olduğundan emin olun
2. **Backup**: Önemli verileri yedekleyin
3. **Test ortamı**: Önce test ortamında çalıştırın
4. **Bağımlılıklar**: Seed işlemi sırasında diğer işlemleri durdurun

## 📞 Destek

Herhangi bir sorun yaşarsanız:
1. Console loglarını kontrol edin
2. Database bağlantısını doğrulayın
3. Prisma schema'nın güncel olduğundan emin olun 