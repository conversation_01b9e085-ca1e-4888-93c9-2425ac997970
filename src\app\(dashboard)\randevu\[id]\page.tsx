"use client"

import { useState, useEffect } from "react"
import { notFound } from "next/navigation"
import { ArrowLeft, Calendar, Clock, User, MapPin, Edit, Trash2, Plus, Wrench, Package, Save, ChevronRight, CheckCircle } from "lucide-react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"
import dynamic from "next/dynamic"
import { format } from "date-fns"
import { tr } from "date-fns/locale"

// Dynamic import for markdown editor to avoid SSR issues
const MDEditor = dynamic(() => import("@uiw/react-md-editor"), { ssr: false })

// Import markdown editor styles
import "@uiw/react-md-editor/markdown-editor.css"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { AddTechnicianToAppointmentDialog } from "@/components/appointments/add-technician-dialog"
import { AppointmentWorkRecords } from "@/components/appointments/appointment-work-records"
import { AppointmentMaterialUsage } from "@/components/appointments/appointment-material-usage"
import { CloseAppointmentModal } from "@/components/appointments/close-appointment-modal"

import { AppointmentStatus, APPOINTMENT_STATUS_LABELS, APPOINTMENT_STATUS_COLORS, ExpertiseLevel, EXPERTISE_LEVEL_LABELS } from "@/lib/enums"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface AppointmentDetailPageProps {
  params: Promise<{ id: string }>
}

interface ExpertiseArea {
  id: string
  ad: string
  aciklama: string | null
  renk: string
  seviye: keyof typeof ExpertiseLevel
}

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
  uzmanlikAlanlari: ExpertiseArea[]
}

interface AppointmentTechnician {
  teknisyen: Technician
}

interface AppointmentData {
  id: string
  randevu_tarihi: Date
  durum: AppointmentStatus
  aciklama: string | null
  teknisyenler: AppointmentTechnician[]
  ariza: {
    id: string
    numara: string
    baslik: string
    slug: string
    daire: {
      numara: string
      slug: string
      blok: {
        ad: string
        slug: string
        proje: { 
          ad: string
          slug: string
        }
      }
    }
  }
  olusturulma_tarihi: Date
  guncelleme_tarihi: Date
}

const formatDate = (dateInput: string | Date) => {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput
  return date.toLocaleDateString("tr-TR", {
    day: "2-digit",
    month: "2-digit", 
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })
}

// Inline AppointmentWorkArea Component
interface Material {
  id: string
  ad: string
  birim: string
  stok_durumu: string
}

interface UsedMaterial {
  id: string
  malzeme_id: string
  miktar: number
  malzeme: Material
  teknisyen: {
    ad: string
    soyad: string
  }
}

interface AppointmentWorkAreaProps {
  appointmentId: string
  appointmentStatus: AppointmentStatus
  faultId: string
  appointmentTechnicians: Array<{
    teknisyen: {
      id: string
      ad: string
      soyad: string
    }
  }>
}

function AppointmentWorkArea({ appointmentId, appointmentStatus, faultId, appointmentTechnicians }: AppointmentWorkAreaProps) {
  // States
  const [workDescription, setWorkDescription] = useState("")
  const [materials, setMaterials] = useState<Material[]>([])
  const [usedMaterials, setUsedMaterials] = useState<UsedMaterial[]>([])
  const [newMaterial, setNewMaterial] = useState({ malzeme_id: "", miktar: 1, teknisyen_id: "" })
  const [resultAction, setResultAction] = useState<string>("")
  const [saving, setSaving] = useState(false)
  const [validationError, setValidationError] = useState<string>("")
  const [showCloseModal, setShowCloseModal] = useState(false)
  const [pendingResultData, setPendingResultData] = useState<any>(null)
  const router = useRouter()
  
  // Randevu tamamlandı mı kontrolü
  const isAppointmentCompleted = appointmentStatus === "TAMAMLANDI" || appointmentStatus === "IPTAL"

  // Load initial data
  useEffect(() => {
    loadMaterials()
    loadUsedMaterials()
    loadWorkArea()
  }, [appointmentId])

  const loadWorkArea = async () => {
    try {
      const resultResponse = await fetch(`/api/appointments/${appointmentId}/sonuc`)
      if (resultResponse.ok) {
        const result = await resultResponse.json()
        setWorkDescription(result.teknisyen_notlari || "")
        setResultAction(result.action || "")
      }
    } catch (error) {
      console.error("Error loading work area:", error)
    }
  }

  const loadMaterials = async () => {
    try {
      const response = await fetch("/api/materials")
      if (response.ok) {
        const data = await response.json()
        setMaterials(data)
      }
    } catch (error) {
      console.error("Error loading materials:", error)
    }
  }

  const loadUsedMaterials = async () => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler`)
      if (response.ok) {
        const data = await response.json()
        setUsedMaterials(data)
      }
    } catch (error) {
      console.error("Error loading used materials:", error)
    }
  }

  const addMaterial = async () => {
    if (!newMaterial.malzeme_id || !newMaterial.teknisyen_id || newMaterial.miktar <= 0) {
      toast.error("Lütfen tüm alanları doldurun")
      return
    }

    try {
      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newMaterial)
      })

      if (response.ok) {
        toast.success("Malzeme eklendi")
        setNewMaterial({ malzeme_id: "", miktar: 1, teknisyen_id: "" })
        loadUsedMaterials()
      } else {
        toast.error("Malzeme eklenirken hata oluştu")
      }
    } catch (error) {
      console.error("Error adding material:", error)
      toast.error("Malzeme eklenirken hata oluştu")
    }
  }

  const removeMaterial = async (materialId: string) => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/malzemeler/${materialId}`, {
        method: "DELETE"
      })

      if (response.ok) {
        toast.success("Malzeme kaldırıldı")
        loadUsedMaterials()
      } else {
        toast.error("Malzeme kaldırılırken hata oluştu")
      }
    } catch (error) {
      console.error("Error removing material:", error)
      toast.error("Malzeme kaldırılırken hata oluştu")
    }
  }

  const saveWorkArea = async () => {
    // Validasyon
    if (!resultAction) {
      setValidationError("Lütfen randevu durumunu seçin")
      return
    }

    if (!workDescription.trim()) {
      setValidationError("Lütfen teknisyen notlarını girin")
      return
    }

    setSaving(true)
    setValidationError("")

    try {
      const response = await fetch(`/api/appointments/${appointmentId}/sonuc`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: resultAction,
          teknisyen_notlari: workDescription,
        })
      })

      if (response.ok) {
        const result = await response.json()
        
        // Eğer arıza çözüldü ve başka aktif randevu yoksa, kullanıcıya sor
        if (result.action === "FAULT_RESOLVED" && result.activeAppointments === 0) {
          setPendingResultData(result)
          setShowCloseModal(true)
          return
        }
        
        // Normal iş akışı
        handleResultSuccess(result)
      } else {
        const errorData = await response.json()
        toast.error("❌ Kayıt başarısız!", {
          description: errorData.error || "Beklenmeyen bir hata oluştu",
          duration: 5000
        })
      }
    } catch (error) {
      console.error("Error saving work area:", error)
      toast.error("❌ Kayıt başarısız!", {
        description: "Beklenmeyen bir hata oluştu",
        duration: 5000
      })
    } finally {
      setSaving(false)
    }
  }

  const handleResultSuccess = (result: any) => {
    toast.success("🎉 Randevu sonucu başarıyla kaydedildi!", {
      description: `Aksiyon: ${result.actionLabel || "Güncellendi"}`,
      duration: 4000
    })
    
    // Yeni randevu gerekli mi bildirimi?
    if (result.shouldCreateNewAppointment) {
      toast.info("ℹ️ Sonraki randevu gerekli", {
        description: "Arıza detay sayfasından yeni randevu oluşturabilirsiniz",
        duration: 5000
      })
    }
    
    // Yönlendirme
    if (result.shouldRedirectToApartment && result.apartmentId) {
      toast.success("🏠 Arıza tamamen çözüldü!", {
        description: "Daire detay sayfasına yönlendiriliyorsunuz",
        duration: 4000
      })
      
      // Slug-based URL'e yönlendir
      setTimeout(() => {
        router.push(`/projeler/${result.projeSlug}/bloklar/${result.blokSlug}/${result.daireSlug}`)
      }, 2000)
    } else if (result.shouldRedirectToFault && result.faultId) {
      // Arıza detay sayfasına yönlendir
      setTimeout(() => {
        router.push(`/projeler/${result.projeSlug}/bloklar/${result.blokSlug}/${result.daireSlug}/arizalar/${result.faultId}`)
      }, 2000)
    }
    
    loadWorkArea()
    setValidationError("")
  }

  const handleCloseModalConfirm = async (closeFaultToo: boolean) => {
    if (!pendingResultData) return

    try {
      setSaving(true)
      
      // Arıza kapatma seçeneği ile tekrar gönder
      const response = await fetch(`/api/appointments/${appointmentId}/sonuc`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: pendingResultData.action,
          teknisyen_notlari: workDescription,
          closeFaultToo
        })
      })

      if (response.ok) {
        const result = await response.json()
        handleResultSuccess(result)
      } else {
        const errorData = await response.json()
        toast.error("❌ Kayıt başarısız!", {
          description: errorData.error || "Beklenmeyen bir hata oluştu",
          duration: 5000
        })
      }
    } catch (error) {
      console.error("Error confirming result:", error)
      toast.error("❌ Kayıt başarısız!", {
        description: "Beklenmeyen bir hata oluştu",
        duration: 5000
      })
    } finally {
      setSaving(false)
      setShowCloseModal(false)
      setPendingResultData(null)
    }
  }

  const handleCloseModalCancel = () => {
    setShowCloseModal(false)
    setPendingResultData(null)
  }

  return (
    <div className="space-y-6">
      {/* Tamamlanan Randevu Bilgilendirmesi */}
      {isAppointmentCompleted && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
              <CheckCircle className="h-5 w-5 text-white" />
            </div>
            <div>
              <h4 className="font-medium text-blue-900">
                🎉 Bu randevu {appointmentStatus === "TAMAMLANDI" ? "tamamlandı" : "iptal edildi"}
              </h4>
              <p className="text-sm text-blue-700 mt-1">
                {appointmentStatus === "TAMAMLANDI" 
                  ? "Randevu sonucu kaydedildi ve iş akışı tamamlandı. Artık değişiklik yapılamaz."
                  : "Bu randevu iptal edildi. Artık değişiklik yapılamaz."
                }
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Work Description */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Wrench className="h-5 w-5" />
          <h3 className="text-lg font-medium">📝 Yapılan İşlemler</h3>
        </div>
        
        {/* Markdown Editor */}
        <div data-color-mode="light" className={validationError && !workDescription.trim() && !isAppointmentCompleted ? "ring-2 ring-red-500 rounded-lg" : ""}>
          <MDEditor
            value={workDescription}
            onChange={isAppointmentCompleted ? undefined : (value) => {
              setWorkDescription(value || "")
              if (validationError && (value || "").trim()) {
                setValidationError("") // Kullanıcı yazarken hata mesajını temizle
              }
            }}
            height={200}
            preview={isAppointmentCompleted ? "preview" : "edit"}
            data-bs-no-jquery={isAppointmentCompleted}
          />
        </div>
        
        {/* Kullanım rehberi */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <h4 className="text-sm font-medium text-blue-900 mb-2">📋 Maddeleme Rehberi:</h4>
          <div className="text-xs text-blue-700 space-y-1">
            <div>• <code>- Maddeli liste</code> için tire (-) kullanın</div>
            <div>• <code>1. Numaralı liste</code> için rakam kullanın</div>
            <div>• <code>**Kalın yazı**</code> için çift yıldız kullanın</div>
            <div>• <code>*İtalik yazı*</code> için tek yıldız kullanın</div>
          </div>
        </div>
        
        <p className="text-xs text-muted-foreground">
          💡 İpucu: Detaylı açıklama gelecekteki randevularda yardımcı olacaktır
        </p>
      </div>

      {/* Materials */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          <h3 className="text-lg font-medium">🧰 Kullanılan Malzemeler</h3>
        </div>
        
        {/* Add Material */}
        {!isAppointmentCompleted && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div>
              <Label className="text-sm font-medium text-gray-700">Malzeme</Label>
              <Select value={newMaterial.malzeme_id} onValueChange={(value) => 
                setNewMaterial(prev => ({ ...prev, malzeme_id: value }))
              }>
                <SelectTrigger className="h-10 bg-white border-gray-300">
                  <SelectValue placeholder="Malzeme seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {materials.map((material) => (
                    <SelectItem key={material.id} value={material.id}>
                      {material.ad} ({material.birim})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">Miktar</Label>
              <Input
                type="text"
                placeholder="1.5"
                value={newMaterial.miktar}
                onChange={(e) => {
                  const value = e.target.value
                  // Sadece sayıları ve noktayı kabul et
                  if (value === '' || /^\d*\.?\d*$/.test(value)) {
                    setNewMaterial(prev => ({ 
                      ...prev, 
                      miktar: value === '' ? 0 : parseFloat(value) || 0
                    }))
                  }
                }}
                className="h-10 bg-white border-gray-300"
              />
            </div>

            <div className="flex items-end">
              <Button 
                onClick={addMaterial} 
                size="sm" 
                className="h-10 w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-1" />
                Malzeme Ekle
              </Button>
            </div>
          </div>
        )}

        {/* Used Materials List */}
        {usedMaterials.length > 0 && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">📦 Kullanılan Malzemeler:</Label>
            <div className="space-y-2">
              {usedMaterials.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-white border rounded-lg shadow-sm">
                  <div className="flex items-center gap-3">
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      {item.miktar} {item.malzeme.birim}
                    </Badge>
                    <span className="font-medium">{item.malzeme.ad}</span>
                  </div>
                  {!isAppointmentCompleted && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeMaterial(item.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Result Selection */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          <h3 className="text-lg font-medium">🎯 Randevu Sonucu</h3>
        </div>
        
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <Label className="text-sm font-medium text-gray-700 mb-2 block">
            Randevu durumunu seçin:
          </Label>
          <Select value={resultAction} onValueChange={(value) => {
            setResultAction(value)
            if (validationError && value) {
              setValidationError("") // Kullanıcı seçim yaparken hata mesajını temizle
            }
          }}>
            <SelectTrigger className={`h-12 bg-white ${validationError && !resultAction ? "border-red-500 ring-2 ring-red-500" : "border-gray-300"}`}>
              <SelectValue placeholder="Durum seçin..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FAULT_RESOLVED">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  <span>✅ Arıza Çözüldü (Kapatılsın)</span>
                </div>
              </SelectItem>
              <SelectItem value="CANCELLED">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <span>❌ İptal Edildi</span>
                </div>
              </SelectItem>
              <SelectItem value="NEXT_APPOINTMENT_REQUIRED">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                  <span>🔄 Sonraki Randevu Gerekli</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          
          {resultAction && (
            <div className="mt-3 p-3 bg-white border rounded-lg">
              <p className="text-sm text-gray-600">
                {resultAction === "FAULT_RESOLVED" && 
                  "✅ Arıza kapalı olarak işaretlenecek ve tüm ilgili randevular tamamlanacak."}
                {resultAction === "CANCELLED" && 
                  "❌ Arıza iptal edilecek ve tüm ilgili randevular iptal edilecek."}
                {resultAction === "NEXT_APPOINTMENT_REQUIRED" && 
                  "🔄 Bu randevu tamamlanacak ancak arıza aktif kalacak. Yeni randevu formu açılacak."}
              </p>
            </div>
          )}
        </div>
        
        <div className="space-y-2">
          <Button 
            onClick={saveWorkArea}
            disabled={saving || !resultAction || !workDescription.trim()}
            className="w-full h-12 bg-green-600 hover:bg-green-700 text-white font-medium"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sonuç Kaydediliyor...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Randevu Sonucunu Kaydet
              </>
            )}
          </Button>
          
          {validationError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 flex items-center gap-2">
              <div className="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold">!</span>
              </div>
              <p className="text-red-700 text-sm font-medium">{validationError}</p>
            </div>
          )}
        </div>
      </div>

      {/* Close Appointment Modal */}
      <CloseAppointmentModal
        open={showCloseModal}
        onOpenChange={setShowCloseModal}
        appointmentId={appointmentId}
        appointmentDate={new Date().toLocaleDateString("tr-TR")}
        faultNumber=""
        faultTitle=""
        onConfirm={handleCloseModalConfirm}
        loading={saving}
      />

    </div>
  )
}

export default function AppointmentDetailPage({ params }: AppointmentDetailPageProps) {
  const [appointment, setAppointment] = useState<AppointmentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [appointmentId, setAppointmentId] = useState<string | null>(null)
  const [isAddTechnicianDialogOpen, setIsAddTechnicianDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showCloseModal, setShowCloseModal] = useState(false)
  const [closingAppointment, setClosingAppointment] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParams = await params
      setAppointmentId(resolvedParams.id)
    }
    loadParams()
  }, [params])

  const loadAppointmentData = async () => {
    if (!appointmentId) return
    
    try {
      setLoading(true)
      const response = await fetch(`/api/appointments/${appointmentId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch appointment data')
      }
      const data = await response.json()
      setAppointment(data)
    } catch (error) {
      console.error('Error loading appointment data:', error)
      // Handle error - could show error message or redirect
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    if (appointmentId) {
      loadAppointmentData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appointmentId])

  const handleRemoveTechnician = async (technicianId: string) => {
    if (!appointment) return
    
    try {
      // Filter out the technician to remove
      const remainingTechnicianIds = appointment.teknisyenler
        .filter(at => at.teknisyen.id !== technicianId)
        .map(at => at.teknisyen.id)

      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          teknisyen_ids: remainingTechnicianIds,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to remove technician")
      }

      // Reload appointment data to get updated technician list
      loadAppointmentData()
    } catch (error) {
      console.error("Error removing technician:", error)
    }
  }

  const handleDeleteAppointment = async () => {
    if (!appointment) return
    
    try {
      setIsDeleting(true)
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete appointment")
      }

      toast.success("Randevu başarıyla silindi")
      setIsDeleteDialogOpen(false)
      
      // Redirect to appointments list and force refresh after successful deletion
      setTimeout(() => {
        window.location.href = "/randevu?refresh=true"
      }, 1000)
    } catch (error) {
      console.error("Error deleting appointment:", error)
      toast.error("Randevu silinirken hata oluştu")
      setIsDeleting(false)
    }
  }

  const handleCloseAppointment = async (closeFaultToo: boolean) => {
    if (!appointment) return
    
    try {
      setClosingAppointment(true)
      const response = await fetch(`/api/appointments/${appointment.id}/close`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ closeFaultToo }),
      })

      if (!response.ok) {
        throw new Error("Failed to close appointment")
      }

      const result = await response.json()
      
      toast.success(result.message, {
        description: result.details,
        duration: 5000
      })

      // Reload appointment data to reflect changes
      loadAppointmentData()
      
    } catch (error) {
      console.error("Error closing appointment:", error)
      toast.error("Randevu kapatılırken hata oluştu")
    } finally {
      setClosingAppointment(false)
    }
  }

  const handleStatusChange = async (newStatus: AppointmentStatus) => {
    if (!appointment) return
    
    // Eğer randevu TAMAMLANDI yapılıyorsa ve henüz tamamlanmamışsa
    if (newStatus === "TAMAMLANDI" && appointment.durum !== "TAMAMLANDI") {
      // Önce arıza altındaki aktif randevuları kontrol et
      try {
        const response = await fetch(`/api/appointments/${appointment.id}/close`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ checkOnly: true }),
        })

        if (response.ok) {
          const result = await response.json()
          
          // Eğer başka aktif randevu yoksa modal göster
          if (result.activeAppointments === 0) {
            setShowCloseModal(true)
            return
          }
        }
      } catch (error) {
        console.error("Error checking active appointments:", error)
      }
    }

    // Normal durum güncellemesi
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ durum: newStatus }),
      })

      if (!response.ok) {
        throw new Error("Failed to update appointment status")
      }

      toast.success("Randevu durumu güncellendi")
      loadAppointmentData()
    } catch (error) {
      console.error("Error updating appointment status:", error)
      toast.error("Randevu durumu güncellenirken hata oluştu")
    }
  }



  if (loading) {
    return <div className="flex items-center justify-center min-h-96">
      <div className="text-lg">Yükleniyor...</div>
    </div>
  }

  if (!appointment) {
    notFound()
  }

  return (
    <div className="space-y-6">
      {/* Custom Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Link href="/dashboard" className="hover:text-foreground transition-colors">
          Ana Sayfa
        </Link>
        <ChevronRight className="h-4 w-4" />
        <span>{appointment.ariza.daire.blok.proje.ad}</span>
        <ChevronRight className="h-4 w-4" />
        <span>{appointment.ariza.daire.blok.ad}</span>
        <ChevronRight className="h-4 w-4" />
        <span>Daire {appointment.ariza.daire.numara}</span>
        <ChevronRight className="h-4 w-4" />
        <Link href={`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}/${appointment.ariza.daire.slug}/arizalar/${appointment.ariza.id}`} className="hover:text-foreground transition-colors">
          Arıza #{appointment.ariza.baslik}
        </Link>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground">Randevu</span>
      </div>
      
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="/randevu">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Randevulara Dön
          </Link>
        </Button>
        <div className="flex-1">
          <h2 className="text-3xl font-bold tracking-tight">
            Randevu Detayı
          </h2>
          <div className="flex items-center gap-4 mt-1">
            <p className="text-muted-foreground">
              {appointment.ariza.baslik}
            </p>
            <Badge variant="outline" className="text-xs">
              Arıza #{appointment.ariza.baslik}
            </Badge>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <MapPin className="h-3 w-3" />
              {appointment.ariza.daire.blok.proje.ad} - {appointment.ariza.daire.blok.ad} - Daire {appointment.ariza.daire.numara}
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}/${appointment.ariza.daire.slug}/arizalar/${appointment.ariza.id}`}>
              Arızayı Görüntüle
            </Link>
          </Button>
          <Button asChild>
            <Link href={`/randevu/${appointment.id}/duzenle`}>
              <Edit className="mr-2 h-4 w-4" />
              Düzenle
            </Link>
          </Button>
          <Button 
            variant="destructive" 
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Sil
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Ana Bilgiler */}
        <div className="md:col-span-2 space-y-6">
          {/* Randevu Detayları */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Randevu Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Tarih & Saat</h4>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDate(appointment.randevu_tarihi)}</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Durum</h4>
                  <div className="flex items-center gap-2">
                    <Badge 
                      style={{ 
                        backgroundColor: APPOINTMENT_STATUS_COLORS[appointment.durum] + "20",
                        borderColor: APPOINTMENT_STATUS_COLORS[appointment.durum],
                        color: APPOINTMENT_STATUS_COLORS[appointment.durum]
                      }}
                    >
                      {APPOINTMENT_STATUS_LABELS[appointment.durum]}
                    </Badge>
                    {appointment.durum !== "TAMAMLANDI" && appointment.durum !== "IPTAL" && (
                      <Select
                        value={appointment.durum}
                        onValueChange={(value) => handleStatusChange(value as AppointmentStatus)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="PLANLI">Planlı</SelectItem>
                          <SelectItem value="DEVAM_EDIYOR">Devam Ediyor</SelectItem>
                          <SelectItem value="TAMAMLANDI">Tamamlandı</SelectItem>
                          <SelectItem value="IPTAL">İptal</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </div>
              </div>
              {appointment.aciklama && (
                <div>
                  <h4 className="font-semibold mb-2">Açıklama</h4>
                  <p className="whitespace-pre-wrap">{appointment.aciklama}</p>
                </div>
              )}
            </CardContent>
          </Card>          {/* Atanan Teknisyenler */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Atanan Teknisyenler
                </CardTitle>
                <Button 
                  onClick={() => setIsAddTechnicianDialogOpen(true)}
                  size="sm"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Teknisyen Ekle
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {appointment.teknisyenler.length === 0 ? (
                <div className="text-center py-8">
                  <User className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p className="text-muted-foreground mb-4">Henüz teknisyen atanmamış</p>
                  <Button 
                    onClick={() => setIsAddTechnicianDialogOpen(true)}
                    variant="outline"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    İlk Teknisyeni Ekle
                  </Button>
                </div>              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Teknisyen</TableHead>
                      <TableHead>Telefon</TableHead>
                      <TableHead>Uzmanlık Alanları</TableHead>
                      <TableHead>İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {appointment.teknisyenler.map((at) => (
                      <TableRow key={at.teknisyen.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={at.teknisyen.resim || ""} />
                              <AvatarFallback>
                                {at.teknisyen.ad.charAt(0)}{at.teknisyen.soyad.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">
                                {at.teknisyen.ad} {at.teknisyen.soyad}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{at.teknisyen.telefon || "Belirtilmemiş"}</TableCell>
                        <TableCell>
                          {at.teknisyen.uzmanlikAlanlari && at.teknisyen.uzmanlikAlanlari.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              <TooltipProvider>
                                {at.teknisyen.uzmanlikAlanlari.map((expertise) => (
                                  <Tooltip key={expertise.id}>
                                    <TooltipTrigger>
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                        style={{
                                          borderColor: expertise.renk,
                                          color: expertise.renk,
                                          backgroundColor: `${expertise.renk}10`,
                                        }}
                                      >
                                        {expertise.ad}
                                        <span className="ml-1 text-[10px] opacity-70">
                                          ({EXPERTISE_LEVEL_LABELS[expertise.seviye as keyof typeof EXPERTISE_LEVEL_LABELS]})
                                        </span>
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <div className="text-center">
                                        <p className="font-medium">{expertise.ad}</p>
                                        <p className="text-xs text-muted-foreground">
                                          Seviye: {EXPERTISE_LEVEL_LABELS[expertise.seviye as keyof typeof EXPERTISE_LEVEL_LABELS]}
                                        </p>
                                        {expertise.aciklama && (
                                          <p className="text-xs mt-1">{expertise.aciklama}</p>
                                        )}
                                      </div>
                                    </TooltipContent>
                                  </Tooltip>
                                ))}
                              </TooltipProvider>
                            </div>
                          ) : (
                            <span className="text-xs text-muted-foreground">Belirtilmemiş</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleRemoveTechnician(at.teknisyen.id)}
                            disabled={appointment.teknisyenler.length === 1}
                          >
                            Kaldır
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Yan Panel */}
        <div className="space-y-6">
          {/* İlgili Arıza */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                İlgili Arıza
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Arıza Numarası</p>
                <Link 
                  href={`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}/${appointment.ariza.daire.slug}/arizalar/${appointment.ariza.id}`}
                  className="font-medium text-primary hover:underline"
                >
                  #{appointment.ariza.baslik}
                </Link>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Başlık</p>
                <p className="font-medium">{appointment.ariza.baslik}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Konum</p>
                <p className="text-sm">
                  {appointment.ariza.daire.blok.proje.ad} - {appointment.ariza.daire.blok.ad} - Daire {appointment.ariza.daire.numara}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Zaman Bilgileri */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Zaman Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Oluşturulma</p>
                <p className="font-medium">{formatDate(appointment.olusturulma_tarihi)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Son Güncelleme</p>
                <p className="font-medium">{formatDate(appointment.guncelleme_tarihi)}</p>
              </div>            </CardContent>          </Card>
        </div>
      </div>

      {/* İşlem Kayıtları ve Malzeme Kullanımı */}
      {appointment && (
        <div className="mt-8 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                🔧 İşlem Takibi ve Malzeme Kullanımı
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-2">
                Yapılan işlemleri kaydedin ve kullanılan malzemeleri takip edin
              </p>
            </CardHeader>
            <CardContent>
              <AppointmentWorkArea 
                appointmentId={appointment.id}
                appointmentStatus={appointment.durum}
                faultId={appointment.ariza.id}
                appointmentTechnicians={appointment.teknisyenler}
              />
            </CardContent>
          </Card>

        </div>
      )}

      {/* Add Technician Modal */}
      {appointment && (
        <AddTechnicianToAppointmentDialog
          appointmentId={appointment.id}
          currentTechnicians={appointment.teknisyenler.map(at => at.teknisyen)}
          open={isAddTechnicianDialogOpen}
          onOpenChange={setIsAddTechnicianDialogOpen}
          onSuccess={() => {
            setIsAddTechnicianDialogOpen(false)
            loadAppointmentData()
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Randevuyu Sil</AlertDialogTitle>
            <AlertDialogDescription>
              Bu randevuyu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
              <br /><br />
              <strong>Silinecek randevu:</strong>
              <br />
              • Arıza #{appointment?.ariza.baslik} - {appointment?.ariza.baslik}
              <br />
              • Tarih: {appointment && formatDate(appointment.randevu_tarihi)}
              <br />
              • Durum: {appointment && APPOINTMENT_STATUS_LABELS[appointment.durum]}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAppointment}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Siliniyor..." : "Evet, Sil"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Close Appointment Modal */}
      {appointment && (
        <CloseAppointmentModal
          open={showCloseModal}
          onOpenChange={setShowCloseModal}
          appointmentId={appointment.id}
          appointmentDate={formatDate(appointment.randevu_tarihi)}
          faultNumber={appointment.ariza.baslik}
          faultTitle={appointment.ariza.baslik}
          onConfirm={handleCloseAppointment}
          loading={closingAppointment}
        />
      )}
    </div>
  )
}
