version: '3.8'

services:
  ml-fault-classifier:
    build: .
    container_name: ml-fault-classifier
    ports:
      - "3050:3050"
    environment:
      - NODE_ENV=production
      - PORT=3050
      - MAIN_APP_URL=http://host.docker.internal:3001
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3050/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ml-network

networks:
  ml-network:
    driver: bridge 