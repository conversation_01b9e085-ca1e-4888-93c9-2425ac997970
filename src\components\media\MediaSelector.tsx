import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export interface MediaFile {
  id: number;
  filename: string;
  url: string;
  originalName: string;
  size: number;
  mimeType: string;
  createdAt: string;
}

interface MediaSelectorProps {
  onSelect: (media: MediaFile) => void;
  selectedMedia?: MediaFile | null;
  trigger: React.ReactNode;
  title?: string;
  description?: string;
  acceptedTypes?: string[];
  customFolder?: string;
}

export const MediaSelector: React.FC<MediaSelectorProps> = ({
  onSelect,
  selectedMedia,
  trigger,
  title = "Medya Seç",
  description,
  acceptedTypes = ["image/*"],
  customFolder = "media",
}) => {
  const [open, setOpen] = useState(false);
  const [mediaList, setMediaList] = useState<MediaFile[]>([]);
  const [tab, setTab] = useState<"gallery" | "upload">("gallery");
  const [uploading, setUploading] = useState(false);

  // Galeri verisini çek
  const fetchMedia = async () => {
    const res = await fetch(`/api/media?customFolder=${customFolder}`);
    const data = await res.json();
    setMediaList(data.data || []);
  };

  React.useEffect(() => {
    if (open && tab === "gallery") fetchMedia();
  }, [open, tab]);

  // Dosya yükle
  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return;
    setUploading(true);
    const formData = new FormData();
    formData.append("file", e.target.files[0]);
    formData.append("folder", customFolder);
    await fetch("/api/upload", { method: "POST", body: formData });
    setUploading(false);
    setTab("gallery");
    fetchMedia();
  };

  return (
    <>
      <span onClick={() => setOpen(true)}>{trigger}</span>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            {description && <div className="text-sm text-gray-500">{description}</div>}
          </DialogHeader>
          <Tabs value={tab} onValueChange={setTab as any}>
            <TabsList>
              <TabsTrigger value="gallery">Galeri</TabsTrigger>
              <TabsTrigger value="upload">Yükle</TabsTrigger>
            </TabsList>
            <TabsContent value="gallery">
              <div className="grid grid-cols-3 gap-4 mt-4">
                {mediaList.length === 0 && <div>Hiç dosya yok</div>}
                {mediaList.map((media) => (
                  <div
                    key={media.id}
                    className={`border rounded p-2 cursor-pointer ${selectedMedia?.id === media.id ? "border-blue-500" : ""}`}
                    onClick={() => { onSelect(media); setOpen(false); }}
                  >
                    <img src={media.url} alt={media.originalName} className="w-full h-24 object-cover" />
                    <div className="text-xs mt-1">{media.originalName}</div>
                  </div>
                ))}
              </div>
            </TabsContent>
            <TabsContent value="upload">
              <div className="mt-4">
                <Input type="file" accept={acceptedTypes.join(",")} onChange={handleUpload} disabled={uploading} />
                {uploading && <div>Yükleniyor...</div>}
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
}; 