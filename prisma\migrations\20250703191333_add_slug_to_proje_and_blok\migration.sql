/*
  Warnings:

  - A unique constraint covering the columns `[slug]` on the table `bloklar` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[slug]` on the table `projeler` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `slug` to the `bloklar` table without a default value. This is not possible if the table is not empty.
  - Added the required column `slug` to the `projeler` table without a default value. This is not possible if the table is not empty.

*/

-- Add slug columns with default values
ALTER TABLE "projeler" ADD COLUMN "slug" TEXT;
ALTER TABLE "bloklar" ADD COLUMN "slug" TEXT;

-- Update existing projects with default slugs
UPDATE "projeler" SET "slug" = 'proje-' || id WHERE "slug" IS NULL;

-- Update existing blocks with default slugs
UPDATE "bloklar" SET "slug" = 'blok-' || id WHERE "slug" IS NULL;

-- Make slug columns NOT NULL
ALTER TABLE "projeler" ALTER COLUMN "slug" SET NOT NULL;
ALTER TABLE "bloklar" ALTER COLUMN "slug" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "bloklar_slug_key" ON "bloklar"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "projeler_slug_key" ON "projeler"("slug");
