import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CacheManager } from "@/lib/cache"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Pagination
    const page = parseInt(searchParams.get("sayfa") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const skip = (page - 1) * limit

    // Filters
    const rol = searchParams.getAll("rol")
    const durum = searchParams.getAll("durum")
    const arama = searchParams.get("arama")

    // Generate cache key based on parameters
    const cacheKey = CacheManager.generateKey("users", {
      page,
      limit,
      rol: rol.join(","),
      durum: durum.join(","),
      arama,
    })

    // Try to get from cache first
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for users: ${cacheKey}`)
      return NextResponse.json(cached)
    }

    console.log(`Cache miss for users: ${cacheKey}`)

    // Build where clause
    const where: Record<string, unknown> = {
      silindi_mi: false,
    }

    // Role filter
    if (rol.length > 0) {
      where.rol = { in: rol }
    }

    // Status filter
    if (durum.length > 0) {
      where.durum = { in: durum }
    }

    // Search filter
    if (arama) {
      where.OR = [
        { ad: { contains: arama, mode: "insensitive" } },
        { soyad: { contains: arama, mode: "insensitive" } },
        { email: { contains: arama, mode: "insensitive" } },
      ]
    }

    // Get total count
    const total = await prisma.user.count({ where })

    // Get users with relations
    const users = await prisma.user.findMany({
      where,
      include: {
        departman: {
          select: {
            id: true,
            ad: true,
          }
        },
        daire: {
          include: {
            blok: {
              include: {
                proje: {
                  select: {
                    id: true,
                    ad: true,
                  }
                }
              }
            }
          }
        }
      },
      orderBy: {
        olusturulma_tarihi: "desc",
      },
      skip,
      take: limit,
    })

    const result = {
      users,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
    }

    // Cache the result for 5 minutes
    await CacheManager.set(cacheKey, result, 300)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching users:", error)
    return NextResponse.json(
      { message: "Kullanıcılar yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Your existing POST logic here
    // ... existing code ...

    // Invalidate users cache after creating new user
    await CacheManager.deletePattern("users:*")
    
    return NextResponse.json({ message: "Kullanıcı başarıyla oluşturuldu" })
  } catch (error) {
    console.error("Error creating user:", error)
    return NextResponse.json(
      { message: "Kullanıcı oluşturulurken hata oluştu" },
      { status: 500 }
    )
  }
} 