"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"

const statusSchema = z.object({
  ad: z.string().min(1, "Ad alanı gereklidir"),
  sira: z.coerce.number().min(1, "Sıra 1'den büyük olmalıdır"),
  renk: z.string().regex(/^#[0-9A-F]{6}$/i, "<PERSON><PERSON><PERSON><PERSON><PERSON> bir hex renk kodu giriniz"),
  aciklama: z.string().optional(),
})

type StatusFormValues = z.infer<typeof statusSchema>

interface Status {
  id: string
  ad: string
  sira: number
  renk: string
  aciklama?: string
}

interface StatusDialogProps {
  status?: Status | null
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export function StatusDialog({ status, isOpen, onClose, onSuccess }: StatusDialogProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!status

  const form = useForm<StatusFormValues>({
    resolver: zodResolver(statusSchema),
    defaultValues: {
      ad: "",
      sira: 1,
      renk: "#3B82F6",
      aciklama: "",
    },
  })

  useEffect(() => {
    if (status) {
      form.reset({
        ad: status.ad,
        sira: status.sira,
        renk: status.renk,
        aciklama: status.aciklama || "",
      })
    } else {
      form.reset({
        ad: "",
        sira: 1,
        renk: "#3B82F6",
        aciklama: "",
      })
    }
  }, [status, form])

  const onSubmit = async (values: StatusFormValues) => {
    try {
      setLoading(true)

      const url = isEditing ? `/api/statuses/${status.id}` : "/api/statuses"
      const method = isEditing ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Bir hata oluştu")
      }

      toast.success(
        isEditing 
          ? "Arıza durumu başarıyla güncellendi"
          : "Arıza durumu başarıyla oluşturuldu"
      )

      onSuccess()
    } catch (error) {
      console.error("Status save error:", error)
      toast.error(error instanceof Error ? error.message : "Bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Arıza Durumu Düzenle" : "Yeni Arıza Durumu"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Arıza durumu bilgilerini güncelleyin." 
              : "Yeni bir arıza durumu tanımlayın."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="ad"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ad</FormLabel>
                    <FormControl>
                      <Input placeholder="Örn: Devam Ediyor" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sira"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sıra</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1" 
                        placeholder="1, 2, 3..." 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      İş akışındaki sıra
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="renk"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Renk</FormLabel>
                  <FormControl>
                    <div className="flex gap-2">
                      <Input 
                        placeholder="#3B82F6" 
                        {...field} 
                        className="flex-1"
                      />
                      <div 
                        className="w-10 h-10 rounded border border-input" 
                        style={{ backgroundColor: field.value }}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Hex renk kodu (örn: #3B82F6)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="aciklama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama (Opsiyonel)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Bu durum hakkında açıklama..."
                      className="resize-none"
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                İptal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Kaydediliyor..." : isEditing ? "Güncelle" : "Oluştur"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
} 