import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { Prisma } from "@prisma/client"

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string, materialId: string }> }
) {
  try {
    const resolvedParams = await params
    const { id: randevuId, materialId } = resolvedParams

    // Randevu malzemesini kontrol et
    const randevuMalzeme = await prisma.randevuMalzeme.findUnique({
      where: { id: materialId },
      include: {
        malzeme: true
      }
    })

    if (!randevuMalzeme) {
      return NextResponse.json(
        { error: "Malzeme kullanım kaydı bulunamadı" },
        { status: 404 }
      )
    }

    if (randevuMalzeme.randevu_id !== randevuId) {
      return NextResponse.json(
        { error: "Bu malzeme kaydı belirtilen randevuya ait değil" },
        { status: 400 }
      )
    }

    // Malzeme kullanım kaydını sil
    await prisma.randevuMalzeme.delete({
      where: { id: materialId }
    })

    // TODO: Stok yönetimi henüz uygulanmadı
    // Gelecekte malzeme stoğu takibi için stok_miktari field'ı eklenecek

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting randevu malzemesi:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
