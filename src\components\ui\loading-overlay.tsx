"use client"

import { useLoading } from "@/contexts/loading-context"
import { Loader2 } from "lucide-react"

export function LoadingOverlay() {
  const { isLoading } = useLoading()

  if (!isLoading) return null

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <p className="text-sm font-medium text-gray-900">Yükleniyor...</p>
      </div>
    </div>
  )
}
