import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"
import { CacheManager } from "@/lib/cache"

// Validation schema
const prioritySchema = z.object({
  ad: z.string().min(1, "Ad alanı gereklidir"),
  seviye: z.number().min(1, "Seviye 1'den büyük olmalıdır").max(10, "Seviye 10'dan küçük olmalıdır"),
  renk: z.string().regex(/^#[0-9A-F]{6}$/i, "Geçerli bir hex renk kodu giriniz"),
  aciklama: z.string().optional(),
})

// GET - List all priorities
export async function GET() {
  try {
    const cacheKey = CacheManager.generateKey("priorities", { all: "true" })

    // Try to get from cache first
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for priorities: ${cacheKey}`)
      return NextResponse.json(cached)
    }

    console.log(`Cache miss for priorities: ${cacheKey}`)

    const priorities = await prisma.aciliyetSeviye.findMany({
      where: {
        silindi_mi: false,
      },
      orderBy: {
        seviye: "asc",
      },
    })

    const result = {
      priorities,
      total: priorities.length,
    }

    // Cache the result for 15 minutes
    await CacheManager.set(cacheKey, result, 900)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching priorities:", error)
    return NextResponse.json(
      { message: "Öncelik seviyeleri yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

// POST - Create new priority
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate data
    const validatedData = prioritySchema.parse(body)

    // Check if level already exists
    const existingLevel = await prisma.aciliyetSeviye.findFirst({
      where: {
        seviye: validatedData.seviye,
        silindi_mi: false,
      },
    })

    if (existingLevel) {
      return NextResponse.json(
        { message: `${validatedData.seviye} seviyesi zaten kullanılıyor` },
        { status: 400 }
      )
    }

    // Check if name already exists
    const existingName = await prisma.aciliyetSeviye.findFirst({
      where: {
        ad: validatedData.ad,
        silindi_mi: false,
      },
    })

    if (existingName) {
      return NextResponse.json(
        { message: `"${validatedData.ad}" adı zaten kullanılıyor` },
        { status: 400 }
      )
    }

    const priority = await prisma.aciliyetSeviye.create({
      data: {
        ...validatedData,
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date(),
      },
    })

    // Invalidate priorities cache after creating new priority
    await CacheManager.deletePattern("priorities:*")

    return NextResponse.json(priority, { status: 201 })
  } catch (error) {
    console.error("Error creating priority:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          message: "Geçersiz veri",
          errors: error.errors 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { message: "Öncelik seviyesi oluşturulurken hata oluştu" },
      { status: 500 }
    )
  }
} 