"use client"

import { useState, useEffect } from "react"
import { Plus } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { TechnicianManagement } from "@/components/technicians/technician-management"
import { ExpertiseManagement } from "@/components/technicians/expertise-management"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb } from "@/components/layout/breadcrumb"

export default function TechniciansPage() {
  const [stats, setStats] = useState({
    totalTechnicians: 0,
    expertTechnicians: 0,
    totalExpertiseAreas: 0,
    loading: true
  })

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [techResponse, expertiseResponse] = await Promise.all([
          fetch('/api/technicians'),
          fetch('/api/expertise-areas')
        ])
        
        const technicians = await techResponse.json()
        const expertiseAreas = await expertiseResponse.json()
        
        const expertCount = technicians.filter((tech: any) => 
          tech.uzmanlikAlanlari.some((ua: any) => ua.seviye === 'UZMAN')
        ).length
        
        setStats({
          totalTechnicians: technicians.length,
          expertTechnicians: expertCount,
          totalExpertiseAreas: expertiseAreas.length,
          loading: false
        })
      } catch (error) {
        console.error('Stats yüklenirken hata:', error)
        setStats(prev => ({ ...prev, loading: false }))
      }
    }

    fetchStats()
  }, [])

  return (
    <div className="space-y-8">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Modern Header with Gradient */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative">
          <div className="flex items-center gap-4 mb-4">
            <div className="flex items-center justify-center w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl">
              <span className="text-3xl">👨‍🔧</span>
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">Teknisyen Yönetim Sistemi</h1>
              <p className="text-white/90 text-lg">
                🔧 Teknisyenleri ve uzmanlık alanlarını profesyonelce yönetin
              </p>
            </div>
          </div>
          
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 border border-white/30 hover:bg-white/30 transition-all duration-200">
              <div className="flex items-center gap-3">
                <span className="text-2xl">👥</span>
                <div>
                  <p className="text-white/90 text-sm">Toplam Teknisyen</p>
                  <p className="text-white text-xl font-bold">
                    {stats.loading ? (
                      <span className="animate-pulse">●●●</span>
                    ) : (
                      stats.totalTechnicians
                    )}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 border border-white/30 hover:bg-white/30 transition-all duration-200">
              <div className="flex items-center gap-3">
                <span className="text-2xl">⭐</span>
                <div>
                  <p className="text-white/90 text-sm">Uzman Seviye</p>
                  <p className="text-white text-xl font-bold">
                    {stats.loading ? (
                      <span className="animate-pulse">●●●</span>
                    ) : (
                      `${stats.expertTechnicians}/${stats.totalTechnicians}`
                    )}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 border border-white/30 hover:bg-white/30 transition-all duration-200">
              <div className="flex items-center gap-3">
                <span className="text-2xl">🔧</span>
                <div>
                  <p className="text-white/90 text-sm">Uzmanlık Alanı</p>
                  <p className="text-white text-xl font-bold">
                    {stats.loading ? (
                      <span className="animate-pulse">●●●</span>
                    ) : (
                      stats.totalExpertiseAreas
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Tabs defaultValue="technicians" className="space-y-8">
        <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto bg-gray-100/80 p-1.5 rounded-xl border border-gray-200/50 backdrop-blur-sm">
          <TabsTrigger 
            value="technicians" 
            className="flex items-center justify-center gap-2 rounded-lg px-6 py-3 transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-blue-700 data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 data-[state=inactive]:hover:bg-white/50"
          >
            <span className="text-lg">👨‍🔧</span>
            <span className="font-medium">Teknisyenler</span>
          </TabsTrigger>
          <TabsTrigger 
            value="expertise" 
            className="flex items-center justify-center gap-2 rounded-lg px-6 py-3 transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-orange-700 data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 data-[state=inactive]:hover:bg-white/50"
          >
            <span className="text-lg">🎯</span>
            <span className="font-medium">Uzmanlık Alanları</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="technicians" className="space-y-6">
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <TechnicianManagement />
          </div>
        </TabsContent>

        <TabsContent value="expertise" className="space-y-6">
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
            <ExpertiseManagement />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 