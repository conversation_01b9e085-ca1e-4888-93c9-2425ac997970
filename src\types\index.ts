// ===== CORE TYPES =====

export interface BaseEntity {
  id: string
  olusturulma_tarihi: Date
  guncelleme_tarihi: Date
  olusturan_id?: string
  guncelleyen_id?: string
  silindi_mi: boolean
  silinme_tarihi?: Date
  silen_id?: string
}

// ===== USER TYPES =====

export interface User extends BaseEntity {
  ad: string
  soyad: string
  email: string
  telefon?: string
  resim?: string
  rol: UserRole
  durum: UserStatus
  departman_id?: string
  daire_id?: string
  
  // Relations
  departman?: Departman
  daire?: Apartment
  uzmanlik_alanlari?: TechnicianExpertise[]
}

export interface Departman extends BaseEntity {
  ad: string
  aciklama?: string
  kullanicilar?: User[]
}

// ===== PROJECT TYPES =====

export interface Project extends BaseEntity {
  ad: string
  aciklama?: string
  adres: string
  baslangic_tarihi: Date
  bitis_tarihi?: Date
  bloklar?: Block[]
  slug: string
  _count?: {
    bloklar: number
    arizalar: number
  }
}

export interface Block extends BaseEntity {
  ad: string
  aciklama?: string
  proje_id: string
  proje?: Project
  daireler?: Apartment[]
  slug: string
}

export interface Apartment extends BaseEntity {
  numara: string
  kat: number
  blok_id: string
  blok?: Block
  sakinler?: User[]
  arizalar?: Fault[]
  slug: string
}

// ===== FAULT TYPES =====

export interface Fault extends BaseEntity {
  baslik: string
  aciklama: string
  konum?: string
  oncelik_seviye_id: string
  kategori_id: string
  durum_id: string
  daire_id: string
  slug: string
  
  // Relations
  oncelik_seviye?: Priority
  kategori?: Category
  durum?: Status
  daire?: Apartment
  randevular?: Appointment[]
  dosyalar?: string[]
}

export interface Category extends BaseEntity {
  ad: string
  aciklama?: string
  renk: string
  ikon?: string
  arizalar?: Fault[]
}

export interface Priority extends BaseEntity {
  ad: string
  seviye: number
  renk: string
  aciklama?: string
  arizalar?: Fault[]
}

export interface Status extends BaseEntity {
  ad: string
  renk: string
  aciklama?: string
  sira: number
  arizalar?: Fault[]
}

// ===== APPOINTMENT TYPES =====

export interface Appointment extends BaseEntity {
  ariza_id: string
  randevu_tarihi: Date
  durum: AppointmentStatus
  aciklama?: string
  onceki_randevu_id?: string
  devam_randevusu_mu: boolean
  
  // Relations
  ariza?: Fault
  teknisyenler?: AppointmentTechnician[]
  islemler?: AppointmentWork[]
  malzemeler?: AppointmentMaterial[]
  sonuc?: AppointmentResult
}

export interface AppointmentTechnician {
  id: string
  randevu_id: string
  teknisyen_id: string
  teknisyen?: User
  randevu?: Appointment
}

export interface AppointmentWork extends BaseEntity {
  randevu_id: string
  islem_turu_id: string
  teknisyen_id: string
  aciklama?: string
  baslangic_saati?: Date
  bitis_saati?: Date
  durum: WorkStatus
  
  // Relations
  randevu?: Appointment
  islem_turu?: WorkType
  teknisyen?: User
}

export interface AppointmentMaterial extends BaseEntity {
  randevu_id: string
  malzeme_id: string
  teknisyen_id: string
  miktar: number
  birim_fiyat?: number
  toplam_fiyat?: number
  
  // Relations
  randevu?: Appointment
  malzeme?: Material
  teknisyen?: User
}

export interface AppointmentResult extends BaseEntity {
  randevu_id: string
  sonuc_durum: AppointmentResultStatus
  aciklama?: string
  musteri_memnuniyet?: number
  tekrar_ziyaret_gerekli: boolean
  kullanici_id: string
  
  // Relations
  randevu?: Appointment
  kullanici?: User
}

// ===== MATERIAL TYPES =====

export interface Material extends BaseEntity {
  ad: string
  birim: string
  aciklama?: string
  stok_miktar?: number
  min_stok_seviye?: number
  birim_fiyat?: number
  
  // Relations
  randevu_malzemeleri?: AppointmentMaterial[]
}

export interface WorkType extends BaseEntity {
  ad: string
  aciklama?: string
  tahmini_sure?: number
  
  // Relations
  randevu_islemleri?: AppointmentWork[]
}

// ===== EXPERTISE TYPES =====

export interface ExpertiseArea extends BaseEntity {
  ad: string
  aciklama?: string
  renk: string
  teknisyen_uzmanlik_alanlari?: TechnicianExpertise[]
}

export interface TechnicianExpertise {
  id: string
  teknisyen_id: string
  uzmanlik_alani_id: string
  seviye: ExpertiseLevel
  olusturulma_tarihi: Date
  guncelleme_tarihi: Date
  
  // Relations
  teknisyen?: User
  uzmanlik_alani?: ExpertiseArea
}

// ===== ENUMS =====

export enum UserRole {
  ADMIN = "ADMIN",
  MANAGER = "MANAGER", 
  TECHNICIAN = "TECHNICIAN",
  USER = "USER"
}

export enum UserStatus {
  PENDING = "PENDING",
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  SUSPENDED = "SUSPENDED"
}

export enum AppointmentStatus {
  PLANLI = "PLANLI",
  DEVAM_EDIYOR = "DEVAM_EDIYOR",
  TAMAMLANDI = "TAMAMLANDI",
  IPTAL = "IPTAL"
}

export enum WorkStatus {
  PLANLI = "PLANLI",
  DEVAM_EDIYOR = "DEVAM_EDIYOR",
  TAMAMLANDI = "TAMAMLANDI",
  BEKLEMEDE = "BEKLEMEDE",
  IPTAL = "IPTAL"
}

export enum AppointmentResultStatus {
  TAMAMEN_COZULDU = "TAMAMEN_COZULDU",
  KISMI_COZULDU = "KISMI_COZULDU",
  COZULEMEDI = "COZULEMEDI",
  ERTELENDI = "ERTELENDI",
  IPTAL_EDILDI = "IPTAL_EDILDI"
}

export enum ExpertiseLevel {
  BASLANGIC = "BASLANGIC",
  ORTA = "ORTA",
  ILERI = "ILERI", 
  UZMAN = "UZMAN"
}

// ===== API RESPONSE TYPES =====

export interface ApiResponse<T = any> {
  data?: T
  error?: string
  message?: string
  success: boolean
  total?: number
  page?: number
  limit?: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  total: number
  page: number
  limit: number
  totalPages: number
}

// ===== FORM TYPES =====

export interface CreateFaultData {
  baslik: string
  aciklama: string
  konum?: string
  oncelik_seviye_id: string
  kategori_id: string
  daire_id: string
  dosyalar?: File[]
}

export interface CreateAppointmentData {
  ariza_id: string
  randevu_tarihi: Date
  durum?: AppointmentStatus
  aciklama?: string
  teknisyen_ids: string[]
  onceki_randevu_id?: string
  devam_randevusu_mu?: boolean
}

export interface CreateUserData {
  ad: string
  soyad: string
  email: string
  telefon?: string
  rol: UserRole
  departman_id?: string
  daire_id?: string
  sifre?: string
}

// ===== STATISTICS TYPES =====

export interface DashboardStats {
  faults: {
    total: number
    open: number
    closed: number
    pending: number
  }
  appointments: {
    total: number
    today: number
    thisWeek: number
    thisMonth: number
  }
  technicians: {
    total: number
    active: number
    busy: number
  }
  users: {
    total: number
    active: number
    pending: number
  }
}

export interface FaultStats {
  total: number
  byStatus: Record<string, number>
  byCategory: Record<string, number>
  byPriority: Record<string, number>
  monthly: Array<{
    month: string
    count: number
  }>
}

// ===== FILTER TYPES =====

export interface FaultFilters {
  status?: string[]
  category?: string[]
  priority?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
  search?: string
}

export interface UserFilters {
  role?: string[]
  status?: string[]
  department?: string[]
  search?: string
}

export interface AppointmentFilters {
  status?: string[]
  technician?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
  search?: string
} 