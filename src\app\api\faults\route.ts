import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("sayfa") || "1", 10)
    const limit = parseInt(searchParams.get("limit") || "10", 10)
    const offset = (page - 1) * limit

    // Filtreler
    const durum = searchParams.getAll("durum")
    const oncelik = searchParams.getAll("oncelik")
    const kategori = searchParams.get("kategori")
    const teknisyen = searchParams.get("teknisyen")
    const daire = searchParams.get("daire")
    const blok = searchParams.get("blok")
    const proje = searchParams.get("proje")
    const arama = searchParams.get("arama")
    const baslangic = searchParams.get("baslangic")
    const bitis = searchParams.get("bitis")

    // Temel filtre
    const where: any = {
      silindi_mi: false,
    }
    if (durum.length > 0) {
      where.durum = { ad: { in: durum } }
    }
    if (oncelik.length > 0) {
      where.aciliyet = { ad: { in: oncelik } }
    }
    if (kategori) {
      where.tip = { id: kategori }
    }
    if (daire) {
      where.daire_id = daire
    }
    if (blok) {
      where.daire = { blok: { id: blok } }
    }
    if (proje) {
      where.daire = { blok: { proje: { id: proje } } }
    }
    if (arama) {
      where.OR = [
        { baslik: { contains: arama, mode: "insensitive" } },
        { aciklama: { contains: arama, mode: "insensitive" } },
      ]
    }
    if (baslangic || bitis) {
      where.olusturulma_tarihi = {}
      if (baslangic) where.olusturulma_tarihi.gte = new Date(baslangic)
      if (bitis) where.olusturulma_tarihi.lte = new Date(bitis)
    }

    // Toplam kayıt sayısı
    const total = await prisma.ariza.count({ where })

    // Liste
    const faults = await prisma.ariza.findMany({
      where,
      orderBy: { olusturulma_tarihi: "desc" },
      skip: offset,
      take: limit,
      include: {
        tip: true,
        durum: true,
        aciliyet: true,
        daire: {
          select: {
            id: true,
            slug: true,
            blok: {
              select: {
                id: true,
                ad: true,
                slug: true,
                proje: {
                  select: {
                    id: true,
                    ad: true,
                    slug: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    return NextResponse.json({ faults, total, page })
  } catch (error) {
    console.error("Arıza listesi alınırken hata:", error)
    return NextResponse.json(
      { message: "Arıza listesi yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
} 