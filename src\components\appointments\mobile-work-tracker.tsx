"use client"

import { useState, useEffect } from "react"
import { 
  Smartphone,
  Clock,
  MapPin,
  Camera,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  Square,
  Wrench,
  FileText,
  Phone,
  Navigation,
  Battery,
  Wifi,
  Signal
} from "lucide-react"
import { toast } from "sonner"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"

interface AppointmentData {
  id: string
  randevu_tarihi: Date
  durum: string
  aciklama: string | null
  ariza: {
    id: string
    numara: string
    baslik: string
    kategori: string
    daire: {
      numara: string
      blok: {
        ad: string
        proje: {
          ad: string
        }
      }
    }
  }
}

interface WorkStep {
  id: string
  title: string
  description: string
  completed: boolean
  startTime?: Date
  endTime?: Date
  notes?: string
  photos?: string[]
}

interface MobileWorkTrackerProps {
  appointment: AppointmentData
  onUpdateProgress: (progress: any) => void
}

export function MobileWorkTracker({ appointment, onUpdateProgress }: MobileWorkTrackerProps) {
  const [isWorking, setIsWorking] = useState(false)
  const [workStartTime, setWorkStartTime] = useState<Date | null>(null)
  const [elapsedTime, setElapsedTime] = useState(0)
  const [currentStep, setCurrentStep] = useState(0)
  const [workNotes, setWorkNotes] = useState("")
  const [photos, setPhotos] = useState<string[]>([])
  const [workSteps, setWorkSteps] = useState<WorkStep[]>([
    {
      id: "1",
      title: "Saha Varış",
      description: "Lokasyona varış ve durum tespiti",
      completed: false
    },
    {
      id: "2", 
      title: "Problem Analizi",
      description: "Arızanın detaylı incelenmesi",
      completed: false
    },
    {
      id: "3",
      title: "Malzeme Kontrolü",
      description: "Gerekli malzemelerin kontrolü",
      completed: false
    },
    {
      id: "4",
      title: "Onarım İşlemi",
      description: "Aktif onarım çalışması",
      completed: false
    },
    {
      id: "5",
      title: "Test ve Kontrol",
      description: "Onarım sonrası test işlemleri",
      completed: false
    },
    {
      id: "6",
      title: "Teslim ve Onay",
      description: "İş teslimi ve müşteri onayı",
      completed: false
    }
  ])

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isWorking && workStartTime) {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - workStartTime.getTime())
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isWorking, workStartTime])

  const formatElapsedTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    return `${hours.toString().padStart(2, '0')}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`
  }

  const handleStartWork = () => {
    setIsWorking(true)
    setWorkStartTime(new Date())
    toast.success("İş başlatıldı")
  }

  const handlePauseWork = () => {
    setIsWorking(false)
    toast.info("İş duraklatıldı")
  }

  const handleStopWork = () => {
    setIsWorking(false)
    setWorkStartTime(null)
    setElapsedTime(0)
    toast.success("İş sonlandırıldı")
  }

  const handleStepComplete = (stepIndex: number) => {
    setWorkSteps(prev => prev.map((step, index) => 
      index === stepIndex 
        ? { ...step, completed: true, endTime: new Date() }
        : step
    ))
    
    if (stepIndex === currentStep) {
      setCurrentStep(stepIndex + 1)
    }
    
    toast.success(`${workSteps[stepIndex].title} tamamlandı`)
  }

  const handleTakePhoto = () => {
    // Mock photo functionality
    const newPhoto = `photo_${Date.now()}.jpg`
    setPhotos(prev => [...prev, newPhoto])
    toast.success("Fotoğraf eklendi")
  }

  const calculateProgress = () => {
    const completedSteps = workSteps.filter(step => step.completed).length
    return Math.round((completedSteps / workSteps.length) * 100)
  }

  const getCurrentTime = () => {
    return new Date().toLocaleTimeString("tr-TR", {
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  return (
    <div className="max-w-md mx-auto bg-gradient-to-b from-blue-50 to-white min-h-screen">
      {/* Mobile Status Bar */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 flex justify-between items-center text-sm">
        <div className="flex items-center gap-1">
          <Signal className="h-4 w-4" />
          <Wifi className="h-4 w-4" />
          <span>{getCurrentTime()}</span>
        </div>
        <div className="flex items-center gap-1">
          <span>85%</span>
          <Battery className="h-4 w-4" />
        </div>
      </div>

      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
        <div className="flex items-center gap-3 mb-3">
          <Smartphone className="h-6 w-6" />
          <div>
            <h1 className="font-bold text-lg">📱 Mobil İş Takip</h1>
            <p className="text-blue-100 text-sm">Gerçek zamanlı iş ilerlemesi</p>
          </div>
        </div>
        
        {/* Job Info */}
        <Card className="bg-white/10 border-white/20">
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-white">{appointment.ariza.numara}</h3>
                  <p className="text-blue-100 text-sm">{appointment.ariza.baslik}</p>
                </div>
                <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                  {appointment.ariza.kategori}
                </Badge>
              </div>
              <div className="flex items-center gap-1 text-blue-100 text-sm">
                <MapPin className="h-3 w-3" />
                {appointment.ariza.daire.blok.proje.ad} - {appointment.ariza.daire.blok.ad} - Daire {appointment.ariza.daire.numara}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Timer and Controls */}
      <div className="p-4">
        <Card className="border-0 shadow-lg">
          <CardContent className="p-6">
            <div className="text-center mb-4">
              <div className="text-3xl font-mono font-bold text-blue-600 mb-2">
                {formatElapsedTime(elapsedTime)}
              </div>
              <p className="text-sm text-muted-foreground">Çalışma Süresi</p>
            </div>
            
            <div className="flex gap-2 justify-center">
              {!isWorking ? (
                <Button 
                  onClick={handleStartWork}
                  className="bg-green-600 hover:bg-green-700 flex-1"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Başla
                </Button>
              ) : (
                <Button 
                  onClick={handlePauseWork}
                  variant="outline"
                  className="flex-1"
                >
                  <Pause className="w-4 h-4 mr-2" />
                  Duraklat
                </Button>
              )}
              <Button 
                onClick={handleStopWork}
                variant="destructive"
                className="flex-1"
              >
                <Square className="w-4 h-4 mr-2" />
                Bitir
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <div className="px-4 mb-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">İlerleme Durumu</h3>
              <span className="text-lg font-bold text-blue-600">
                %{calculateProgress()}
              </span>
            </div>
            <Progress value={calculateProgress()} className="h-3 mb-2" />
            <p className="text-xs text-muted-foreground">
              {workSteps.filter(s => s.completed).length} / {workSteps.length} adım tamamlandı
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Work Steps */}
      <div className="px-4 mb-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-blue-600" />
          İş Adımları
        </h3>
        <div className="space-y-3">
          {workSteps.map((step, index) => (
            <Card 
              key={step.id} 
              className={`border-0 shadow-sm transition-all ${
                step.completed ? "bg-green-50 border-green-200" :
                index === currentStep ? "bg-blue-50 border-blue-200" :
                "bg-white"
              }`}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      step.completed ? "bg-green-600 text-white" :
                      index === currentStep ? "bg-blue-600 text-white" :
                      "bg-gray-200 text-gray-600"
                    }`}>
                      {step.completed ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <span className="text-xs font-bold">{index + 1}</span>
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{step.title}</h4>
                      <p className="text-xs text-muted-foreground">{step.description}</p>
                    </div>
                  </div>
                  {!step.completed && index <= currentStep && (
                    <Button
                      size="sm"
                      onClick={() => handleStepComplete(index)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <CheckCircle className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-4 mb-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <Wrench className="h-5 w-5 text-blue-600" />
          Hızlı İşlemler
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-1"
            onClick={handleTakePhoto}
          >
            <Camera className="w-5 h-5" />
            <span className="text-xs">Fotoğraf</span>
          </Button>
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-1"
            onClick={() => window.open(`tel:+90`, '_self')}
          >
            <Phone className="w-5 h-5" />
            <span className="text-xs">Ara</span>
          </Button>
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-1"
            onClick={() => toast.info("GPS navigasyon açılıyor")}
          >
            <Navigation className="w-5 h-5" />
            <span className="text-xs">Navigasyon</span>
          </Button>
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-1"
          >
            <FileText className="w-5 h-5" />
            <span className="text-xs">Rapor</span>
          </Button>
        </div>
      </div>

      {/* Work Notes */}
      <div className="px-4 mb-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          İş Notları
        </h3>
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <Textarea
              placeholder="İş ile ilgili notlarınızı buraya yazın..."
              value={workNotes}
              onChange={(e) => setWorkNotes(e.target.value)}
              rows={3}
              className="resize-none"
            />
            {photos.length > 0 && (
              <div className="mt-3 pt-3 border-t">
                <p className="text-sm font-medium mb-2">Eklenen Fotoğraflar:</p>
                <div className="flex gap-2 flex-wrap">
                  {photos.map((photo, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      📷 {photo}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Bottom Actions */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4 max-w-md mx-auto">
        <div className="flex gap-3">
          <Button 
            variant="outline" 
            className="flex-1"
            onClick={() => toast.info("Taslak kaydedildi")}
          >
            Kaydet
          </Button>
          <Button 
            className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            onClick={() => {
              onUpdateProgress({
                progress: calculateProgress(),
                notes: workNotes,
                photos: photos,
                steps: workSteps
              })
              toast.success("İş güncellemesi gönderildi")
            }}
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Güncelle
          </Button>
        </div>
      </div>

      {/* Spacer for fixed bottom */}
      <div className="h-20"></div>
    </div>
  )
} 