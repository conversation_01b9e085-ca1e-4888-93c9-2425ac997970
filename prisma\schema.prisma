generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("kullanici_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_hesap_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("hesaplar")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("oturum_token")
  userId       String   @map("kullanici_id")
  expires      DateTime @map("gecerlilik_tarihi")
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("oturumlar")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("dogrulama_tokenleri")
}

model User {
  id                    String                   @id @default(cuid())
  ad                    String
  soyad                 String
  email                 String                   @unique
  emailVerified         DateTime?                @map("email_dogrulandi")
  sifre                 String?
  telefon               String?
  resim                 String?
  rol                   UserRole                 @default(USER)
  durum                 UserStatus               @default(PENDING)
  departman_id          String?
  olusturulma_tarihi    DateTime                 @default(now()) @map("olusturulma_tarihi")
  guncelleme_tarihi     DateTime                 @updatedAt @map("guncelleme_tarihi")
  olusturan_id          String?                  @map("olusturan_id")
  guncelleyen_id        String?                  @map("guncelleyen_id")
  silindi_mi            Boolean                  @default(false) @map("silindi_mi")
  silinme_tarihi        DateTime?                @map("silinme_tarihi")
  silen_id              String?                  @map("silen_id")
  daire_id              String?
  ariza_gecmisi         ArizaGecmis[]
  yorumlar              ArizaYorum[]
  accounts              Account[]
  daire                 Daire?                   @relation(fields: [daire_id], references: [id])
  departman             Departman?               @relation(fields: [departman_id], references: [id])
  olusturan             User?                    @relation("UserCreatedBy", fields: [olusturan_id], references: [id])
  olusturulanlar        User[]                   @relation("UserCreatedBy")
  sessions              Session[]
  randevu_islemleri     RandevuIslem[]
  kullandigi_malzemeler RandevuMalzeme[]
  randevu_teknisyenleri RandevuTeknisyen[]
  uzmanlik_alanlari     TeknisyenUzmanlikAlani[]
  arizalar              Ariza[]                  @relation("UserArizalar")

  @@index([email, rol])
  @@index([durum, rol])
  @@index([olusturulma_tarihi])
  @@index([departman_id])
  @@map("kullanicilar")
}

model Departman {
  id                 String    @id @default(cuid())
  ad                 String
  aciklama           String?
  olusturulma_tarihi DateTime  @default(now())
  guncelleme_tarihi  DateTime  @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean   @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  kullanicilar       User[]

  @@map("departmanlar")
}

model Proje {
  id                 String    @id @default(cuid())
  ad                 String
  slug               String    @unique
  aciklama           String?
  adres              String?
  baslangic_tarihi   DateTime
  bitis_tarihi       DateTime?
  olusturulma_tarihi DateTime  @default(now())
  guncelleme_tarihi  DateTime  @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean   @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  project_image_url  String?
  bloklar            Blok[]

  @@map("projeler")
}

model Blok {
  id                 String    @id @default(cuid())
  ad                 String
  slug               String    @unique
  aciklama           String?
  proje_id           String
  olusturulma_tarihi DateTime  @default(now())
  guncelleme_tarihi  DateTime  @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean   @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  proje              Proje     @relation(fields: [proje_id], references: [id])
  daireler           Daire[]

  @@map("bloklar")
}

model Daire {
  id                 String    @id @default(cuid())
  numara             String
  slug               String    @unique
  kat                Int
  blok_id            String
  olusturulma_tarihi DateTime  @default(now())
  guncelleme_tarihi  DateTime  @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean   @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  arizalar           Ariza[]
  blok               Blok      @relation(fields: [blok_id], references: [id])
  sakinler           User[]

  @@map("daireler")
}

model ArizaTip {
  id                 String    @id @default(cuid())
  ad                 String
  aciklama           String?
  renk               String    @default("#6B7280")
  ikon               String?
  olusturulma_tarihi DateTime  @default(now())
  guncelleme_tarihi  DateTime  @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean   @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  arizalar           Ariza[]

  @@map("ariza_tipleri")
}

model AciliyetSeviye {
  id                 String    @id @default(cuid())
  ad                 String
  seviye             Int       @unique
  renk               String
  aciklama           String?
  olusturulma_tarihi DateTime  @default(now())
  guncelleme_tarihi  DateTime  @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean   @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  arizalar           Ariza[]

  @@map("aciliyet_seviyeleri")
}

model ArizaDurum {
  id                 String        @id @default(cuid())
  ad                 String
  renk               String
  aciklama           String?
  sira               Int           @unique
  olusturulma_tarihi DateTime      @default(now())
  guncelleme_tarihi  DateTime      @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean       @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  gecmis             ArizaGecmis[]
  arizalar           Ariza[]

  @@map("ariza_durumlari")
}

model Ariza {
  id                  String         @id @default(cuid())
  baslik              String
  aciklama            String
  slug                String         @unique
  daire_id            String
  ariza_tip_id        String
  aciliyet_id         String
  durum_id            String
  bildiren_ad_soyad   String
  bildiren_telefon    String
  baslangic_tarihi    DateTime?
  hedef_bitis_tarihi  DateTime?
  gercek_bitis_tarihi DateTime?
  tahmini_maliyet     Float?
  gercek_maliyet      Float?
  resimler            String[]       @default([])
  olusturulma_tarihi  DateTime       @default(now())
  guncelleme_tarihi   DateTime       @updatedAt
  olusturan_id        String?
  guncelleyen_id      String?
  silindi_mi          Boolean        @default(false)
  silinme_tarihi      DateTime?
  silen_id            String?
  gecmis              ArizaGecmis[]
  yorumlar            ArizaYorum[]
  aciliyet            AciliyetSeviye @relation(fields: [aciliyet_id], references: [id])
  tip                 ArizaTip       @relation(fields: [ariza_tip_id], references: [id])
  daire               Daire          @relation(fields: [daire_id], references: [id])
  durum               ArizaDurum     @relation(fields: [durum_id], references: [id])
  randevular          Randevu[]
  olusturan           User?          @relation("UserArizalar", fields: [olusturan_id], references: [id])

  @@index([olusturulma_tarihi])
  @@index([durum_id, olusturulma_tarihi])
  @@index([daire_id, silindi_mi])
  @@index([ariza_tip_id, durum_id])
  @@index([aciliyet_id])
  @@index([silindi_mi, durum_id])
  @@map("arizalar")
}

model ArizaGecmis {
  id                 String     @id @default(cuid())
  ariza_id           String
  durum_id           String
  kullanici_id       String
  yorum              String?
  olusturulma_tarihi DateTime   @default(now())
  ariza              Ariza      @relation(fields: [ariza_id], references: [id])
  durum              ArizaDurum @relation(fields: [durum_id], references: [id])
  kullanici          User       @relation(fields: [kullanici_id], references: [id])

  @@map("ariza_gecmisi")
}

model ArizaYorum {
  id                 String   @id @default(cuid())
  ariza_id           String
  kullanici_id       String
  yorum              String
  resimler           String[] @default([])
  olusturulma_tarihi DateTime @default(now())
  guncelleme_tarihi  DateTime @updatedAt
  ariza              Ariza    @relation(fields: [ariza_id], references: [id])
  kullanici          User     @relation(fields: [kullanici_id], references: [id])

  @@map("ariza_yorumlari")
}

model Malzeme {
  id                  String           @id @default(cuid())
  ad                  String
  birim               String
  aciklama            String?
  olusturulma_tarihi  DateTime         @default(now())
  guncelleme_tarihi   DateTime         @updatedAt
  olusturan_id        String?
  guncelleyen_id      String?
  silindi_mi          Boolean          @default(false)
  silinme_tarihi      DateTime?
  silen_id            String?
  randevu_malzemeleri RandevuMalzeme[]

  @@map("malzemeler")
}

model Randevu {
  id                    String             @id @default(cuid())
  ariza_id              String
  randevu_tarihi        DateTime
  durum                 RandevuDurum       @default(PLANLI)
  aciklama              String?
  olusturulma_tarihi    DateTime           @default(now())
  guncelleme_tarihi     DateTime           @updatedAt
  olusturan_id          String?
  guncelleyen_id        String?
  silindi_mi            Boolean            @default(false)
  silinme_tarihi        DateTime?
  silen_id              String?
  devam_randevusu_mu    Boolean            @default(false)
  onceki_randevu_id     String?
  islemler              RandevuIslem[]
  kullanilan_malzemeler RandevuMalzeme[]
  sonuc                 RandevuSonuc?
  teknisyenler          RandevuTeknisyen[]
  ariza                 Ariza              @relation(fields: [ariza_id], references: [id])
  onceki_randevu        Randevu?           @relation("RandevuDevam", fields: [onceki_randevu_id], references: [id])
  devam_randevulari     Randevu[]          @relation("RandevuDevam")

  @@index([randevu_tarihi])
  @@index([durum, randevu_tarihi])
  @@index([ariza_id, randevu_tarihi])
  @@index([silindi_mi, randevu_tarihi])
  @@map("randevular")
}

model RandevuTeknisyen {
  id                 String   @id @default(cuid())
  randevu_id         String
  teknisyen_id       String
  olusturulma_tarihi DateTime @default(now())
  randevu            Randevu  @relation(fields: [randevu_id], references: [id], onDelete: Cascade)
  teknisyen          User     @relation(fields: [teknisyen_id], references: [id])

  @@unique([randevu_id, teknisyen_id])
  @@map("randevu_teknisyenleri")
}

model IslemTuru {
  id                 String         @id @default(cuid())
  ad                 String         @unique
  aciklama           String?
  kategori           String
  renk               String         @default("#6B7280")
  sure_dk            Int?
  olusturulma_tarihi DateTime       @default(now())
  guncelleme_tarihi  DateTime       @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  silindi_mi         Boolean        @default(false)
  silinme_tarihi     DateTime?
  silen_id           String?
  randevu_islemleri  RandevuIslem[]

  @@map("islem_turleri")
}

model RandevuIslem {
  id                 String     @id @default(cuid())
  randevu_id         String
  islem_turu_id      String
  teknisyen_id       String
  aciklama           String?
  baslangic_saat     DateTime?
  bitis_saat         DateTime?
  durum              IslemDurum @default(PLANLI)
  notlar             String?
  resimler           String[]   @default([])
  olusturulma_tarihi DateTime   @default(now())
  guncelleme_tarihi  DateTime   @updatedAt
  olusturan_id       String?
  guncelleyen_id     String?
  islem_turu         IslemTuru  @relation(fields: [islem_turu_id], references: [id])
  randevu            Randevu    @relation(fields: [randevu_id], references: [id], onDelete: Cascade)
  teknisyen          User       @relation(fields: [teknisyen_id], references: [id])

  @@map("randevu_islemleri")
}

model RandevuMalzeme {
  id                 String   @id @default(cuid())
  randevu_id         String
  malzeme_id         String
  miktar             Float
  teknisyen_id       String
  kullanim_aciklama  String?
  olusturulma_tarihi DateTime @default(now())
  olusturan_id       String?
  malzeme            Malzeme  @relation(fields: [malzeme_id], references: [id])
  randevu            Randevu  @relation(fields: [randevu_id], references: [id], onDelete: Cascade)
  teknisyen          User     @relation(fields: [teknisyen_id], references: [id])

  @@map("randevu_malzemeleri")
}

model RandevuSonuc {
  id                      String            @id @default(cuid())
  randevu_id              String            @unique
  durum                   RandevuSonucDurum
  ariza_cozuldu_mu        Boolean           @default(false)
  tamamlanma_orani        Int               @default(0)
  sonraki_randevu_gerekli Boolean           @default(false)
  teknisyen_notlari       String?
  musteri_memnuniyet      Int?
  musteri_yorumu          String?
  karsilasilan_zorluklar  String?
  resimler                String[]          @default([])
  gercek_baslangic        DateTime?
  gercek_bitis            DateTime?
  toplam_sure_dk          Int?
  toplam_malzeme_maliyeti Float             @default(0)
  iscilik_maliyeti        Float             @default(0)
  toplam_maliyet          Float             @default(0)
  olusturulma_tarihi      DateTime          @default(now())
  guncelleme_tarihi       DateTime          @updatedAt
  olusturan_id            String?
  guncelleyen_id          String?
  randevu                 Randevu           @relation(fields: [randevu_id], references: [id], onDelete: Cascade)

  @@map("randevu_sonuclari")
}

model UzmanlikAlani {
  id                          String                   @id @default(cuid())
  ad                          String                   @unique
  aciklama                    String?
  renk                        String                   @default("#3B82F6")
  olusturulma_tarihi          DateTime                 @default(now())
  guncelleme_tarihi           DateTime                 @updatedAt
  olusturan_id                String?
  guncelleyen_id              String?
  silindi_mi                  Boolean                  @default(false)
  silinme_tarihi              DateTime?
  silen_id                    String?
  teknisyen_uzmanlik_alanlari TeknisyenUzmanlikAlani[]

  @@map("uzmanlik_alanlari")
}

model TeknisyenUzmanlikAlani {
  id                 String         @id @default(cuid())
  teknisyen_id       String
  uzmanlik_alani_id  String
  seviye             UzmanlikSeviye @default(ORTA)
  olusturulma_tarihi DateTime       @default(now())
  guncelleme_tarihi  DateTime       @updatedAt
  teknisyen          User           @relation(fields: [teknisyen_id], references: [id], onDelete: Cascade)
  uzmanlik_alani     UzmanlikAlani  @relation(fields: [uzmanlik_alani_id], references: [id], onDelete: Cascade)

  @@unique([teknisyen_id, uzmanlik_alani_id])
  @@map("teknisyen_uzmanlik_alanlari")
}

enum UzmanlikSeviye {
  BASLANGIC
  ORTA
  ILERI
  UZMAN
}

enum UserRole {
  ADMIN
  USER
  TECHNICIAN
  MANAGER
}

enum UserStatus {
  PENDING
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum RandevuDurum {
  PLANLI
  DEVAM_EDIYOR
  TAMAMLANDI
  IPTAL
}

enum IslemDurum {
  PLANLI
  DEVAM_EDIYOR
  TAMAMLANDI
  BEKLEMEDE
  IPTAL
}

enum RandevuSonucDurum {
  TAMAMEN_COZULDU
  KISMI_COZULDU
  COZULEMEDI
  ERTELENDI
  IPTAL_EDILDI
}
