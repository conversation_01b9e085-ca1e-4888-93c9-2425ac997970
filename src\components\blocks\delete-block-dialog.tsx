"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Block {
  id: string
  ad: string
  proje: {
    ad: string
  }
  _count?: {
    daireler: number
  }
}

interface DeleteBlockDialogProps {
  open: boolean
  onClose: () => void
  block?: Block | null
}

export function DeleteBlockDialog({ open, onClose, block }: DeleteBlockDialogProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  if (!block) return null

  const hasApartments = block._count && block._count.daireler > 0

  const handleDelete = async () => {
    try {      setLoading(true)
      setError(null)

      const response = await fetch(`/api/blocks/${block.id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Silme işlemi başarısız")
      }

      // Başarı bildirimi
      toast.success(`"${block.ad}" bloku başarıyla silindi!`, {
        title: "Silindi",
        duration: 4000
      })

      onClose()
    } catch (error) {
      console.error("Error deleting block:", error)
      const errorMessage = error instanceof Error ? error.message : "Silme işlemi başarısız"
      
      // Hata bildirimi
      toast.error(errorMessage, {
        title: "Silme Hatası",
        duration: 6000
      })
      
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Blogu Sil
          </DialogTitle>
          <DialogDescription>
            Bu işlem geri alınamaz. Blok ve tüm ilişkili veriler silinecek.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium mb-2">Silinecek Blok:</h4>
            <p className="text-sm font-medium">{block.ad}</p>
            <p className="text-sm text-muted-foreground">Proje: {block.proje.ad}</p>
            {block._count && (
              <div className="mt-2 text-sm text-muted-foreground">
                <p>• {block._count.daireler} daire</p>
              </div>
            )}
          </div>

          {hasApartments && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Bu blok silinirse:
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li>{block._count?.daireler} daire silinecek</li>
                  <li>Bu dairelere ait tüm arıza kayıtları silinecek</li>
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? "Siliniyor..." : "Sil"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
