"use client"

import React, { useMemo } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Calendar, 
  Settings, 
  Users, 
  BarChart3, 
  FileText,
  Wrench,
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react"
import Link from "next/link"

const DashboardQuickActions = React.memo(() => {
  const quickActions = useMemo(() => [
    {
      title: "Arıza Listesi",
      description: "Tüm arızaları görüntüle ve yönet",
      icon: Wrench,
      href: "/arizalar",
      color: "from-orange-500 to-red-500",
      bgColor: "from-orange-50 to-red-50"
    },
    {
      title: "Randevular",
      description: "Teknisyen randevularını yönet",
      icon: Calendar,
      href: "/randevu",
      color: "from-purple-500 to-pink-500",
      bgColor: "from-purple-50 to-pink-50"
    },
    {
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON> Yönetimi",
      description: "<PERSON><PERSON><PERSON><PERSON><PERSON>ları ve rolleri yönet",
      icon: Users,
      href: "/kullanicilar",
      color: "from-green-500 to-emerald-500",
      bgColor: "from-green-50 to-emerald-50"
    },
    {
      title: "Proje Yönetimi",
      description: "Projeleri ve blokları yönet",
      icon: Wrench,
      href: "/projeler",
      color: "from-indigo-500 to-purple-500",
      bgColor: "from-indigo-50 to-purple-50"
    },
    {
      title: "Malzeme Yönetimi",
      description: "Malzeme stoklarını takip et",
      icon: Plus,
      href: "/malzemeler",
      color: "from-teal-500 to-cyan-500",
      bgColor: "from-teal-50 to-cyan-50"
    },
    {
      title: "Raporlar",
      description: "Detaylı raporlar ve analizler",
      icon: BarChart3,
      href: "/raporlar",
      color: "from-rose-500 to-pink-500",
      bgColor: "from-rose-50 to-pink-50"
    },
    {
      title: "Sistem Ayarları",
      description: "Sistem konfigürasyonu",
      icon: Settings,
      href: "/ayarlar",
      color: "from-gray-500 to-slate-500",
      bgColor: "from-gray-50 to-slate-50"
    }
  ], [])

  const recentReports = useMemo(() => [
    {
      title: "Aylık Arıza Raporu",
      date: "2024-01-15",
      type: "PDF",
      size: "2.3 MB"
    },
    {
      title: "Teknisyen Performans Raporu",
      date: "2024-01-14",
      type: "Excel",
      size: "1.8 MB"
    },
    {
      title: "Proje Durum Raporu",
      date: "2024-01-13",
      type: "PDF",
      size: "3.1 MB"
    }
  ], [])

  return (
    <div className="space-y-6">
      {/* Quick Actions Grid */}
      <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <Plus className="h-6 w-6 text-blue-600" />
            Hızlı İşlemler
          </CardTitle>
          <p className="text-sm text-gray-600">
            Sık kullanılan işlemlere hızlı erişim
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => {
              const IconComponent = action.icon
              return (
                <div
                  key={action.title}
                  className="flex items-center space-x-3"
                >
                  <Link href={action.href}>
                    <Card className={`border-0 shadow-md bg-gradient-to-br ${action.bgColor} hover:shadow-lg transition-all duration-300 cursor-pointer h-full`}>
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className={`p-2 rounded-lg bg-gradient-to-r ${action.color} text-white`}>
                            <IconComponent className="h-5 w-5" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 text-sm">
                              {action.title}
                            </h3>
                            <p className="text-xs text-gray-600 mt-1">
                              {action.description}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Reports and Notifications */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Reports */}
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Son Raporlar
            </CardTitle>
            <p className="text-sm text-gray-600">
              Son oluşturulan raporlar
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentReports.map((report) => (
                <div
                  key={report.title}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                      <FileText className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {report.title}
                      </p>
                      <p className="text-xs text-gray-500">
                        {report.date} • {report.type} • {report.size}
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    İndir
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Notifications */}
        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-600" />
              Sistem Bildirimleri
            </CardTitle>
            <p className="text-sm text-gray-600">
              Önemli sistem bildirimleri
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-green-50 border border-green-200">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-green-800">
                    Sistem Güncellemesi Tamamlandı
                  </p>
                  <p className="text-xs text-green-600">
                    Yeni özellikler aktif edildi
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-yellow-50 border border-yellow-200">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Yedekleme Hatırlatması
                  </p>
                  <p className="text-xs text-yellow-600">
                    Haftalık yedekleme zamanı
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-blue-50 border border-blue-200">
                <Clock className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-blue-800">
                    Performans İyileştirmesi
                  </p>
                  <p className="text-xs text-blue-600">
                    Dashboard optimizasyonu tamamlandı
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
})

DashboardQuickActions.displayName = "DashboardQuickActions"

export default DashboardQuickActions 