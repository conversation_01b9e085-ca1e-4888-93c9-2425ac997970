import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // const session = await auth()
    
    // if (!session?.user) {
    //   return NextResponse.json({ error: "Yetkisiz erişim" }, { status: 401 })
    // }

    const { id } = await params

    const fault = await prisma.ariza.findFirst({
      where: {
        id: id,
        silindi_mi: false
      },
      include: {
        daire: {
          select: {
            id: true,
            slug: true,
            blok: {
              select: {
                id: true,
                ad: true,
                slug: true,
                proje: {
                  select: {
                    id: true,
                    ad: true,
                    slug: true
                  }
                }
              }
            }
          }
        },
        tip: true,
        durum: true,
        aciliyet: true,
        randevular: {
          where: {
            silindi_mi: false
          },
          include: {
            teknisyenler: {
              include: {
                teknisyen: {
                  select: {
                    id: true,
                    ad: true,
                    soyad: true,
                    email: true,
                    telefon: true,
                    resim: true
                  }
                }
              }
            }
          },
          orderBy: {
            randevu_tarihi: 'asc'
          }
        }
      }
    })

    if (!fault) {
      return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 })
    }

    const response = NextResponse.json(fault)
    // Prevent caching to ensure fresh data
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    return response
  } catch (error) {
    console.error("Arıza detayı alınırken hata:", error)
    return NextResponse.json(
      { error: "Sunucu hatası" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Smart auth check - get session but don't fail if missing in development
    const session = await auth()
    
    // In production, require auth. In development, it's optional
    if (process.env.NODE_ENV === "production" && !session?.user) {
      return NextResponse.json({ error: "Yetkisiz erişim" }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()

    // Arızanın var olup olmadığını kontrol et
    const existingFault = await prisma.ariza.findFirst({
      where: {
        id: id,
        silindi_mi: false
      }
    })

    if (!existingFault) {
      return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 })
    }

    // Arızayı güncelle
    const updatedFault = await prisma.ariza.update({
      where: { id },
      data: {
        baslik: body.baslik,
        aciklama: body.aciklama,
        ariza_tip_id: body.kategoriId,
        durum_id: body.durumId || existingFault.durum_id,
        aciliyet_id: body.oncelikId || existingFault.aciliyet_id,
        guncelleyen_id: session?.user?.id || "system", // Use session user or fallback
      },
      include: {
        daire: {
          select: {
            id: true,
            slug: true,
            blok: {
              select: {
                id: true,
                ad: true,
                slug: true,
                proje: {
                  select: {
                    id: true,
                    ad: true,
                    slug: true
                  }
                }
              }
            }
          }
        },
        tip: true,
        durum: true,
        aciliyet: true,
        randevular: {
          where: {
            silindi_mi: false
          },
          include: {
            teknisyenler: {
              include: {
                teknisyen: {
                  select: {
                    id: true,
                    ad: true,
                    soyad: true,
                    email: true,
                    telefon: true,
                    resim: true
                  }
                }
              }
            }
          },
          orderBy: {
            randevu_tarihi: 'asc'
          }
        }
      }
    })

    return NextResponse.json(updatedFault)

  } catch (error) {
    console.error("Arıza güncelleme hatası:", error)
    return NextResponse.json(
      { error: "Arıza güncellenirken bir hata oluştu" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth()
    
    // In production, require auth. In development, it's optional
    if (process.env.NODE_ENV === "production" && !session?.user) {
      return NextResponse.json({ error: "Yetkisiz erişim" }, { status: 401 })
    }

    const { id } = await params

    // Arızanın var olup olmadığını kontrol et
    const existingFault = await prisma.ariza.findFirst({
      where: {
        id: id,
        silindi_mi: false
      }
    })

    if (!existingFault) {
      return NextResponse.json({ error: "Arıza bulunamadı" }, { status: 404 })
    }

    // Soft delete - arızayı silindi olarak işaretle
    await prisma.ariza.update({
      where: { id },
      data: {
        silindi_mi: true,
        silinme_tarihi: new Date(),
        silen_id: session?.user?.id || "system",
      }
    })

    return NextResponse.json({ message: "Arıza başarıyla silindi" })

  } catch (error) {
    console.error("Arıza silme hatası:", error)
    return NextResponse.json(
      { error: "Arıza silinirken bir hata oluştu" },
      { status: 500 }
    )
  }
}
