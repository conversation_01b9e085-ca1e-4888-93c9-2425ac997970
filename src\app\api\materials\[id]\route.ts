import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const malzeme = await prisma.malzeme.findUnique({
      where: {
        id: id,
        silindi_mi: false
      }
    })

    if (!malzeme) {
      return NextResponse.json(
        { error: "Malzeme bulunamadı" },
        { status: 404 }
      )
    }

    return NextResponse.json(malzeme)
  } catch (error) {
    console.error("Error fetching malzeme:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await req.json()
    const { ad, aciklama, birim } = body

    const malzeme = await prisma.malzeme.update({
      where: {
        id: id,
        silindi_mi: false
      },
      data: {
        ad,
        aciklama,
        birim,
        guncelleme_tarihi: new Date()
      }
    })

    return NextResponse.json(malzeme)
  } catch (error) {
    console.error("Error updating malzeme:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    await prisma.malzeme.update({
      where: {
        id: id,
        silindi_mi: false
      },
      data: {
        silindi_mi: true,
        guncelleme_tarihi: new Date()
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting malzeme:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 