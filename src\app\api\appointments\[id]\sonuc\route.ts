import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AppointmentResultAction, FaultStatus, FAULT_STATUS_LABELS } from "@/lib/enums"
import { PrismaClient, Prisma } from "@prisma/client"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const randevuId = resolvedParams.id

    // Randevu sonucunu getir
    const sonuc = await prisma.randevuSonuc.findUnique({
      where: { randevu_id: randevuId },
      include: {
        randevu: {
          include: {
            ariza: true
          }
        }
      }
    })

    if (!sonuc) {
      // Yeni randevular için boş sonuç döndür
      return NextResponse.json({
        randevu_id: randevuId,
        durum: null,
        ariza_cozuldu_mu: false,
        tamamlanma_orani: 0,
        sonraki_randevu_gerekli: false,
        teknisyen_notlari: "",
        musteri_memnuniyet: null,
        musteri_yorumu: "",
        karsila<PERSON><PERSON>_zorluklar: "",
        gercek_baslangic: null,
        gercek_bitis: null,
        toplam_sure_dk: null,
        resimler: [],
        iscilik_maliyeti: 0,
        olusturulma_tarihi: null,
        guncelleme_tarihi: null,
        randevu: null
      })
    }

    return NextResponse.json(sonuc)
  } catch (error) {
    console.error("Error fetching randevu sonucu:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const randevuId = resolvedParams.id
    const body = await request.json()
    
    const { 
      action, // AppointmentResultAction enum değeri
      teknisyen_notlari,
      musteri_memnuniyet,
      musteri_yorumu,
      karsilasilan_zorluklar,
      gercek_baslangic,
      gercek_bitis,
      resimler,
      iscilik_maliyeti,
      olusturan_id,
      closeFaultToo = false // Arıza da kapatılsın mı?
    } = body

    // Action zorunlu kontrolü
    if (!action || !Object.values(AppointmentResultAction).includes(action)) {
      return NextResponse.json(
        { error: "Geçerli bir aksiyon seçmelisiniz" },
        { status: 400 }
      )
    }

    // Teknisyen notları zorunlu
    if (!teknisyen_notlari || teknisyen_notlari.trim() === "") {
      return NextResponse.json(
        { error: "Teknisyen notları gereklidir" },
        { status: 400 }
      )
    }

    // Randevunun var olduğunu kontrol et
    const randevu = await prisma.randevu.findUnique({
      where: { id: randevuId },
      include: {
        ariza: {
          include: {
            durum: true,
            daire: {
              include: {
                blok: {
                  include: {
                    proje: true
                  }
                }
              }
            }
          }
        },
        teknisyenler: {
          include: {
            teknisyen: true
          }
        }
      }
    })

    if (!randevu) {
      return NextResponse.json(
        { error: "Randevu bulunamadı" },
        { status: 404 }
      )
    }

    // Süre hesapla
    let toplamSureDk = null
    if (gercek_baslangic && gercek_bitis) {
      const baslangic = new Date(gercek_baslangic)
      const bitis = new Date(gercek_bitis)
      toplamSureDk = Math.round((bitis.getTime() - baslangic.getTime()) / (1000 * 60))
    }

    // Action'a göre durum ve field değerlerini belirle
    let durum: "TAMAMEN_COZULDU" | "KISMI_COZULDU" | "COZULEMEDI" | "ERTELENDI" | "IPTAL_EDILDI"
    let ariza_cozuldu_mu: boolean
    let sonraki_randevu_gerekli: boolean
    let tamamlanma_orani: number

    switch (action) {
      case AppointmentResultAction.FAULT_RESOLVED:
        durum = "TAMAMEN_COZULDU"
        ariza_cozuldu_mu = true
        sonraki_randevu_gerekli = false
        tamamlanma_orani = 100
        break
      case AppointmentResultAction.CANCELLED:
        durum = "IPTAL_EDILDI"
        ariza_cozuldu_mu = false
        sonraki_randevu_gerekli = false
        tamamlanma_orani = 0
        break
      case AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED:
        durum = "KISMI_COZULDU"
        ariza_cozuldu_mu = false
        sonraki_randevu_gerekli = true
        tamamlanma_orani = 75
        break
      default:
        throw new Error("Geçersiz aksiyon")
    }

    // İş akışı mantığını uygula
    const result = await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      // 1. Randevu sonucunu kaydet
      const sonuc = await tx.randevuSonuc.upsert({
        where: { randevu_id: randevuId },
        update: {
          durum,
          ariza_cozuldu_mu,
          tamamlanma_orani,
          sonraki_randevu_gerekli,
          teknisyen_notlari,
          musteri_memnuniyet,
          musteri_yorumu,
          karsilasilan_zorluklar,
          gercek_baslangic: gercek_baslangic ? new Date(gercek_baslangic) : null,
          gercek_bitis: gercek_bitis ? new Date(gercek_bitis) : null,
          toplam_sure_dk: toplamSureDk,
          resimler: resimler || [],
          iscilik_maliyeti: iscilik_maliyeti || 0,
          guncelleme_tarihi: new Date(),
        },
        create: {
          randevu_id: randevuId,
          durum,
          ariza_cozuldu_mu,
          tamamlanma_orani,
          sonraki_randevu_gerekli,
          teknisyen_notlari,
          musteri_memnuniyet,
          musteri_yorumu,
          karsilasilan_zorluklar,
          gercek_baslangic: gercek_baslangic ? new Date(gercek_baslangic) : null,
          gercek_bitis: gercek_bitis ? new Date(gercek_bitis) : null,
          toplam_sure_dk: toplamSureDk,
          resimler: resimler || [],
          iscilik_maliyeti: iscilik_maliyeti || 0,
        }
      })

      // 2. Arıza altındaki aktif randevuları kontrol et
      const digerAktifRandevular = await tx.randevu.count({
        where: {
          ariza_id: randevu.ariza.id,
          id: { not: randevuId }, // Mevcut randevu hariç
          durum: { notIn: ["TAMAMLANDI", "IPTAL"] }, // Aktif randevular
        }
      })

      // 3. Action'a göre iş kurallarını uygula
      switch (action) {
        case AppointmentResultAction.FAULT_RESOLVED:
          // Arıza çözüldü - randevu tamamlandı
          await tx.randevu.update({
            where: { id: randevuId },
            data: {
              durum: "TAMAMLANDI",
              guncelleme_tarihi: new Date()
            }
          })

          // Eğer başka aktif randevu yoksa ve kullanıcı arızayı da kapatmak istiyorsa
          if (digerAktifRandevular === 0 && closeFaultToo) {
            // Arıza durumunu COZULDU yap
            const cozulduDurumu = await tx.arizaDurum.findFirst({
              where: { ad: "Çözüldü" }
            })

            if (cozulduDurumu) {
              await tx.ariza.update({
                where: { id: randevu.ariza.id },
                data: {
                  durum_id: cozulduDurumu.id,
                  gercek_bitis_tarihi: new Date(),
                  guncelleme_tarihi: new Date()
                }
              })

              // Arıza geçmişine kayıt ekle
              await tx.arizaGecmis.create({
                data: {
                  ariza_id: randevu.ariza.id,
                  durum_id: cozulduDurumu.id,
                  kullanici_id: olusturan_id || "system",
                  yorum: "Randevu tamamlandı, arıza çözüldü olarak kapatıldı"
                }
              })
            }
          } else if (digerAktifRandevular === 0 && !closeFaultToo) {
            // Arıza durumunu BEKLEMEDE yap
            const beklemedeDurumu = await tx.arizaDurum.findFirst({
              where: { ad: "Beklemede" }
            })

            if (beklemedeDurumu) {
              await tx.ariza.update({
                where: { id: randevu.ariza.id },
                data: {
                  durum_id: beklemedeDurumu.id,
                  guncelleme_tarihi: new Date()
                }
              })

              // Arıza geçmişine kayıt ekle
              await tx.arizaGecmis.create({
                data: {
                  ariza_id: randevu.ariza.id,
                  durum_id: beklemedeDurumu.id,
                  kullanici_id: olusturan_id || "system",
                  yorum: "Randevu tamamlandı, arıza beklemede durumuna alındı"
                }
              })
            }
          }
          break

        case AppointmentResultAction.CANCELLED:
          // Randevu iptal edildi
          await tx.randevu.update({
            where: { id: randevuId },
            data: {
              durum: "IPTAL",
              guncelleme_tarihi: new Date()
            }
          })
          break

        case AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED:
          // Yeni randevu gerekli - arıza devam ediyor
          if (randevu.ariza) {
            // Arıza durumunu "Devam Ediyor" olarak güncelle
            const devamDurumu = await tx.arizaDurum.findFirst({
              where: {
                ad: FAULT_STATUS_LABELS[FaultStatus.DEVAM_EDIYOR]
              }
            })

            if (devamDurumu) {
              await tx.ariza.update({
                where: { id: randevu.ariza.id },
                data: {
                  durum_id: devamDurumu.id,
                  guncelleme_tarihi: new Date()
                }
              })
            }

            // Mevcut randevuyu tamamlandı olarak işaretle
            await tx.randevu.update({
              where: { id: randevuId },
              data: {
                durum: "TAMAMLANDI",
                guncelleme_tarihi: new Date()
              }
            })
          }
          break

        default:
          throw new Error("Geçersiz aksiyon")
      }

      // 4. Yanıt için gerekli bilgileri hazırla
      return {
        ...sonuc,
        action,
        shouldCreateNewAppointment: action === AppointmentResultAction.NEXT_APPOINTMENT_REQUIRED,
        shouldRedirectToApartment: action === AppointmentResultAction.FAULT_RESOLVED && digerAktifRandevular === 0 && closeFaultToo,
        shouldRedirectToFault: action === AppointmentResultAction.FAULT_RESOLVED && (digerAktifRandevular > 0 || !closeFaultToo),
        apartmentId: randevu.ariza?.daire?.id,
        faultId: randevu.ariza?.id,
        activeAppointments: digerAktifRandevular,
        faultClosed: action === AppointmentResultAction.FAULT_RESOLVED && digerAktifRandevular === 0 && closeFaultToo,
        // Slug bilgileri
        projeSlug: randevu.ariza?.daire?.blok?.proje?.slug,
        blokSlug: randevu.ariza?.daire?.blok?.slug,
        daireSlug: randevu.ariza?.daire?.slug,
        arizaSlug: randevu.ariza?.slug
      }
    })

    return NextResponse.json(result)

  } catch (error) {
    console.error("Error saving appointment result:", error)
    return NextResponse.json(
      { error: "Randevu sonucu kaydedilirken hata oluştu" },
      { status: 500 }
    )
  }
}
