"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { 
  Save, 
  X, 
  FileText, 
  MapPin, 
  Camera, 
  Settings,
  Loader2,
  AlertTriangle,
  User,
  Plus,
  Minus,
  Calendar
} from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MultiSelect, MultiSelectOption } from "@/components/ui/multi-select"

// Form validation schema
const editFaultSchema = z.object({
  baslik: z.string().min(5, "Başlık en az 5 karakter olmalıdır").max(100, "Başlık en fazla 100 karakter olabilir"),
  aciklama: z.string().min(10, "Açıklama en az 10 karakter olmalıdır").max(1000, "Açıklama en fazla 1000 karakter olabilir"),
  kategoriId: z.string().min(1, "Kategori seçimi gereklidir"),
  durumId: z.string().optional(),
  oncelikId: z.string().optional(),
})

type EditFaultFormData = z.infer<typeof editFaultSchema>

interface Category {
  id: string
  ad: string
  renk: string
}

interface Status {
  id: string
  ad: string
  renk: string
}

interface Priority {
  id: string
  ad: string
  renk: string
  seviye: number
}

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
  aktifRandevuSayisi: number
  uzmanlikAlanlari: {
    id: string
    ad: string
    aciklama: string | null
    renk: string
    seviye: string
  }[]
}

interface AppointmentTechnician {
  teknisyen: Technician
}

interface FaultAppointment {
  id: string
  randevu_tarihi: Date
  durum: string
  aciklama: string | null
  teknisyenler: AppointmentTechnician[]
}

interface FaultData {
  id: string
  numara: string
  baslik: string
  aciklama: string
  ariza_tip_id: string
  durum_id: string
  aciliyet_id: string
  slug: string
  resimler: string[]
  daire: {
    id: string
    numara: string
    slug: string
    blok: {
      id: string
      ad: string
      slug: string
      proje: {
        id: string
        ad: string
        slug: string
      }
    }
  }
  tip: Category
  durum: Status
  aciliyet: Priority
  randevular: FaultAppointment[]
}

interface EditFaultFormProps {
  fault: FaultData
  onCancel: () => void
}

interface NewAppointment {
  randevu_tarihi: string
  teknisyen_ids: string[]
  aciklama: string
}

export function EditFaultForm({ fault, onCancel }: EditFaultFormProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showCancelDialog, setShowCancelDialog] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  
  // Data states
  const [categories, setCategories] = useState<Category[]>([])
  const [statuses, setStatuses] = useState<Status[]>([])
  const [priorities, setPriorities] = useState<Priority[]>([])
  const [technicians, setTechnicians] = useState<Technician[]>([])
  const [dataLoading, setDataLoading] = useState(true)

  // Appointment states
  const [newAppointments, setNewAppointments] = useState<NewAppointment[]>([])
  const [selectedTechnicians, setSelectedTechnicians] = useState<string[]>([])
  const [appointmentDate, setAppointmentDate] = useState("")
  const [appointmentDescription, setAppointmentDescription] = useState("")

  const router = useRouter()

  const form = useForm<EditFaultFormData>({
    resolver: zodResolver(editFaultSchema),
    defaultValues: {
      baslik: fault.baslik,
      aciklama: fault.aciklama,
      kategoriId: fault.ariza_tip_id,
      durumId: fault.durum_id,
      oncelikId: fault.aciliyet_id,
    },
  })

  const { formState: { isDirty } } = form

  // Watch for form changes
  useEffect(() => {
    setHasUnsavedChanges(isDirty || newAppointments.length > 0)
  }, [isDirty, newAppointments])

  // Load form data
  useEffect(() => {
    async function loadFormData() {
      try {
        setDataLoading(true)
        const [categoriesRes, statusesRes, prioritiesRes, techniciansRes] = await Promise.all([
          fetch("/api/categories"),
          fetch("/api/faults/statuses"),
          fetch("/api/faults/priorities"),
          fetch("/api/technicians"),
        ])

        const [categoriesData, statusesData, prioritiesData, techniciansData] = await Promise.all([
          categoriesRes.json(),
          statusesRes.json(),
          prioritiesRes.json(),
          techniciansRes.json(),
        ])

        setCategories(categoriesData.categories || [])
        setStatuses(statusesData.statuses || [])
        setPriorities(prioritiesData.priorities || [])
        setTechnicians(Array.isArray(techniciansData) ? techniciansData : [])
      } catch (error) {
        console.error("Form data yüklenirken hata:", error)
        toast.error("Form verileri yüklenemedi")
      } finally {
        setDataLoading(false)
      }
    }

    loadFormData()
  }, [])

  const onSubmit = async (data: EditFaultFormData) => {
    try {
      setIsSubmitting(true)
      
      // 1. Önce arızayı güncelle
      const response = await fetch(
        `/api/projects/${fault.daire.blok.proje.slug}/blocks/${fault.daire.blok.slug}/apartments/${fault.daire.slug}/arizalar/${fault.slug}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            baslik: data.baslik,
            aciklama: data.aciklama,
            ariza_tip_id: data.kategoriId,
            durum_id: data.durumId,
            aciliyet_id: data.oncelikId,
          }),
        }
      )

      if (!response.ok) {
        throw new Error("Arıza güncellenemedi")
      }

      // 2. Sonra randevuları oluştur
      if (newAppointments.length > 0) {
        const appointmentPromises = newAppointments.map(async (appointment) => {
          const appointmentResponse = await fetch("/api/appointments", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              ariza_id: fault.id,
              randevu_tarihi: appointment.randevu_tarihi,
              durum: "PLANLI",
              aciklama: appointment.aciklama || null,
              teknisyen_ids: appointment.teknisyen_ids,
            }),
          })

          if (!appointmentResponse.ok) {
            throw new Error("Randevu oluşturulamadı")
          }

          return appointmentResponse.json()
        })

        await Promise.all(appointmentPromises)
      }

      toast.success(
        newAppointments.length > 0 
          ? `Arıza başarıyla güncellendi ve ${newAppointments.length} randevu oluşturuldu`
          : "Arıza başarıyla güncellendi"
      )
      setHasUnsavedChanges(false)
      
      // Redirect to fault detail page
      router.push(`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.slug}`)
    } catch (error) {
      console.error("Arıza güncellenirken hata:", error)
      toast.error("Arıza güncellenirken bir hata oluştu")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setShowCancelDialog(true)
    } else {
      onCancel()
    }
  }

  const confirmCancel = () => {
    setShowCancelDialog(false)
    onCancel()
  }

  const addNewAppointment = () => {
    if (selectedTechnicians.length === 0 || !appointmentDate) return

    const newAppointment: NewAppointment = {
      randevu_tarihi: appointmentDate,
      teknisyen_ids: [...selectedTechnicians],
      aciklama: appointmentDescription,
    }

    setNewAppointments(prev => [...prev, newAppointment])
    setSelectedTechnicians([])
    setAppointmentDate("")
    setAppointmentDescription("")
  }

  const removeNewAppointment = (index: number) => {
    setNewAppointments(prev => prev.filter((_, i) => i !== index))
  }

  const getTechnicianById = (id: string) => {
    return technicians.find(tech => tech.id === id)
  }

  if (dataLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Form Header */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div>
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Arıza Düzenle
                </h2>
                <p className="text-sm text-muted-foreground mt-1">
                  #{fault.numara} - Arıza bilgilerini güncelleyin
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                >
                  <X className="mr-2 h-4 w-4" />
                  <span className="hidden sm:inline">İptal</span>
                  <span className="sm:hidden">İptal</span>
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || (!hasUnsavedChanges && newAppointments.length === 0)}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span className="hidden sm:inline">Kaydediliyor...</span>
                      <span className="sm:hidden">Kaydediliyor</span>
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      <span className="hidden sm:inline">
                        {newAppointments.length > 0 ? `Kaydet (${newAppointments.length} randevu)` : "Kaydet"}
                      </span>
                      <span className="sm:hidden">Kaydet</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
                <TabsTrigger value="basic" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <span className="hidden sm:inline">Temel Bilgiler</span>
                  <span className="sm:hidden">Temel</span>
                </TabsTrigger>
                <TabsTrigger value="location" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span className="hidden sm:inline">Konum</span>
                  <span className="sm:hidden">Konum</span>
                </TabsTrigger>
                <TabsTrigger value="photos" className="flex items-center gap-2">
                  <Camera className="h-4 w-4" />
                  <span className="hidden sm:inline">Fotoğraflar</span>
                  <span className="sm:hidden">Foto</span>
                </TabsTrigger>
                <TabsTrigger value="status" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span className="hidden sm:inline">Durum & Atama</span>
                  <span className="sm:hidden">Durum</span>
                </TabsTrigger>
              </TabsList>

              {/* Temel Bilgiler Tab */}
              <TabsContent value="basic" className="space-y-6 mt-6">
                <div className="grid gap-6">
                  <FormField
                    control={form.control}
                    name="baslik"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Arıza Başlığı *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Arıza başlığını girin"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="kategoriId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Kategori *</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Kategori seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                <div className="flex items-center gap-2">
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: category.renk }}
                                  />
                                  {category.ad}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="aciklama"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Detaylı Açıklama *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Arıza hakkında detaylı bilgi verin..."
                            className="min-h-[120px] resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Konum Bilgileri Tab */}
              <TabsContent value="location" className="space-y-6 mt-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <MapPin className="h-5 w-5 text-muted-foreground" />
                    <h3 className="text-lg font-medium">Konum Bilgileri</h3>
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">Proje</label>
                      <div className="p-3 bg-muted rounded-md">
                        <p className="font-medium">{fault.daire.blok.proje.ad}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">Blok</label>
                      <div className="p-3 bg-muted rounded-md">
                        <p className="font-medium">{fault.daire.blok.ad}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-muted-foreground">Daire</label>
                      <div className="p-3 bg-muted rounded-md">
                        <p className="font-medium">{fault.daire.numara}</p>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg bg-blue-50 p-4">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-900">Konum Değişikliği</h4>
                        <p className="text-sm text-blue-700 mt-1">
                          Arızanın konumu değiştirilemez. Yeni bir arıza bildirimi oluşturun.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Fotoğraflar Tab */}
              <TabsContent value="photos" className="space-y-6 mt-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <Camera className="h-5 w-5 text-muted-foreground" />
                    <h3 className="text-lg font-medium">Arıza Fotoğrafları</h3>
                  </div>

                  <div className="space-y-4">
                    {fault.resimler.length > 0 ? (
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {fault.resimler.map((photo, index) => (
                          <div key={index} className="relative group">
                            <img
                              src={photo}
                              alt={`Arıza fotoğrafı ${index + 1}`}
                              className="w-full h-32 object-cover rounded-lg border"
                            />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Camera className="mx-auto h-12 w-12 mb-4 opacity-50" />
                        <p>Bu arıza için fotoğraf bulunmuyor</p>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              {/* Durum & Atama Tab */}
              <TabsContent value="status" className="space-y-6 mt-6">
                <div className="space-y-6">
                  {/* Durum ve Öncelik */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="durumId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Durum</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Durum seçin" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {statuses.map((status) => (
                                <SelectItem key={status.id} value={status.id}>
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-full"
                                      style={{ backgroundColor: status.renk }}
                                    />
                                    {status.ad}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="oncelikId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Öncelik</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Öncelik seçin" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {priorities.map((priority) => (
                                <SelectItem key={priority.id} value={priority.id}>
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-full"
                                      style={{ backgroundColor: priority.renk }}
                                    />
                                    {priority.ad}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  {/* Randevu Ekleme */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-muted-foreground" />
                      <h3 className="text-lg font-medium">Yeni Randevu Ekle</h3>
                    </div>

                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Randevu Tarihi</label>
                          <Input
                            type="datetime-local"
                            value={appointmentDate}
                            onChange={(e) => setAppointmentDate(e.target.value)}
                            min={new Date().toISOString().slice(0, 16)}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Açıklama (Opsiyonel)</label>
                          <Input
                            placeholder="Randevu açıklaması..."
                            value={appointmentDescription}
                            onChange={(e) => setAppointmentDescription(e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Teknisyen Seçimi</label>
                        <MultiSelect
                          options={technicians.map(tech => ({
                            label: `${tech.ad} ${tech.soyad}`,
                            value: tech.id,
                            description: `${tech.aktifRandevuSayisi} aktif randevu`
                          }))}
                          selected={selectedTechnicians}
                          onChange={setSelectedTechnicians}
                          placeholder="Teknisyen seçin..."
                        />
                      </div>

                      {/* Seçilen Teknisyenlerin Detaylı Görünümü */}
                      {selectedTechnicians.length > 0 && (
                        <div className="space-y-3">
                          <label className="text-sm font-medium">Seçilen Teknisyenler</label>
                          <div className="grid gap-3">
                            {selectedTechnicians.map((techId) => {
                              const technician = getTechnicianById(techId)
                              return technician ? (
                                <div key={techId} className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg">
                                  <Avatar className="h-10 w-10">
                                    <AvatarImage src={technician.resim || ""} />
                                    <AvatarFallback>
                                      {technician.ad.charAt(0)}{technician.soyad.charAt(0)}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1">
                                    <p className="font-medium">
                                      {technician.ad} {technician.soyad}
                                    </p>
                                    <p className="text-sm text-muted-foreground">
                                      {technician.email}
                                    </p>
                                    {technician.uzmanlikAlanlari && technician.uzmanlikAlanlari.length > 0 && (
                                      <div className="flex flex-wrap gap-1 mt-2">
                                        {technician.uzmanlikAlanlari.map((expertise) => (
                                          <Badge
                                            key={expertise.id}
                                            variant="outline"
                                            className="text-xs"
                                            style={{ borderColor: expertise.renk, color: expertise.renk }}
                                          >
                                            {expertise.ad} ({expertise.seviye})
                                          </Badge>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                  <div className="text-right">
                                    <p className="text-xs text-muted-foreground">
                                      Aktif Randevu
                                    </p>
                                    <p className="text-sm font-medium">
                                      {technician.aktifRandevuSayisi}
                                    </p>
                                  </div>
                                </div>
                              ) : null
                            })}
                          </div>
                        </div>
                      )}

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <Button
                                type="button"
                                onClick={addNewAppointment}
                                disabled={selectedTechnicians.length === 0 || !appointmentDate || fault.durum.ad !== "Açık"}
                                className="w-full"
                              >
                                <Plus className="mr-2 h-4 w-4" />
                                Randevu Ekle ({selectedTechnicians.length} teknisyen seçildi)
                              </Button>
                            </span>
                          </TooltipTrigger>
                          {fault.durum.ad !== "Açık" && (
                            <TooltipContent>
                              Sadece durumu <b>açık</b> olan arızalara randevu eklenebilir.
                            </TooltipContent>
                          )}
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    {/* Yeni Eklenen Randevular */}
                    {newAppointments.length > 0 && (
                      <>
                        <Separator />
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-5 w-5 text-green-600" />
                            <h4 className="font-medium text-green-600">Eklenecek Randevular</h4>
                          </div>
                          <div className="space-y-3">
                            {newAppointments.map((appointment, index) => (
                              <Card key={index} className="p-4 border-green-200 bg-green-50">
                                <div className="flex items-start justify-between">
                                  <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                                        {new Date(appointment.randevu_tarihi).toLocaleDateString("tr-TR", {
                                          day: "2-digit",
                                          month: "2-digit",
                                          year: "numeric",
                                          hour: "2-digit",
                                          minute: "2-digit",
                                        })}
                                      </Badge>
                                      <Badge variant="outline" className="border-green-300">PLANLI</Badge>
                                    </div>
                                    {appointment.aciklama && (
                                      <p className="text-sm text-muted-foreground">
                                        {appointment.aciklama}
                                      </p>
                                    )}
                                    <div className="flex flex-wrap gap-2">
                                      {appointment.teknisyen_ids.map((techId) => {
                                        const tech = getTechnicianById(techId)
                                        return tech ? (
                                          <div key={techId} className="flex items-center gap-2 bg-white rounded-md px-2 py-1 border border-green-200">
                                            <Avatar className="h-6 w-6">
                                              <AvatarImage src={tech.resim || ""} />
                                              <AvatarFallback className="text-xs">
                                                {tech.ad.charAt(0)}{tech.soyad.charAt(0)}
                                              </AvatarFallback>
                                            </Avatar>
                                            <span className="text-sm">
                                              {tech.ad} {tech.soyad}
                                            </span>
                                          </div>
                                        ) : null
                                      })}
                                    </div>
                                  </div>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => removeNewAppointment(index)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Minus className="h-4 w-4" />
                                  </Button>
                                </div>
                              </Card>
                            ))}
                          </div>
                        </div>
                      </>
                    )}

                    <Separator />

                    <div className="space-y-3">
                      <h4 className="font-medium">Mevcut Durum</h4>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary" className="flex items-center gap-1">
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: fault.durum.renk }}
                          />
                          {fault.durum.ad}
                        </Badge>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: fault.aciliyet.renk }}
                          />
                          {fault.aciliyet.ad}
                        </Badge>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: fault.tip.renk }}
                          />
                          {fault.tip.ad}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </form>
      </Form>

      {/* Cancel Confirmation Dialog */}
      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Değişiklikleri kaydetmeden çık?</AlertDialogTitle>
            <AlertDialogDescription>
              Kaydedilmemiş değişiklikleriniz kaybolacak. Devam etmek istediğinizden emin misiniz?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={confirmCancel}>
              Evet, Çık
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
} 