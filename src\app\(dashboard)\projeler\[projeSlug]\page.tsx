import { notFound } from "next/navigation";
import Link from "next/link";
import { ChevronRight, Building2, Home, Users } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { headers } from 'next/headers';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import Image from "next/image";
import BlocksTable from "@/components/blocks/blocks-table";
import { BreadcrumbWithLoading } from "@/components/ui/breadcrumb-with-loading";
import { ViewAllBlocksButton } from "@/components/ui/view-all-blocks-button";
import { getInternalApiBaseUrl } from "@/lib/utils";

async function getProjectBySlug(slug: string) {
  const baseUrl = await getInternalApiBaseUrl();
  const res = await fetch(`${baseUrl}/api/projects/${slug}`, {
    cache: 'no-store'
  });
  if (!res.ok) return null;
  return res.json();
}

async function getBlocksByProjectSlug(projectSlug: string) {
  const baseUrl = await getInternalApiBaseUrl();
  const res = await fetch(`${baseUrl}/api/projects/${projectSlug}/blocks`, {
    cache: 'no-store'
  });
  if (!res.ok) return [];
  const data = await res.json();
  return data.blocks || [];
}

export default async function ProjectDetailPage({ params }: { params: Promise<{ projeSlug: string }> }) {
  const resolvedParams = await params;
  const project = await getProjectBySlug(resolvedParams.projeSlug);
  if (!project) return notFound();

  const blocks = await getBlocksByProjectSlug(project.slug);

  const breadcrumbItems = [
    { label: "Ana Sayfa", href: "/dashboard" },
    { label: "Projeler", href: "/projeler" },
    { label: project.ad, isActive: true }
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="mb-4">
        <BreadcrumbWithLoading items={breadcrumbItems} />
      </div>

      {/* Project Header */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{project.ad}</h1>
          {project.aciklama && (
            <p className="text-muted-foreground mt-2">{project.aciklama}</p>
          )}
        </div>
        {project.project_image_url && (
          <div className="w-24 h-24 rounded-lg overflow-hidden bg-muted">
            <Image
              src="/media/projeler/1751314918871-hakkimizda.webp"
              alt="Proje görseli"
              width={600}
              height={400}
              quality={80}
              placeholder="blur"
              blurDataURL="/media/projeler/1751314918871-hakkimizda.jpg"
              style={{ objectFit: "cover", borderRadius: 12, width: "100%", height: "auto" }}
              sizes="(max-width: 768px) 100vw, 600px"
            />
          </div>
        )}
      </div>

      {/* Project Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blok Sayısı</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{project.blok_sayisi || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daire Sayısı</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{project.daire_sayisi || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Başlangıç Tarihi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm font-medium">
              {project.baslangic_tarihi ? new Date(project.baslangic_tarihi).toLocaleDateString('tr-TR') : '-'}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bitiş Tarihi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm font-medium">
              {project.bitis_tarihi ? new Date(project.bitis_tarihi).toLocaleDateString('tr-TR') : '-'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Details */}
      {project.adres && (
        <Card>
          <CardHeader>
            <CardTitle>Proje Adresi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{project.adres}</p>
          </CardContent>
        </Card>
      )}

      {/* Blocks Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold tracking-tight">Bloklar</h2>
          <ViewAllBlocksButton projectSlug={project.slug} />
        </div>

        {blocks.length === 0 ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center">
                <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Bu projede henüz blok bulunmuyor</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <BlocksTable blocks={blocks} projectSlug={project.slug} />
          </Card>
        )}
      </div>
    </div>
  );
} 