const natural = require('natural');

class FaultClassifier {
  constructor() {
    this.classifier = new natural.BayesClassifier();
    this.categories = new Map();
    this.confidenceThreshold = 0.7;
    this.isTrained = false;
    this.trainingStats = {
      totalSamples: 0,
      categories: {},
      accuracy: 0,
      trainingTime: 0
    };
  }

  /**
   * Modeli eğit
   * @param {Array} trainingData - Eğitim verileri
   */
  train(trainingData) {
    console.log('🤖 Model eğitimi başlıyor...');
    const startTime = Date.now();
    
    if (!Array.isArray(trainingData) || trainingData.length === 0) {
      throw new Error('Training data geçersiz veya boş');
    }

    // Kategori mapping'i oluştur
    trainingData.forEach(item => {
      this.categories.set(item.kategori, item.kategori_id);
    });

    // Her örnek için modeli eğit
    trainingData.forEach((item, index) => {
      const text = `${item.baslik} ${item.aciklama}`.toLowerCase();
      this.classifier.addDocument(text, item.kategori);
      
      // İlerleme göster
      if ((index + 1) % 50 === 0) {
        console.log(`  📚 ${index + 1}/${trainingData.length} örnek eğitildi`);
      }
    });

    // Modeli eğit
    this.classifier.train();
    
    // Eğitim istatistiklerini kaydet
    this.trainingStats = {
      totalSamples: trainingData.length,
      categories: this.getCategoryDistribution(trainingData),
      trainingTime: Date.now() - startTime,
      modelSize: this.classifier.docs.length
    };

    this.isTrained = true;
    
    console.log(`✅ Model eğitimi tamamlandı!`);
    console.log(`   📊 Toplam örnek: ${this.trainingStats.totalSamples}`);
    console.log(`   ⏱️  Eğitim süresi: ${this.trainingStats.trainingTime}ms`);
    console.log(`   🏷️  Kategori sayısı: ${this.categories.size}`);
  }

  /**
   * Kategori tahmini yap
   * @param {string} title - Arıza başlığı
   * @param {string} description - Arıza açıklaması
   * @returns {Object} Tahmin sonucu
   */
  predict(title, description) {
    if (!this.isTrained) {
      throw new Error('Model henüz eğitilmemiş');
    }

    if (!title || !description) {
      return {
        success: false,
        error: 'Başlık ve açıklama gereklidir',
        confidence: 0
      };
    }

    const text = `${title} ${description}`.toLowerCase();
    const classifications = this.classifier.getClassifications(text);
    
    if (!classifications || classifications.length === 0) {
      return {
        success: false,
        error: 'Tahmin yapılamadı',
        confidence: 0
      };
    }

    const topResult = classifications[0];
    const confidence = topResult.value;

    // Güven eşiğini kontrol et
    if (confidence < this.confidenceThreshold) {
      return {
        success: false,
        message: `Güven seviyesi düşük (${(confidence * 100).toFixed(1)}%)`,
        confidence: confidence,
        alternatives: classifications.slice(0, 3).map(c => ({
          category: c.label,
          category_id: this.categories.get(c.label),
          confidence: c.value
        }))
      };
    }

    return {
      success: true,
      category: topResult.label,
      category_id: this.categories.get(topResult.label),
      confidence: confidence,
      alternatives: classifications.slice(1, 3).map(c => ({
        category: c.label,
        category_id: this.categories.get(c.label),
        confidence: c.value
      })),
      all_classifications: classifications.map(c => ({
        category: c.label,
        category_id: this.categories.get(c.label),
        confidence: c.value
      }))
    };
  }

  /**
   * Model performansını test et
   * @param {Array} testData - Test verileri
   * @returns {Object} Test sonuçları
   */
  test(testData) {
    if (!this.isTrained) {
      throw new Error('Model henüz eğitilmemiş');
    }

    console.log('🧪 Model testi başlıyor...');
    
    let correct = 0;
    let total = 0;
    const categoryResults = {};

    testData.forEach((item, index) => {
      const prediction = this.predict(item.baslik, item.aciklama);
      
      if (prediction.success && prediction.category === item.kategori) {
        correct++;
      }
      
      total++;

      // Kategori bazında sonuçları kaydet
      if (!categoryResults[item.kategori]) {
        categoryResults[item.kategori] = { correct: 0, total: 0 };
      }
      
      if (prediction.success && prediction.category === item.kategori) {
        categoryResults[item.kategori].correct++;
      }
      categoryResults[item.kategori].total++;

      // İlerleme göster
      if ((index + 1) % 20 === 0) {
        console.log(`  🧪 ${index + 1}/${testData.length} test tamamlandı`);
      }
    });

    const accuracy = (correct / total) * 100;
    this.trainingStats.accuracy = accuracy;

    console.log(`✅ Test tamamlandı!`);
    console.log(`   📊 Doğruluk: ${accuracy.toFixed(2)}%`);
    console.log(`   ✅ Doğru: ${correct}/${total}`);

    return {
      accuracy: accuracy,
      correct: correct,
      total: total,
      categoryResults: categoryResults
    };
  }

  /**
   * Kategori dağılımını hesapla
   * @param {Array} data - Veri seti
   * @returns {Object} Kategori dağılımı
   */
  getCategoryDistribution(data) {
    const distribution = {};
    data.forEach(item => {
      distribution[item.kategori] = (distribution[item.kategori] || 0) + 1;
    });
    return distribution;
  }

  /**
   * Model durumunu al
   * @returns {Object} Model durumu
   */
  getStatus() {
    return {
      isTrained: this.isTrained,
      totalSamples: this.trainingStats.totalSamples,
      categories: this.trainingStats.categories,
      accuracy: this.trainingStats.accuracy,
      trainingTime: this.trainingStats.trainingTime,
      modelSize: this.trainingStats.modelSize,
      confidenceThreshold: this.confidenceThreshold
    };
  }

  /**
   * Güven eşiğini ayarla
   * @param {number} threshold - Yeni eşik değeri (0-1 arası)
   */
  setConfidenceThreshold(threshold) {
    if (threshold < 0 || threshold > 1) {
      throw new Error('Güven eşiği 0-1 arasında olmalıdır');
    }
    this.confidenceThreshold = threshold;
    console.log(`🎯 Güven eşiği ${threshold} olarak ayarlandı`);
  }

  /**
   * Modeli sıfırla
   */
  reset() {
    this.classifier = new natural.BayesClassifier();
    this.categories.clear();
    this.isTrained = false;
    this.trainingStats = {
      totalSamples: 0,
      categories: {},
      accuracy: 0,
      trainingTime: 0
    };
    console.log('🔄 Model sıfırlandı');
  }
}

module.exports = FaultClassifier; 