"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "@/hooks/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"


const blockSchema = z.object({
  ad: z.string().min(1, "Blok adı gereklidir").max(100, "Blok adı çok uzun"),
  proje_id: z.string().min(1, "Proje seçimi gereklidir"),
  aciklama: z.string().optional().or(z.literal("")),
})

type BlockFormData = z.infer<typeof blockSchema>

interface Project {
  id: string
  ad: string
}

interface Block {
  id: string
  ad: string
  aciklama?: string
  proje_id: string
}

interface BlockDialogProps {
  open: boolean
  onClose: () => void
  block?: Block | null
  projects: Project[]
  preselectedProjectId?: string | null
}

export function BlockDialog({ open, onClose, block, projects, preselectedProjectId }: BlockDialogProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!block

  const form = useForm<BlockFormData>({
    resolver: zodResolver(blockSchema),
    defaultValues: {
      ad: "",
      proje_id: "",
      aciklama: "",
    },
  })
  useEffect(() => {
    if (block) {
      form.reset({
        ad: block.ad,
        proje_id: block.proje_id,
        aciklama: block.aciklama || "",
      })
    } else {
      form.reset({
        ad: "",
        proje_id: preselectedProjectId || "",
        aciklama: "",
      })
    }
  }, [block, form, preselectedProjectId])

  // Ensure proje_id is set when preselectedProjectId changes
  useEffect(() => {
    if (preselectedProjectId && !block) {
      form.setValue("proje_id", preselectedProjectId)
    }
  }, [preselectedProjectId, form, block])

  const handleSubmit = async (data: BlockFormData) => {
    try {
      setLoading(true)

      const payload: any = {
        ad: data.ad,
        proje_id: data.proje_id,
      }

      // Only include aciklama if it has content
      if (data.aciklama && data.aciklama.trim() !== "") {
        payload.aciklama = data.aciklama.trim()
      }


      const url = isEditing ? `/api/blocks/${block.id}` : "/api/blocks"
      const method = isEditing ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        console.error("API Error Response:", error)
        console.error("API Error Details:", JSON.stringify(error, null, 2))
        if (error.errors) {
          console.error("Validation Errors:", error.errors)
        }
        throw new Error(error.message || "İşlem başarısız")
      }


      // Başarı bildirimi
      toast.success(
        isEditing ? "Blok başarıyla güncellendi!" : "Yeni blok başarıyla oluşturuldu!",
        {
          title: "Başarılı",
          duration: 4000
        }
      )

      onClose()
    } catch (error) {
      console.error("Error saving block:", error)
      
      // Hata bildirimi  
      toast.error(
        error instanceof Error ? error.message : "Beklenmeyen bir hata oluştu",
        {
          title: "Hata",
          duration: 6000
        }
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Blogu Düzenle" : "Yeni Blok Ekle"}
          </DialogTitle>
          <DialogDescription>
            {isEditing ? "Blok bilgilerini güncelleyin." : "Yeni bir blok oluşturun ve projeye ekleyin."}
          </DialogDescription>
        </DialogHeader>        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="proje_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Proje *</FormLabel>
                  {preselectedProjectId ? (
                    <>
                      <div className="py-2 px-3 bg-muted rounded text-sm font-medium">
                        {projects.find(p => p.id === preselectedProjectId)?.ad || "Proje"}
                      </div>
                      <FormControl>
                        <input type="hidden" {...field} value={preselectedProjectId} />
                      </FormControl>
                    </>
                  ) : (
                    <FormControl>
                      <select 
                        {...field}
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="" disabled>Proje seçin...</option>
                        {projects.map((project) => (
                          <option key={project.id} value={project.id}>
                            {project.ad}
                          </option>
                        ))}
                      </select>
                    </FormControl>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="ad"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Blok Adı *</FormLabel>
                  <FormControl>
                    <Input placeholder="Örn: A Blok, B Blok" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="aciklama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Blok açıklaması (opsiyonel)"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                İptal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Kaydediliyor..." : isEditing ? "Güncelle" : "Kaydet"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
