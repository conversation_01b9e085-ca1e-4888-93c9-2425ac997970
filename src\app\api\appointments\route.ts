import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AppointmentStatus } from "@/lib/enums"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      ariza_id, 
      randevu_tarihi, 
      durum, 
      aciklama, 
      teknisyen_ids,
      onceki_randevu_id,  // Devam randevusu için önceki randevu ID'si
      devam_randevusu_mu = false  // Bu randevu bir devam randevusu mu?
    } = body

    // Required fields validation
    if (!ariza_id || !randevu_tarihi || !teknisyen_ids || teknisyen_ids.length === 0) {
      return NextResponse.json(
        { error: "Arıza, randevu tarihi ve en az bir teknisyen seçimi gerekli" },
        { status: 400 }
      )
    }

    // Create appointment
    const appointment = await prisma.randevu.create({
      data: {
        ariza_id,
        randevu_tarihi: new Date(randevu_tarihi),
        durum: durum || "PLANLI",
        aciklama,
        ...(onceki_randevu_id && { onceki_randevu_id }),
        ...(devam_randevusu_mu !== undefined && { devam_randevusu_mu }),
      },
      include: {
        ariza: {
          include: {
            daire: {
              include: {
                blok: {
                  include: {
                    proje: true
                  }
                }
              }
            }
          }
        }
      }
    })

    // Assign technicians
    if (teknisyen_ids && teknisyen_ids.length > 0) {
      await Promise.all(
        teknisyen_ids.map((technicianId: string) =>
          prisma.randevuTeknisyen.create({
            data: {
              randevu_id: appointment.id,
              teknisyen_id: technicianId,
            },
          })
        )
      )
    }

    // Devam randevusu ise önceki randevuyu otomatik sonlandır
    let previousAppointmentClosed = false
    if (devam_randevusu_mu && onceki_randevu_id) {
      await prisma.randevu.update({
        where: { id: onceki_randevu_id },
        data: { 
          durum: "TAMAMLANDI",
          guncelleme_tarihi: new Date()
        }
      })
      
      previousAppointmentClosed = true
      console.log(`✅ Önceki randevu (${onceki_randevu_id}) otomatik olarak sonlandırıldı`)
    }

    return NextResponse.json({
      ...appointment,
      _meta: {
        devam_randevusu_mu,
        onceki_randevu_kapatildi: previousAppointmentClosed,
        message: previousAppointmentClosed 
          ? "Devam randevusu oluşturuldu ve önceki randevu otomatik sonlandırıldı"
          : "Randevu başarıyla oluşturuldu"
      }
    })
  } catch (error) {
    console.error("Error creating appointment:", error)
    return NextResponse.json(
      { error: "Randevu oluşturulurken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const faultId = searchParams.get("fault_id")

    let whereClause: any = {
      silindi_mi: false // Silinmiş randevuları gösterme
    }
    if (faultId) {
      whereClause.ariza_id = faultId
    }

    const appointments = await prisma.randevu.findMany({
      where: whereClause,
      include: {
        ariza: {
          include: {
            daire: {
              include: {
                blok: {
                  include: {
                    proje: true
                  }
                }
              }
            }
          }
        },
        teknisyenler: {
          include: {
            teknisyen: true
          }
        }
      },
      orderBy: {
        randevu_tarihi: 'desc'
      }
    })

    return NextResponse.json(appointments)
  } catch (error) {
    console.error("Error fetching appointments:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
