import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projeSlug: string; blokSlug: string }> }
) {
  try {
    const resolvedParams = await params
    const { projeSlug, blokSlug } = resolvedParams

    // Önce projeyi bul
    const project = await prisma.proje.findFirst({
      where: {
        slug: projeSlug,
        silindi_mi: false,
      },
    })

    if (!project) {
      return NextResponse.json(
        { error: "Proje bulunamadı" },
        { status: 404 }
      )
    }

    // Sonra bloku bul
    const block = await prisma.blok.findFirst({
      where: {
        slug: blokSlug,
        proje_id: project.id,
        silindi_mi: false,
      },
      include: {
        proje: {
          select: {
            id: true,
            ad: true,
            slug: true,
          },
        },
        _count: {
          select: {
            daireler: {
              where: { silindi_mi: false },
            },
          },
        },
      },
    })

    if (!block) {
      return NextResponse.json(
        { error: "Blok bulunamadı" },
        { status: 404 }
      )
    }

    // Arıza sayısı: bu bloğa bağlı tüm dairelerin arızalarını say
    const ariza_sayisi = await prisma.ariza.count({
      where: {
        daire: {
          blok_id: block.id,
          silindi_mi: false,
        },
        silindi_mi: false,
      },
    });

    return NextResponse.json({
      ...block,
      ariza_sayisi,
    })
  } catch (error) {
    console.error("Blok detayı getirilirken hata:", error)
    return NextResponse.json(
      { error: "Blok detayı getirilemedi" },
      { status: 500 }
    )
  }
} 