# Node.js 18 Alpine image kullan
FROM node:18-alpine

# <PERSON><PERSON><PERSON><PERSON><PERSON> dizinini belirle
WORKDIR /app

# Package.json ve package-lock.json dosyalarını kopyala
COPY package*.json ./

# Bağımlılıkları yükle
RUN npm ci --only=production

# Uygulama dosyalarını kopyala
COPY . .

# Port 3050'yi expose et
EXPOSE 3050

# Health check ekle
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3050/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Uygulamayı başlat
CMD ["node", "api/http-server.js"] 