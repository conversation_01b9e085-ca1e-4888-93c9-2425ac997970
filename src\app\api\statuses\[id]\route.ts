import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

// Validation schema
const statusUpdateSchema = z.object({
  ad: z.string().min(1, "Ad alanı gereklidir"),
  sira: z.number().min(1, "Sıra 1'den büyük olmalıdır"),
  renk: z.string().regex(/^#[0-9A-F]{6}$/i, "Geçerli bir hex renk kodu giriniz"),
  aciklama: z.string().optional(),
})

// GET - Get single status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const status = await prisma.arizaDurum.findFirst({
      where: {
        id: id,
        silindi_mi: false,
      },
    })

    if (!status) {
      return NextResponse.json(
        { message: "<PERSON>rıza durumu bulunamadı" },
        { status: 404 }
      )
    }

    return NextResponse.json(status)
  } catch (error) {
    console.error("Error fetching status:", error)
    return NextResponse.json(
      { message: "Arıza durumu yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}

// PUT - Update status
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    
    // Validate input
    const validatedData = statusUpdateSchema.parse(body)
    
    // Check if status exists
    const existingStatus = await prisma.arizaDurum.findFirst({
      where: {
        id: id,
        silindi_mi: false,
      },
    })

    if (!existingStatus) {
      return NextResponse.json(
        { message: "Arıza durumu bulunamadı" },
        { status: 404 }
      )
    }

    // Check if order is used by another status
    if (validatedData.sira !== existingStatus.sira) {
      const existingOrder = await prisma.arizaDurum.findFirst({
        where: {
          sira: validatedData.sira,
          silindi_mi: false,
          id: { not: id },
        },
      })

      if (existingOrder) {
        return NextResponse.json(
          { message: `${validatedData.sira} sıra numarası zaten kullanılıyor` },
          { status: 400 }
        )
      }
    }

    // Check if name is used by another status
    if (validatedData.ad !== existingStatus.ad) {
      const existingName = await prisma.arizaDurum.findFirst({
        where: {
          ad: validatedData.ad,
          silindi_mi: false,
          id: { not: id },
        },
      })

      if (existingName) {
        return NextResponse.json(
          { message: `"${validatedData.ad}" adı zaten kullanılıyor` },
          { status: 400 }
        )
      }
    }

    // Update status
    const status = await prisma.arizaDurum.update({
      where: {
        id: id,
      },
      data: {
        ...validatedData,
        guncelleme_tarihi: new Date(),
      },
    })

    return NextResponse.json(status)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Geçersiz veri", errors: error.errors },
        { status: 400 }
      )
    }
    
    console.error("Error updating status:", error)
    return NextResponse.json(
      { message: "Arıza durumu güncellenirken hata oluştu" },
      { status: 500 }
    )
  }
}

// DELETE - Delete status
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Check if status exists
    const existingStatus = await prisma.arizaDurum.findFirst({
      where: {
        id: id,
        silindi_mi: false,
      },
    })

    if (!existingStatus) {
      return NextResponse.json(
        { message: "Arıza durumu bulunamadı" },
        { status: 404 }
      )
    }

    // Check if status is being used by faults
    const usageCount = await prisma.ariza.count({
      where: {
        durum_id: id,
        silindi_mi: false,
      },
    })

    if (usageCount > 0) {
      return NextResponse.json(
        { 
          message: `Bu arıza durumu ${usageCount} arızada kullanılıyor. Önce bu arızaların durumunu değiştirin.` 
        },
        { status: 400 }
      )
    }

    // Check if status is being used by history records
    const historyCount = await prisma.arizaGecmis.count({
      where: {
        durum_id: id,
      },
    })

    if (historyCount > 0) {
      return NextResponse.json(
        { 
          message: `Bu arıza durumu ${historyCount} geçmiş kaydında kullanılıyor. Bu durum silinemez.` 
        },
        { status: 400 }
      )
    }

    // Soft delete
    await prisma.arizaDurum.update({
      where: {
        id: id,
      },
      data: {
        silindi_mi: true,
        silinme_tarihi: new Date(),
      },
    })

    return NextResponse.json({ message: "Arıza durumu başarıyla silindi" })
  } catch (error) {
    console.error("Error deleting status:", error)
    return NextResponse.json(
      { message: "Arıza durumu silinirken hata oluştu" },
      { status: 500 }
    )
  }
} 