import { useEffect, useState } from "react"

interface Technician {
  id: string
  ad: string
  soyad: string
  uzmanlikAlanlari: { id: string; ad: string | null; aciklama: string | null; renk: string; seviye: string }[]
}

interface Step3Props {
  data: any
  onBack: () => void
  onSuccess?: () => void
}

export default function Step3SummaryConfirm({ data, onBack, onSuccess }: Step3Props) {
  const [technicians, setTechnicians] = useState<Technician[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // Seçilen teknisyenlerin detaylarını getir
  useEffect(() => {
    if (!data.technicians || data.technicians.length === 0) return
    fetch("/api/technicians")
      .then(res => res.json())
      .then(list => {
        setTechnicians(list.filter((t: Technician) => data.technicians.includes(t.id)))
      })
  }, [data.technicians])

  const handleCreate = async () => {
    setLoading(true)
    setError("")
    setSuccess("")
    try {
      if (!data.ariza_id) {
        setError("Arıza ID'si eksik!")
        setLoading(false)
        return
      }
      const res = await fetch("/api/appointments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ariza_id: data.ariza_id,
          randevu_tarihi: data.date + "T" + data.time,
          durum: data.status,
          aciklama: data.description,
          teknisyen_ids: data.technicians
        })
      })
      if (!res.ok) {
        const err = await res.json()
        setError(err.error || "Randevu oluşturulamadı")
      } else {
        setSuccess("Randevu başarıyla oluşturuldu!")
        if (onSuccess) onSuccess()
      }
    } catch (e) {
      setError("Beklenmeyen bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center gap-4 mb-4">
        <button type="button" className="bg-gray-100 dark:bg-zinc-800 text-gray-600 dark:text-zinc-300 px-4 py-2 rounded-lg border border-gray-200 dark:border-zinc-700 hover:bg-gray-200 dark:hover:bg-zinc-700 transition" onClick={onBack}>Geri</button>
        <h2 className="text-xl font-bold">Randevu Özeti</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-xl border border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
          <div className="font-medium mb-1 text-gray-700 dark:text-zinc-200">Tarih</div>
          <div className="text-base font-semibold">{data.date}</div>
        </div>
        <div className="rounded-xl border border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
          <div className="font-medium mb-1 text-gray-700 dark:text-zinc-200">Saat</div>
          <div className="text-base font-semibold">{data.time}</div>
        </div>
        <div className="rounded-xl border border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
          <div className="font-medium mb-1 text-gray-700 dark:text-zinc-200">Durum</div>
          <div className="text-base font-semibold">{data.status}</div>
        </div>
        <div className="md:col-span-3 rounded-xl border border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
          <div className="font-medium mb-1 text-gray-700 dark:text-zinc-200">Açıklama</div>
          <div className="min-h-[60px] text-gray-800 dark:text-zinc-100">{data.description || <span className="text-muted-foreground">(Yok)</span>}</div>
        </div>
        <div className="md:col-span-3 rounded-xl border border-gray-200 dark:border-zinc-700 bg-white dark:bg-zinc-900 p-4 shadow-sm">
          <div className="font-medium mb-1 text-gray-700 dark:text-zinc-200">Seçilen Teknisyenler</div>
          <div className="flex flex-wrap gap-2 mt-1">
            {technicians.length === 0 ? <span className="text-muted-foreground">Teknisyen seçilmedi</span> : technicians.map((t) => (
              <span key={t.id} className="px-3 py-1 rounded-full bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700 font-medium text-sm">
                {t.ad} {t.soyad} ({t.uzmanlikAlanlari.map(u=>u.ad).join(", ")})
              </span>
            ))}
          </div>
        </div>
      </div>
      {error && <div className="text-red-600 mt-2">{error}</div>}
      {success && <div className="text-green-600 mt-2">{success}</div>}
      <div className="flex justify-end mt-6">
        <button type="button" className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold px-10 py-2 rounded-lg shadow-lg transition disabled:opacity-50" disabled={loading} onClick={handleCreate}>
          {loading ? "Oluşturuluyor..." : "Randevuyu Oluştur"}
        </button>
      </div>
    </div>
  )
} 