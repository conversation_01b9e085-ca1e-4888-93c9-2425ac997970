"use client"

import Link from "next/link"
import { 
  Settings, 
  Tag, 
  AlertTriangle, 
  CheckCircle, 
  ArrowRight,
  Wrench,
  Target
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb } from "@/components/layout/breadcrumb"

const settingsModules = [
  {
    title: "Kategoriler",
    description: "Arıza kategori türlerini yönetin",
    href: "/ayarlar/kategoriler",
    icon: Tag,
    color: "bg-blue-500",
    stats: "Mevcut kategorileri görüntüleyin ve düzenleyin"
  },
  {
    title: "Öncelik Seviyeleri",
    description: "Arıza öncelik seviyelerini tanımlayın",
    href: "/ayarlar/oncelik-seviyeleri",
    icon: AlertTriangle,
    color: "bg-orange-500",
    stats: "Aciliyet seviyelerini ayarlayın (1-10)"
  },
  {
    title: "Durum Tanımları",
    description: "Arıza durumlarını ve iş akışını yönetin",
    href: "/ayarlar/durum-tanimlari",
    icon: CheckCircle,
    color: "bg-green-500",
    stats: "İş akışı durumlarını sıralayın"
  }
]

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <Breadcrumb />
      
      <div className="flex items-center gap-3">
        <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-blue-100">
          <Settings className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Sistem Ayarları
          </h2>
          <p className="text-muted-foreground">
            Bakım onarım sistemi ayarlarını yönetin
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {settingsModules.map((module) => (
          <Card key={module.href} className="relative overflow-hidden hover:shadow-lg transition-shadow group">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg ${module.color}/10`}>
                  <module.icon className={`h-5 w-5 text-white`} style={{ filter: 'drop-shadow(0 0 0 ' + module.color.replace('bg-', '') + ')' }} />
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground group-hover:translate-x-1 transition-all" />
              </div>
              <div className="space-y-1">
                <CardTitle className="text-lg">{module.title}</CardTitle>
                <CardDescription className="text-sm">
                  {module.description}
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                <p className="text-xs text-muted-foreground">
                  {module.stats}
                </p>
                <Button asChild className="w-full" variant="outline">
                  <Link href={module.href}>
                    Yönet
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            Diğer Ayarlar
          </CardTitle>
          <CardDescription>
            Gelecekte eklenecek ayar modülleri
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3 p-3 rounded-lg border border-dashed">
              <Target className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Kullanıcı Rolleri</p>
                <p className="text-xs text-muted-foreground">Yakında eklenecek</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 rounded-lg border border-dashed">
              <Target className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sistem Bildirimleri</p>
                <p className="text-xs text-muted-foreground">Yakında eklenecek</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 rounded-lg border border-dashed">
              <Target className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Email Şablonları</p>
                <p className="text-xs text-muted-foreground">Yakında eklenecek</p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 rounded-lg border border-dashed">
              <Target className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rapor Ayarları</p>
                <p className="text-xs text-muted-foreground">Yakında eklenecek</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 