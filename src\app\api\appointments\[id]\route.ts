import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const appointmentId = resolvedParams.id

    const appointment = await prisma.randevu.findFirst({
      where: { 
        id: appointmentId,
        silindi_mi: false // Silinmiş randevuları gösterme
      },
      include: {
        ariza: {
          select: {
            id: true,
            baslik: true,
            slug: true,
            daire: {
              select: {
                numara: true,
                slug: true,
                blok: {
                  select: {
                    ad: true,
                    slug: true,
                    proje: {
                      select: {
                        ad: true,
                        slug: true
                      }
                    }
                  }
                }
              }
            }
          }
        },
        teknisyenler: {
          include: {
            teknisyen: {
              select: {
                id: true,
                ad: true,
                soyad: true,
                email: true,
                telefon: true,
                resim: true,
                uzmanlik_alanlari: {
                  select: {
                    id: true,
                    seviye: true,
                    uzmanlik_alani: {
                      select: {
                        id: true,
                        ad: true,
                        aciklama: true,
                        renk: true,
                      }
                    }
                  },
                  orderBy: {
                    seviye: "desc", // En yüksek seviyeden başla
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!appointment) {
      return NextResponse.json(
        { error: "Appointment not found" },
        { status: 404 }
      )
    }

    // Transform the data to match frontend expectations
    const transformedAppointment = {
      ...appointment,
      teknisyenler: appointment.teknisyenler.map(at => ({
        ...at,
        teknisyen: {
          ...at.teknisyen,
          uzmanlikAlanlari: at.teknisyen.uzmanlik_alanlari.map(ua => ({
            id: ua.uzmanlik_alani.id,
            ad: ua.uzmanlik_alani.ad,
            aciklama: ua.uzmanlik_alani.aciklama,
            renk: ua.uzmanlik_alani.renk,
            seviye: ua.seviye
          }))
        }
      }))
    }

    return NextResponse.json(transformedAppointment)
  } catch (error) {
    console.error("Error fetching appointment:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const appointmentId = resolvedParams.id
    const body = await request.json()
    const { randevu_tarihi, durum, aciklama, teknisyen_ids } = body

    // Verify appointment exists and not deleted
    const existingAppointment = await prisma.randevu.findFirst({
      where: { 
        id: appointmentId,
        silindi_mi: false // Silinmiş randevuları güncelleme
      }
    })

    if (!existingAppointment) {
      return NextResponse.json(
        { error: "Appointment not found" },
        { status: 404 }
      )
    }

    // Update appointment
    const updatedAppointment = await prisma.randevu.update({
      where: { id: appointmentId },
      data: {
        randevu_tarihi: randevu_tarihi ? new Date(randevu_tarihi) : undefined,
        durum: durum || undefined,
        aciklama: aciklama !== undefined ? aciklama : undefined,
        guncelleme_tarihi: new Date(),
        // If teknisyen_ids is provided, update technician assignments
        ...(teknisyen_ids && {
          teknisyenler: {
            deleteMany: {}, // Remove all existing assignments
            create: teknisyen_ids.map((teknisyen_id: string) => ({
              teknisyen_id
            }))
          }
        })
      },
      include: {
        teknisyenler: {
          include: {
            teknisyen: true
          }
        }
      }
    })

    return NextResponse.json(updatedAppointment)
  } catch (error) {
    console.error("Error updating appointment:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const appointmentId = resolvedParams.id

    // Verify appointment exists and not deleted
    const existingAppointment = await prisma.randevu.findFirst({
      where: { 
        id: appointmentId,
        silindi_mi: false // Silinmiş randevuları silme
      }
    })

    if (!existingAppointment) {
      return NextResponse.json(
        { error: "Appointment not found" },
        { status: 404 }
      )
    }

    // Soft delete (mark as deleted)
    await prisma.randevu.update({
      where: { id: appointmentId },
      data: {
        silindi_mi: true,
        silinme_tarihi: new Date(),
        guncelleme_tarihi: new Date(),
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting appointment:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
