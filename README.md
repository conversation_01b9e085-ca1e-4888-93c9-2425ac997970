# Bakım Onarım Yönetim Sistemi

Modern web tabanlı bakım onarım takip sistemi. Konut projelerindeki arızaları, randevuları ve teknisyen çalışmalarını yönetmek için geliştirilmiştir.

## 📋 Özellikler

- 🏢 **Proje <PERSON>önet<PERSON>**: Çoklu proje, blok ve daire yönetimi
- 🔧 **Arıza Takip Sistemi**: Kapsamlı arıza bildirimi ve takip sistemi
- 👨‍🔧 **Teknisyen Yönetimi**: Uzmanlık alanları ve randevu atama sistemi
- 📅 **Randevu Planlama**: Akıllı teknisyen atama ve çalışma takibi
- 📊 **Dashboard & Raporlama**: Modern dashboard ve detaylı raporlar
- 🔐 **Kimlik Doğrulama**: NextAuth.js ile güvenli giriş sistemi
- 📱 **Responsive Design**: Mobil uyumlu modern arayüz

## 🚀 Kurulum

### Gereksinimler
- Node.js 18+
- PostgreSQL veritabanı
- npm veya yarn

### Kurulum Adımları

1. **Projeyi klonlayın:**
```bash
git clone <repository-url>
cd bakimonarim
```

2. **Bağımlılıkları yükleyin:**
```bash
npm install
```

3. **Ortam değişkenlerini ayarlayın:**
```bash
cp .env.example .env.local
# .env.local dosyasını düzenleyin
```

4. **Veritabanını ayarlayın:**
```bash
npx prisma db push
npm run db:seed
```

5. **Geliştirme sunucusunu başlatın:**
```bash
npm run dev
```

Tarayıcınızda [http://localhost:3001](http://localhost:3001) adresini açın.

## 🛠️ Teknoloji Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Radix UI
- **Backend**: Next.js API Routes
- **Veritabanı**: PostgreSQL, Prisma ORM
- **Kimlik Doğrulama**: NextAuth.js
- **State Management**: Zustand, React Query
- **Animasyonlar**: Framer Motion

## 📖 Kullanım

### Admin Paneli
- Kullanıcı adı: `<EMAIL>`
- Şifre: `admin123`

### Temel İşlemler
1. Projeler ve blokları tanımlayın
2. Daireleri ve sakinleri ekleyin
3. Teknisyenleri ve uzmanlık alanlarını tanımlayın
4. Arıza bildirimleri oluşturun
5. Randevuları planlayın ve takip edin

## 📝 Changelog

### v1.8.0 (05.07.2025)
#### 🚀 Yeni Özellikler
- [Buraya yeni özellikler ekleyin]

#### 🐛 Düzeltmeler  
- [Buraya düzeltmeler ekleyin]

#### ✨ İyileştirmeler
- [Buraya iyileştirmeler ekleyin]

### v1.8.0 (15.01.2025) - 🚀 Enhanced UX & Loading
#### ✨ Yeni Özellikler
- **Gelişmiş Loading Sistemi**: Tüm sayfa geçişlerinde loading overlay
- **Breadcrumb Navigasyon**: Tüm breadcrumb linklerinde loading desteği
- **Proje Yönetimi UX**: Proje, blok ve daire navigasyonlarında smooth geçişler
- **Dinamik Sürüm Sistemi**: Sürüm bazlı özellik gösterimi

#### 🔧 İyileştirmeler
- **Sidebar Navigation**: Tüm sidebar linklerinde loading context entegrasyonu
- **BlocksTable & DairelerTable**: Router push + loading sistemi
- **Footer**: Dinamik sürüm tag'ları ve renk sistemi

#### 🐛 Düzeltmeler
- **Hydration Hataları**: DashboardQuickActions bileşenindeki sorunlar çözüldü
- **MultiSelect Props**: Edit fault form'da prop'lar düzeltildi
- **Icon Rendering**: React component rendering sorunları giderildi

### v1.7.1 (29.06.2025)
#### 🐛 Düzeltmeler  
- **Randevu Silme Sorunu**: Silinmiş randevular artık API'lerden filtreleniyor ve sayfada görünmüyor
- **Takvim Filtreleme**: Varsayılan filtre "Tüm Durumlar" olarak ayarlandı, tüm randevular takvimde görünüyor
- **Soft Delete**: Randevu silme işlemi tüm API'lerde tutarlı şekilde uygulandı

#### ✨ İyileştirmeler
- **Otomatik Sayfa Yenileme**: Randevu silindikten sonra otomatik liste yenileme sistemi
- **Kullanıcı Deneyimi**: Silme işlemi sonrası bildirim ve yönlendirme iyileştirmeleri
- **Filtre Düzeni**: Randevu filtreleme sıralaması kullanıcı dostu hale getirildi
- **URL Parametresi**: Refresh kontrolü için akıllı URL parametresi sistemi

### v1.7.0 (29.06.2025)
#### 🚀 Yeni Özellikler
- **🎨 Merkezi İkon Sistemi**: Tüm uygulama için merkezi icon service sistemi
- **📦 Kategorize İkonlar**: 80+ ikon 8 kategoride organize edildi (Onarım, Elektrik, Boyama, Tesisat, Yapı, Güvenlik, Genel, Hava Durumu)
- **🎭 Gelişmiş İkon Seçici**: Kategoriler sayfasında görsel icon seçim arayüzü
- **⭐ Popüler İkonlar**: Hızlı erişim için öne çıkan 24 popüler ikon
- **🔧 TypeScript Icon Service**: Type-safe icon management sistemi

#### 🐛 Düzeltmeler  
- **Import Hatları**: Lucide React'tan mevcut olmayan ikonlar temizlendi
- **Dialog Layout**: Kategori düzenleme dialog'u genişletildi ve optimize edildi
- **Icon Mapping**: Güvenli icon mapping sistemi ile runtime hataları önlendi

#### ✨ İyileştirmeler
- **UX/UI**: 2-sütun layout ile gelişmiş kullanıcı deneyimi
- **Responsive Design**: Tüm ekran boyutlarında optimize icon seçici
- **Performans**: Merkezi icon sistemi ile daha hızlı icon yükleme
- **Maintainability**: Kodun yeniden kullanılabilirliği ve bakımı kolaylaştırıldı
- **Accessibility**: İkon seçimi için tooltip ve label desteği eklendi
- **Search Ready**: Gelecek icon arama özelliği için hazır altyapı

### v1.6.0 (2025-01-27)
#### 🐛 Düzeltmeler
- **Arıza Oluşturma**: Foreign key constraint hatası düzeltildi
- **API**: Doğru ID lookup sistemi eklendi (durum ve öncelik seviyeleri için)
- **Breadcrumb**: Arıza düzenle sayfasına hiyerarşik navigasyon eklendi

#### ✨ Yeni Özellikler
- Arıza oluşturma formunda daha güvenilir veri doğrulama
- Gelişmiş hata yönetimi ve kullanıcı geri bildirimi

### v1.5.0
- Smart Appointment System
- Modern Dashboard
- Mobile Work Tracker
- Appointment Analytics

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
