-- CreateEnum
CREATE TYPE "UzmanlikSeviye" AS ENUM ('BASL<PERSON>GIC', 'ORTA', 'ILERI', 'UZMAN');

-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'USER', 'TECH<PERSON>CIAN', 'MANAGER');

-- CreateEnum
CREATE TYPE "UserStatus" AS ENUM ('PENDING', 'ACTIVE', 'INACTIVE', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "RandevuDurum" AS ENUM ('PLANLI', 'DEVAM_EDIYOR', 'TAMAMLANDI', 'IPTAL');

-- CreateEnum
CREATE TYPE "IslemDurum" AS ENUM ('PLANLI', 'DEVAM_EDIYOR', 'TAMAMLANDI', 'BEKLEMEDE', 'IPTAL');

-- CreateEnum
CREATE TYPE "RandevuSonucDurum" AS ENUM ('TAMAMEN_COZULDU', '<PERSON>IS<PERSON>_COZULDU', 'COZULEMEDI', 'ERTELENDI', 'IPTAL_EDILDI');

-- CreateTable
CREATE TABLE "hesaplar" (
    "id" TEXT NOT NULL,
    "kullanici_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "provider_hesap_id" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "hesaplar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "oturumlar" (
    "id" TEXT NOT NULL,
    "oturum_token" TEXT NOT NULL,
    "kullanici_id" TEXT NOT NULL,
    "gecerlilik_tarihi" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "oturumlar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dogrulama_tokenleri" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "kullanicilar" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "soyad" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "email_dogrulandi" TIMESTAMP(3),
    "sifre" TEXT,
    "telefon" TEXT,
    "resim" TEXT,
    "rol" "UserRole" NOT NULL DEFAULT 'USER',
    "durum" "UserStatus" NOT NULL DEFAULT 'PENDING',
    "departman_id" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,
    "daire_id" TEXT,

    CONSTRAINT "kullanicilar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "departmanlar" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "aciklama" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "departmanlar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "projeler" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "aciklama" TEXT,
    "adres" TEXT NOT NULL,
    "baslangic_tarihi" TIMESTAMP(3) NOT NULL,
    "bitis_tarihi" TIMESTAMP(3),
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "projeler_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "bloklar" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "aciklama" TEXT,
    "proje_id" TEXT NOT NULL,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "bloklar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "daireler" (
    "id" TEXT NOT NULL,
    "numara" TEXT NOT NULL,
    "kat" INTEGER NOT NULL,
    "blok_id" TEXT NOT NULL,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "daireler_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ariza_tipleri" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "aciklama" TEXT,
    "renk" TEXT NOT NULL DEFAULT '#6B7280',
    "ikon" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "ariza_tipleri_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "aciliyet_seviyeleri" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "seviye" INTEGER NOT NULL,
    "renk" TEXT NOT NULL,
    "aciklama" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "aciliyet_seviyeleri_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ariza_durumlari" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "renk" TEXT NOT NULL,
    "aciklama" TEXT,
    "sira" INTEGER NOT NULL,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "ariza_durumlari_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "arizalar" (
    "id" TEXT NOT NULL,
    "numara" TEXT NOT NULL,
    "baslik" TEXT NOT NULL,
    "aciklama" TEXT NOT NULL,
    "daire_id" TEXT NOT NULL,
    "ariza_tip_id" TEXT NOT NULL,
    "aciliyet_id" TEXT NOT NULL,
    "durum_id" TEXT NOT NULL,
    "bildiren_ad_soyad" TEXT NOT NULL,
    "bildiren_telefon" TEXT NOT NULL,
    "baslangic_tarihi" TIMESTAMP(3),
    "hedef_bitis_tarihi" TIMESTAMP(3),
    "gercek_bitis_tarihi" TIMESTAMP(3),
    "tahmini_maliyet" DOUBLE PRECISION,
    "gercek_maliyet" DOUBLE PRECISION,
    "resimler" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "arizalar_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ariza_gecmisi" (
    "id" TEXT NOT NULL,
    "ariza_id" TEXT NOT NULL,
    "durum_id" TEXT NOT NULL,
    "kullanici_id" TEXT NOT NULL,
    "yorum" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ariza_gecmisi_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ariza_yorumlari" (
    "id" TEXT NOT NULL,
    "ariza_id" TEXT NOT NULL,
    "kullanici_id" TEXT NOT NULL,
    "yorum" TEXT NOT NULL,
    "resimler" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ariza_yorumlari_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "malzemeler" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "birim" TEXT NOT NULL,
    "aciklama" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "malzemeler_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "randevular" (
    "id" TEXT NOT NULL,
    "ariza_id" TEXT NOT NULL,
    "randevu_tarihi" TIMESTAMP(3) NOT NULL,
    "durum" "RandevuDurum" NOT NULL DEFAULT 'PLANLI',
    "aciklama" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "randevular_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "randevu_teknisyenleri" (
    "id" TEXT NOT NULL,
    "randevu_id" TEXT NOT NULL,
    "teknisyen_id" TEXT NOT NULL,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "randevu_teknisyenleri_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "islem_turleri" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "aciklama" TEXT,
    "kategori" TEXT NOT NULL,
    "renk" TEXT NOT NULL DEFAULT '#6B7280',
    "sure_dk" INTEGER,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "islem_turleri_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "randevu_islemleri" (
    "id" TEXT NOT NULL,
    "randevu_id" TEXT NOT NULL,
    "islem_turu_id" TEXT NOT NULL,
    "teknisyen_id" TEXT NOT NULL,
    "aciklama" TEXT,
    "baslangic_saat" TIMESTAMP(3),
    "bitis_saat" TIMESTAMP(3),
    "durum" "IslemDurum" NOT NULL DEFAULT 'PLANLI',
    "notlar" TEXT,
    "resimler" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,

    CONSTRAINT "randevu_islemleri_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "randevu_malzemeleri" (
    "id" TEXT NOT NULL,
    "randevu_id" TEXT NOT NULL,
    "malzeme_id" TEXT NOT NULL,
    "miktar" DOUBLE PRECISION NOT NULL,
    "teknisyen_id" TEXT NOT NULL,
    "kullanim_aciklama" TEXT,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "olusturan_id" TEXT,

    CONSTRAINT "randevu_malzemeleri_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "randevu_sonuclari" (
    "id" TEXT NOT NULL,
    "randevu_id" TEXT NOT NULL,
    "durum" "RandevuSonucDurum" NOT NULL,
    "ariza_cozuldu_mu" BOOLEAN NOT NULL DEFAULT false,
    "tamamlanma_orani" INTEGER NOT NULL DEFAULT 0,
    "sonraki_randevu_gerekli" BOOLEAN NOT NULL DEFAULT false,
    "teknisyen_notlari" TEXT,
    "musteri_memnuniyet" INTEGER,
    "musteri_yorumu" TEXT,
    "karsilasilan_zorluklar" TEXT,
    "resimler" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "gercek_baslangic" TIMESTAMP(3),
    "gercek_bitis" TIMESTAMP(3),
    "toplam_sure_dk" INTEGER,
    "toplam_malzeme_maliyeti" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "iscilik_maliyeti" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "toplam_maliyet" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,

    CONSTRAINT "randevu_sonuclari_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "uzmanlik_alanlari" (
    "id" TEXT NOT NULL,
    "ad" TEXT NOT NULL,
    "aciklama" TEXT,
    "renk" TEXT NOT NULL DEFAULT '#3B82F6',
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,
    "olusturan_id" TEXT,
    "guncelleyen_id" TEXT,
    "silindi_mi" BOOLEAN NOT NULL DEFAULT false,
    "silinme_tarihi" TIMESTAMP(3),
    "silen_id" TEXT,

    CONSTRAINT "uzmanlik_alanlari_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "teknisyen_uzmanlik_alanlari" (
    "id" TEXT NOT NULL,
    "teknisyen_id" TEXT NOT NULL,
    "uzmanlik_alani_id" TEXT NOT NULL,
    "seviye" "UzmanlikSeviye" NOT NULL DEFAULT 'ORTA',
    "olusturulma_tarihi" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "guncelleme_tarihi" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "teknisyen_uzmanlik_alanlari_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "hesaplar_provider_provider_hesap_id_key" ON "hesaplar"("provider", "provider_hesap_id");

-- CreateIndex
CREATE UNIQUE INDEX "oturumlar_oturum_token_key" ON "oturumlar"("oturum_token");

-- CreateIndex
CREATE UNIQUE INDEX "dogrulama_tokenleri_token_key" ON "dogrulama_tokenleri"("token");

-- CreateIndex
CREATE UNIQUE INDEX "dogrulama_tokenleri_identifier_token_key" ON "dogrulama_tokenleri"("identifier", "token");

-- CreateIndex
CREATE UNIQUE INDEX "kullanicilar_email_key" ON "kullanicilar"("email");

-- CreateIndex
CREATE UNIQUE INDEX "aciliyet_seviyeleri_seviye_key" ON "aciliyet_seviyeleri"("seviye");

-- CreateIndex
CREATE UNIQUE INDEX "ariza_durumlari_sira_key" ON "ariza_durumlari"("sira");

-- CreateIndex
CREATE UNIQUE INDEX "arizalar_numara_key" ON "arizalar"("numara");

-- CreateIndex
CREATE UNIQUE INDEX "randevu_teknisyenleri_randevu_id_teknisyen_id_key" ON "randevu_teknisyenleri"("randevu_id", "teknisyen_id");

-- CreateIndex
CREATE UNIQUE INDEX "islem_turleri_ad_key" ON "islem_turleri"("ad");

-- CreateIndex
CREATE UNIQUE INDEX "randevu_sonuclari_randevu_id_key" ON "randevu_sonuclari"("randevu_id");

-- CreateIndex
CREATE UNIQUE INDEX "uzmanlik_alanlari_ad_key" ON "uzmanlik_alanlari"("ad");

-- CreateIndex
CREATE UNIQUE INDEX "teknisyen_uzmanlik_alanlari_teknisyen_id_uzmanlik_alani_id_key" ON "teknisyen_uzmanlik_alanlari"("teknisyen_id", "uzmanlik_alani_id");

-- AddForeignKey
ALTER TABLE "hesaplar" ADD CONSTRAINT "hesaplar_kullanici_id_fkey" FOREIGN KEY ("kullanici_id") REFERENCES "kullanicilar"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "oturumlar" ADD CONSTRAINT "oturumlar_kullanici_id_fkey" FOREIGN KEY ("kullanici_id") REFERENCES "kullanicilar"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kullanicilar" ADD CONSTRAINT "kullanicilar_departman_id_fkey" FOREIGN KEY ("departman_id") REFERENCES "departmanlar"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kullanicilar" ADD CONSTRAINT "kullanicilar_olusturan_id_fkey" FOREIGN KEY ("olusturan_id") REFERENCES "kullanicilar"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "kullanicilar" ADD CONSTRAINT "kullanicilar_daire_id_fkey" FOREIGN KEY ("daire_id") REFERENCES "daireler"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bloklar" ADD CONSTRAINT "bloklar_proje_id_fkey" FOREIGN KEY ("proje_id") REFERENCES "projeler"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "daireler" ADD CONSTRAINT "daireler_blok_id_fkey" FOREIGN KEY ("blok_id") REFERENCES "bloklar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "arizalar" ADD CONSTRAINT "arizalar_daire_id_fkey" FOREIGN KEY ("daire_id") REFERENCES "daireler"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "arizalar" ADD CONSTRAINT "arizalar_ariza_tip_id_fkey" FOREIGN KEY ("ariza_tip_id") REFERENCES "ariza_tipleri"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "arizalar" ADD CONSTRAINT "arizalar_aciliyet_id_fkey" FOREIGN KEY ("aciliyet_id") REFERENCES "aciliyet_seviyeleri"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "arizalar" ADD CONSTRAINT "arizalar_durum_id_fkey" FOREIGN KEY ("durum_id") REFERENCES "ariza_durumlari"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ariza_gecmisi" ADD CONSTRAINT "ariza_gecmisi_ariza_id_fkey" FOREIGN KEY ("ariza_id") REFERENCES "arizalar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ariza_gecmisi" ADD CONSTRAINT "ariza_gecmisi_durum_id_fkey" FOREIGN KEY ("durum_id") REFERENCES "ariza_durumlari"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ariza_gecmisi" ADD CONSTRAINT "ariza_gecmisi_kullanici_id_fkey" FOREIGN KEY ("kullanici_id") REFERENCES "kullanicilar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ariza_yorumlari" ADD CONSTRAINT "ariza_yorumlari_ariza_id_fkey" FOREIGN KEY ("ariza_id") REFERENCES "arizalar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ariza_yorumlari" ADD CONSTRAINT "ariza_yorumlari_kullanici_id_fkey" FOREIGN KEY ("kullanici_id") REFERENCES "kullanicilar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevular" ADD CONSTRAINT "randevular_ariza_id_fkey" FOREIGN KEY ("ariza_id") REFERENCES "arizalar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_teknisyenleri" ADD CONSTRAINT "randevu_teknisyenleri_randevu_id_fkey" FOREIGN KEY ("randevu_id") REFERENCES "randevular"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_teknisyenleri" ADD CONSTRAINT "randevu_teknisyenleri_teknisyen_id_fkey" FOREIGN KEY ("teknisyen_id") REFERENCES "kullanicilar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_islemleri" ADD CONSTRAINT "randevu_islemleri_randevu_id_fkey" FOREIGN KEY ("randevu_id") REFERENCES "randevular"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_islemleri" ADD CONSTRAINT "randevu_islemleri_islem_turu_id_fkey" FOREIGN KEY ("islem_turu_id") REFERENCES "islem_turleri"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_islemleri" ADD CONSTRAINT "randevu_islemleri_teknisyen_id_fkey" FOREIGN KEY ("teknisyen_id") REFERENCES "kullanicilar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_malzemeleri" ADD CONSTRAINT "randevu_malzemeleri_randevu_id_fkey" FOREIGN KEY ("randevu_id") REFERENCES "randevular"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_malzemeleri" ADD CONSTRAINT "randevu_malzemeleri_malzeme_id_fkey" FOREIGN KEY ("malzeme_id") REFERENCES "malzemeler"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_malzemeleri" ADD CONSTRAINT "randevu_malzemeleri_teknisyen_id_fkey" FOREIGN KEY ("teknisyen_id") REFERENCES "kullanicilar"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "randevu_sonuclari" ADD CONSTRAINT "randevu_sonuclari_randevu_id_fkey" FOREIGN KEY ("randevu_id") REFERENCES "randevular"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "teknisyen_uzmanlik_alanlari" ADD CONSTRAINT "teknisyen_uzmanlik_alanlari_teknisyen_id_fkey" FOREIGN KEY ("teknisyen_id") REFERENCES "kullanicilar"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "teknisyen_uzmanlik_alanlari" ADD CONSTRAINT "teknisyen_uzmanlik_alanlari_uzmanlik_alani_id_fkey" FOREIGN KEY ("uzmanlik_alani_id") REFERENCES "uzmanlik_alanlari"("id") ON DELETE CASCADE ON UPDATE CASCADE;
