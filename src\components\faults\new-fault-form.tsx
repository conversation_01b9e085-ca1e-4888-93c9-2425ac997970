"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "@/hooks/use-toast"
import { useMLPrediction } from "@/hooks/useMLPrediction"
import { 
  Save, 
  ArrowLeft,
  MapPin,
  Camera,
  AlertTriangle,
  User,
  Loader2,
  Sparkles,
  Check,
  Brain,
  BookOpen
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Form validation schema
const newFaultSchema = z.object({
  baslik: z.string().min(5, "Başlık en az 5 karakter olmalıdır").max(100, "Başlık en fazla 100 karakter olabilir"),
  aciklama: z.string().min(10, "Açıklama en az 10 karakter olmalıdır").max(1000, "Açıklama en fazla 1000 karakter olabilir"),
  ariza_tip_id: z.string().min(1, "Kategori seçimi gereklidir"),
  aciliyet_id: z.string().min(1, "Öncelik seçimi gereklidir"),
  bildiren_ad: z.string().min(1, "Bildiren kişinin adı gereklidir"),
  bildiren_telefon: z.string().min(10, "Geçerli bir telefon numarası giriniz"),
  fotograflar: z.array(z.string()).optional(),
  projeSlug: z.string().min(1, "Proje seçimi gereklidir").optional(),
  blokSlug: z.string().min(1, "Blok seçimi gereklidir").optional(),
  daireSlug: z.string().min(1, "Daire seçimi gereklidir").optional(),
})

type NewFaultFormData = z.infer<typeof newFaultSchema>

interface Category {
  id: string
  ad: string
  aciklama?: string
}

interface Priority {
  id: string
  ad: string
  renk: string
  seviye: number
}

interface Project {
  id: string
  ad: string
  slug: string
}

interface Block {
  id: string
  ad: string
  slug: string
  projeId: string
}

interface Apartment {
  id: string
  numara: string
  slug: string
  blokId: string
}

interface LocationInfo {
  proje: { ad: string; slug: string }
  blok: { ad: string; slug: string }
  daire: { numara: string; slug: string }
  projeId: string
  blokId: string
  daireId: string
}

interface NewFaultFormProps {
  locationInfo?: LocationInfo
}

// locationInfo'dan slug'ı güvenli şekilde al
function getSlugFromLocationInfo(locationInfo: LocationInfo | undefined, key: 'proje' | 'blok' | 'daire') {
  if (!locationInfo) return undefined;
  if (key === 'proje' && locationInfo.proje?.slug) return locationInfo.proje.slug;
  if (key === 'blok' && locationInfo.blok?.slug) return locationInfo.blok.slug;
  if (key === 'daire' && locationInfo.daire?.slug) return locationInfo.daire.slug;
  // Fallback: ad'dan slug üret (proje/blok), numara'dan slug üret (daire)
  if (key === 'proje' && locationInfo.proje?.ad) return locationInfo.proje.ad.toLowerCase().replace(/ /g, '-');
  if (key === 'blok' && locationInfo.blok?.ad) return locationInfo.blok.ad.toLowerCase().replace(/ /g, '-');
  if (key === 'daire' && locationInfo.daire?.numara) return locationInfo.daire.numara.toLowerCase().replace(/ /g, '-');
  // Son çare id varsa onu döndür
  if (key === 'proje' && locationInfo.projeId) return locationInfo.projeId;
  if (key === 'blok' && locationInfo.blokId) return locationInfo.blokId;
  if (key === 'daire' && locationInfo.daireId) return locationInfo.daireId;
  return undefined;
}

export function NewFaultForm({ locationInfo }: NewFaultFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [priorities, setPriorities] = useState<Priority[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [blocks, setBlocks] = useState<Block[]>([])
  const [apartments, setApartments] = useState<Apartment[]>([])
  const [dataLoading, setDataLoading] = useState(true)
  const [uploadedPhotos, setUploadedPhotos] = useState<string[]>([])
  
  const router = useRouter()
  
  // ML Prediction hook
  const {
    isPredicting,
    prediction,
    isLearning,
    predictCategory,
    teachModel,
    clearPrediction,
    hasLowConfidence,
    hasHighConfidence,
    suggestedCategory,
    isServiceAvailable
  } = useMLPrediction()

  const form = useForm<NewFaultFormData>({
    resolver: zodResolver(newFaultSchema),
    defaultValues: {
      baslik: "",
      aciklama: "",
      ariza_tip_id: "",
      aciliyet_id: "",
      bildiren_ad: "",
      bildiren_telefon: "",
      fotograflar: [],
    },
  })

  // Load form data
  useEffect(() => {
    async function loadFormData() {
      try {
        setDataLoading(true)
        const [categoriesRes, prioritiesRes] = await Promise.all([
          fetch("/api/categories"),
          fetch("/api/faults/priorities"),
        ])

        const [categoriesData, prioritiesData] = await Promise.all([
          categoriesRes.json(),
          prioritiesRes.json(),
        ])

        setCategories(categoriesData.categories || [])
        setPriorities(prioritiesData.priorities || [])

        // Eğer locationInfo varsa, ilgili blok ve daireleri yükle (sadece slug ile)
        if (locationInfo?.proje?.slug) {
          const blocksRes = await fetch(`/api/projects/${locationInfo.proje.slug}/blocks`)
          if (!blocksRes.ok) {
            throw new Error("Bloklar yüklenemedi")
          }
          const blocksData = await blocksRes.json()
          setBlocks(blocksData.blocks || [])

          if (locationInfo?.blok?.slug) {
            const apartmentsRes = await fetch(`/api/projects/${locationInfo.proje.slug}/blocks/${locationInfo.blok.slug}/apartments`)
            if (!apartmentsRes.ok) {
              throw new Error("Daireler yüklenemedi")
            }
            const apartmentsData = await apartmentsRes.json()
            setApartments(apartmentsData.apartments || [])
          }
        }
      } catch (error) {
        console.error("Form verileri yüklenirken hata:", error)
        toast.error("Form verileri yüklenemedi")
      } finally {
        setDataLoading(false)
      }
    }

    loadFormData()
  }, [locationInfo])

  // locationInfo değiştiğinde form değerlerini güncelle
  useEffect(() => {
    if (locationInfo) {
      if (getSlugFromLocationInfo(locationInfo, 'proje')) form.setValue('projeSlug', getSlugFromLocationInfo(locationInfo, 'proje'));
      if (getSlugFromLocationInfo(locationInfo, 'blok')) form.setValue('blokSlug', getSlugFromLocationInfo(locationInfo, 'blok'));
      if (getSlugFromLocationInfo(locationInfo, 'daire')) form.setValue('daireSlug', getSlugFromLocationInfo(locationInfo, 'daire'));
    }
  }, [locationInfo, form])

  // Proje değiştiğinde blokları yükle
  const handleProjectChange = async (projeSlug: string) => {
    try {
      const response = await fetch(`/api/projects/${projeSlug}/blocks`)
      if (!response.ok) {
        throw new Error("Failed to fetch blocks")
      }
      const data = await response.json()
      setBlocks(data.blocks || [])
      setApartments([]) // Daireleri sıfırla
      form.setValue("blokSlug", "")
      form.setValue("daireSlug", "")
    } catch (error) {
      console.error("Bloklar yüklenirken hata:", error)
      toast.error("Bloklar yüklenemedi")
    }
  }

  // Blok değiştiğinde daireleri yükle
  const handleBlockChange = async (blokSlug: string) => {
    try {
      const projeSlug = form.getValues("projeSlug")
      if (!projeSlug) return
      
      const response = await fetch(`/api/projects/${projeSlug}/blocks/${blokSlug}/apartments`)
      if (!response.ok) {
        throw new Error("Failed to fetch apartments")
      }
      const data = await response.json()
      setApartments(data.apartments || [])
      form.setValue("daireSlug", "")
    } catch (error) {
      console.error("Daireler yüklenirken hata:", error)
      toast.error("Daireler yüklenemedi")
    }
  }

  // ML tahmin yap
  const handlePredictCategory = async () => {
    const title = form.getValues("baslik")
    const description = form.getValues("aciklama")
    
    if (!title || !description) {
      toast.error("Başlık ve açıklama gerekli")
      return
    }
    
    if (title.length < 5 || description.length < 10) {
      toast.error("Başlık en az 5, açıklama en az 10 karakter olmalı")
      return
    }
    
    if (categories.length === 0) {
      toast.error("Kategoriler yüklenemedi")
      return
    }
    
    const result = await predictCategory(title, description, categories)
    
    if (result?.success && result.selectedCategory) {
      toast.success(`AI önerisi: ${result.selectedCategory.ad} (${(result.selectedCategory.confidence * 100).toFixed(1)}% güven)`)
    } else if (result?.serviceUnavailable) {
      toast.info("AI servisi şu anda kullanılamıyor, manuel kategori seçimi yapabilirsiniz")
    } else {
      toast.info("AI yeterli güvenle kategori öneremiyor")
    }
  }

  // ML önerisini uygula
  const applyMlPrediction = () => {
    if (suggestedCategory) {
      form.setValue("ariza_tip_id", suggestedCategory.id)
      toast.success("AI önerisi uygulandı!")
    }
  }

  // Model'e yeni örnek öğret
  const handleTeachModel = async () => {
    const title = form.getValues("baslik")
    const description = form.getValues("aciklama")
    const selectedCategoryId = form.getValues("ariza_tip_id")
    
    if (!title || !description || !selectedCategoryId) {
      toast.error("Başlık, açıklama ve kategori seçimi gereklidir")
      return
    }
    
    const selectedCategory = categories.find(cat => cat.id === selectedCategoryId)
    if (!selectedCategory) {
      toast.error("Seçilen kategori bulunamadı")
      return
    }
    
    const result = await teachModel(title, description, selectedCategory.ad)
    if (result) {
      toast.success("Model bu örneği öğrendi! Gelecekte daha iyi tahmin yapacak.")
      clearPrediction()
    } else {
      toast.info("AI servisi şu anda kullanılamıyor, öğrenme işlemi ertelendi")
    }
  }

  const onSubmit = async (data: NewFaultFormData) => {
    try {
      setIsSubmitting(true)

      // locationInfo varsa slug'ı güvenli şekilde al, yoksa formdan al
      const projeSlug = getSlugFromLocationInfo(locationInfo, 'proje') || data.projeSlug;
      const blokSlug = getSlugFromLocationInfo(locationInfo, 'blok') || data.blokSlug;
      const daireSlug = getSlugFromLocationInfo(locationInfo, 'daire') || data.daireSlug;
      if (!projeSlug || !blokSlug || !daireSlug) {
        toast.error("Konum bilgisi eksik!");
        setIsSubmitting(false);
        return;
      }
      
      const response = await fetch(`/api/projects/${projeSlug}/blocks/${blokSlug}/apartments/${daireSlug}/arizalar`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          baslik: data.baslik,
          aciklama: data.aciklama,
          ariza_tip_id: data.ariza_tip_id,
          aciliyet_id: data.aciliyet_id,
          bildirenAdSoyad: data.bildiren_ad,
          bildirenTelefon: data.bildiren_telefon,
          fotograflar: uploadedPhotos,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Arıza eklenirken hata oluştu")
      }

      const newFault = await response.json()
      
      // Online Learning: Eğer düşük güvenle tahmin yapıldıysa ve kullanıcı farklı kategori seçtiyse model'e öğret
      if (hasLowConfidence && prediction && suggestedCategory && isServiceAvailable) {
        const selectedCategory = categories.find(cat => cat.id === data.ariza_tip_id)
        if (selectedCategory && selectedCategory.ad !== suggestedCategory.ad) {
          const result = await teachModel(data.baslik, data.aciklama, selectedCategory.ad)
          if (result) {
          } else {
          }
        }
      }
      
      toast.success("Arıza başarıyla oluşturuldu!");
      router.push(`/projeler/${projeSlug}/bloklar/${blokSlug}/${daireSlug}/arizalar/${newFault.slug}`);
    } catch (error) {
      console.error("Arıza oluşturulurken hata:", error)
      toast.error(error instanceof Error ? error.message : "Beklenmeyen bir hata oluştu")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (dataLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Form yükleniyor...</span>
        </div>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Sol Sütun */}
          <div className="space-y-6">
            {/* Konum Bilgileri */}
            {locationInfo ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Konum Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Proje</span>
                      <p className="text-lg font-semibold">{locationInfo.proje.ad}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Blok</span>
                      <p className="text-lg font-semibold">{locationInfo.blok.ad}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Daire</span>
                      <p className="text-lg font-semibold">{locationInfo.daire.numara}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Konum Seçimi
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Proje select */}
                  <FormField
                    control={form.control}
                    name="projeSlug"
                    render={({ field }) => (
                      <div>
                        <FormLabel>Proje</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value)
                            handleProjectChange(value)
                          }}
                          value={field.value || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Proje seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {projects.map((project) => (
                              <SelectItem key={project.id} value={project.slug}>
                                {project.ad}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </div>
                    )}
                  />
                  {/* Blok select */}
                  <FormField
                    control={form.control}
                    name="blokSlug"
                    render={({ field }) => (
                      <div>
                        <FormLabel>Blok</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value)
                            handleBlockChange(value)
                          }}
                          value={field.value || ""}
                          disabled={!form.getValues("projeSlug")}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Blok seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {blocks.map((block) => (
                              <SelectItem key={block.id} value={block.slug}>
                                {block.ad}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </div>
                    )}
                  />
                  {/* Daire select */}
                  <FormField
                    control={form.control}
                    name="daireSlug"
                    render={({ field }) => (
                      <div>
                        <FormLabel>Daire</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ""}
                          disabled={!form.getValues("blokSlug")}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Daire seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {apartments.map((apartment) => (
                              <SelectItem key={apartment.id} value={apartment.slug}>
                                {apartment.numara}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </div>
                    )}
                  />
                </CardContent>
              </Card>
            )}

            {/* Bildiren Bilgileri */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Bildiren Bilgileri
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="bildiren_ad"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ad Soyad</FormLabel>
                      <FormControl>
                        <Input placeholder="Bildiren kişinin adı soyadı" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="bildiren_telefon"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefon</FormLabel>
                      <FormControl>
                        <Input placeholder="Telefon numarası" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>

          {/* Sağ Sütun */}
          <div className="space-y-6">
            {/* Arıza Temel Bilgileri */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Arıza Temel Bilgileri
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="baslik"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Başlık</FormLabel>
                      <FormControl>
                        <Input placeholder="Arıza başlığı" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="ariza_tip_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        <span>Kategori</span>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handlePredictCategory}
                          disabled={isPredicting}
                          className={`h-7 px-2 text-xs ${
                            prediction?.serviceUnavailable ? 'opacity-50' : ''
                          }`}
                          title={prediction?.serviceUnavailable ? 'AI servisi şu anda kullanılamıyor' : 'AI önerisi al'}
                        >
                          {isPredicting ? (
                            <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          ) : prediction?.serviceUnavailable ? (
                            <Brain className="h-3 w-3 mr-1 text-gray-400" />
                          ) : (
                            <Sparkles className="h-3 w-3 mr-1" />
                          )}
                          AI Önerisi
                        </Button>
                      </FormLabel>
                      <div className="space-y-2">
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Kategori seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.ad}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        
                        {/* ML Tahmin Sonucu */}
                        {prediction && (
                          <div className={`p-3 border rounded-lg ${
                            prediction.serviceUnavailable 
                              ? 'bg-gray-50 border-gray-200' 
                              : 'bg-blue-50 border-blue-200'
                          }`}>
                            {prediction.serviceUnavailable ? (
                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <span className="text-sm font-medium text-gray-700">
                                    ⚠️ AI Servisi Kullanılamıyor
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  Manuel kategori seçimi yapabilirsiniz. AI servisi tekrar çalışır duruma geldiğinde otomatik olarak kullanılacaktır.
                                </div>
                              </div>
                            ) : prediction.success && suggestedCategory ? (
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium text-blue-900">
                                    🤖 AI Önerisi
                                  </span>
                                  <span className="text-xs text-blue-600">
                                    {(suggestedCategory.confidence * 100).toFixed(1)}% güven
                                  </span>
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="text-sm text-blue-800">
                                    {suggestedCategory.ad}
                                  </span>
                                  <Button
                                    type="button"
                                    size="sm"
                                    onClick={applyMlPrediction}
                                    className="h-6 px-2 text-xs"
                                  >
                                    <Check className="h-3 w-3 mr-1" />
                                    Uygula
                                  </Button>
                                </div>
                                {prediction.alternatives && prediction.alternatives.length > 0 && (
                                  <div className="text-xs text-blue-600">
                                    Alternatifler: {prediction.alternatives.slice(0, 2).map((alt: any) => alt.ad).filter(Boolean).join(', ')}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div className="space-y-2">
                                <div className="text-sm text-blue-800">
                                  AI yeterli güvenle kategori öneremiyor.
                                </div>
                                {hasLowConfidence && (
                                  <div className="flex items-center gap-2">
                                    <Button
                                      type="button"
                                      size="sm"
                                      variant="outline"
                                      onClick={handleTeachModel}
                                      disabled={isLearning}
                                      className="h-6 px-2 text-xs"
                                    >
                                      {isLearning ? (
                                        <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                      ) : (
                                        <BookOpen className="h-3 w-3 mr-1" />
                                      )}
                                      Model'e Öğret
                                    </Button>
                                    <span className="text-xs text-blue-600">
                                      Bu örneği model'e öğretmek için kategori seçin
                                    </span>
                                  </div>
                                )}
                                {prediction.alternatives && prediction.alternatives.length > 0 && (
                                  <div className="text-xs text-blue-600">
                                    Alternatifler: {prediction.alternatives.slice(0, 2).map((alt: any) => alt.ad).filter(Boolean).join(', ')}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="aciklama"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Açıklama</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Arıza detayları" 
                          className="min-h-[120px]" 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Arıza Detayları */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  Arıza Detayları
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="aciliyet_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Öncelik</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Öncelik seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {priorities.map((priority) => (
                            <SelectItem key={priority.id} value={priority.id}>
                              {priority.ad}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* TODO: Fotoğraf yükleme alanı eklenecek */}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-4">
          <Button
            variant="outline"
            type="button"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Geri Dön
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Kaydediliyor...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Kaydet
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}
