const fs = require('fs');
const path = require('path');

// Kategori analizi dosyasını oku
const categoriesData = JSON.parse(fs.readFileSync(path.join(__dirname, '../data/categories-analysis.json'), 'utf8'));

// Türkçe arıza başlıkları ve açıklamaları için şablonlar
const faultTemplates = {
  "Su Tesisatı": {
    titles: [
      "Banyo musluğu damlatıyor",
      "Mutfak lavabosu tıkandı",
      "<PERSON> kaçağı var",
      "Klozet su kaçırıyor",
      "<PERSON>ıcak su gelmiyor",
      "Lavabo tıkanıklığı",
      "<PERSON>ş başlığı bozuk",
      "Su basıncı düşük",
      "Boru patlaması",
      "Su sayacı arızalı",
      "Drenaj problemi",
      "Su kesintisi",
      "<PERSON>ıcak su vanası bozuk",
      "Soğuk su vanası bozuk",
      "<PERSON> sızıntıs<PERSON>",
      "<PERSON><PERSON>zet tıkanıklığı",
      "Banyo gideri tıkandı",
      "Mutfak gideri tıkandı",
      "Su kaçağı tespit edildi",
      "Boru sızıntısı"
    ],
    descriptions: [
      "Banyo musluğunda sürekli damlama var, su israfı oluyor. Musluk başlığı gevşek.",
      "Mutfak lavabosu tamamen tıkandı, su akmıyor. Gider borusu dolu.",
      "Duvarda su kaçağı tespit edildi, nem oluşuyor.",
      "Klozet sürekli su kaçırıyor, su israfı var.",
      "Sıcak su gelmiyor, sadece soğuk su var.",
      "Lavabo tıkanıklığı var, su yavaş akıyor.",
      "Duş başlığı bozuk, su düzgün gelmiyor.",
      "Su basıncı çok düşük, su zayıf akıyor.",
      "Boru patlaması oldu, su fışkırıyor.",
      "Su sayacı arızalı, doğru okuma yapmıyor.",
      "Drenaj sistemi çalışmıyor, su birikiyor.",
      "Sürekli su kesintisi yaşanıyor.",
      "Sıcak su vanası bozuk, ayar yapılamıyor.",
      "Soğuk su vanası bozuk, su kesilemiyor.",
      "Duvarda su sızıntısı var, ıslaklık oluşuyor.",
      "Klozet tamamen tıkandı, su akmıyor.",
      "Banyo gideri tıkandı, su birikiyor.",
      "Mutfak gideri tıkandı, su akmıyor.",
      "Su kaçağı tespit edildi, acil müdahale gerekli.",
      "Boru sızıntısı var, su damlıyor."
    ]
  },
  "Elektrik": {
    titles: [
      "Elektrik kesintisi yaşanıyor",
      "Priz çalışmıyor",
      "Ampul patladı",
      "Sigorta atıyor",
      "Elektrik düğmesi bozuk",
      "Kısa devre var",
      "Elektrik kaçağı",
      "Şalter bozuk",
      "Anahtar çalışmıyor",
      "Fiş takılmıyor",
      "Kablo arızalı",
      "Elektrik sayacı bozuk",
      "Elektrik çarpması",
      "Elektrik prizi gevşek",
      "Elektrik kablosu yanık",
      "Aydınlatma çalışmıyor",
      "Elektrik düğmesi gevşek",
      "Priz yanık",
      "Elektrik kaçağı tespit edildi",
      "Sigorta sürekli atıyor"
    ],
    descriptions: [
      "Dairemizde sürekli elektrik kesintisi oluyor, prizler çalışmıyor. Sigorta atıyor.",
      "Priz çalışmıyor, elektrik gelmiyor.",
      "Ampul patladı, ışık yok.",
      "Sigorta sürekli atıyor, elektrik kesiliyor.",
      "Elektrik düğmesi bozuk, açıp kapatamıyoruz.",
      "Kısa devre var, elektrik kesiliyor.",
      "Elektrik kaçağı tespit edildi, tehlikeli durum.",
      "Şalter bozuk, elektrik kesilemiyor.",
      "Anahtar çalışmıyor, ışık açılmıyor.",
      "Fiş takılmıyor, priz bozuk.",
      "Kablo arızalı, elektrik gelmiyor.",
      "Elektrik sayacı bozuk, doğru okuma yapmıyor.",
      "Elektrik çarpması yaşandı, tehlikeli.",
      "Elektrik prizi gevşek, fiş düşüyor.",
      "Elektrik kablosu yanık, tehlikeli durum.",
      "Aydınlatma çalışmıyor, karanlık.",
      "Elektrik düğmesi gevşek, sallanıyor.",
      "Priz yanık, kullanılamıyor.",
      "Elektrik kaçağı tespit edildi, acil müdahale gerekli.",
      "Sigorta sürekli atıyor, elektrik kesiliyor."
    ]
  },
  "Boyama": {
    titles: [
      "Duvar boyası dökülüyor",
      "Tavan badanası gerekiyor",
      "Duvar çatlağı var",
      "Boya kabarması",
      "Sıva dökülmesi",
      "Renk değişikliği",
      "Boya lekesi",
      "Badana yapılması gerekiyor",
      "Duvar boyası solmuş",
      "Tavan boyası dökülüyor",
      "Boya kokusu",
      "Sıva çatlağı",
      "Duvar boyası kabarması",
      "Tavan sıvası dökülüyor",
      "Boya kuruması",
      "Duvar boyası lekesi",
      "Badana gerekiyor",
      "Sıva tamiri",
      "Boya yenileme",
      "Duvar onarımı"
    ],
    descriptions: [
      "Duvar boyası dökülüyor, duvar çıplak kalıyor.",
      "Tavan badanası gerekiyor, eski badana dökülüyor.",
      "Duvar çatlağı var, boya dökülüyor.",
      "Boya kabarması var, duvar bozuk görünüyor.",
      "Sıva dökülmesi var, duvar hasarlı.",
      "Renk değişikliği olmuş, boya solmuş.",
      "Boya lekesi var, temizlenemiyor.",
      "Badana yapılması gerekiyor, eski badana dökülüyor.",
      "Duvar boyası solmuş, renk değişmiş.",
      "Tavan boyası dökülüyor, tavan çıplak kalıyor.",
      "Boya kokusu var, rahatsız edici.",
      "Sıva çatlağı var, duvar hasarlı.",
      "Duvar boyası kabarması var, düzeltilmesi gerekiyor.",
      "Tavan sıvası dökülüyor, tavan hasarlı.",
      "Boya kuruması var, boya düzgün kurumamış.",
      "Duvar boyası lekesi var, temizlenemiyor.",
      "Badana gerekiyor, eski badana dökülüyor.",
      "Sıva tamiri gerekiyor, duvar hasarlı.",
      "Boya yenileme gerekiyor, eski boya dökülüyor.",
      "Duvar onarımı gerekiyor, duvar hasarlı."
    ]
  },
  "Yapı": {
    titles: [
      "Duvar çatlağı var",
      "Tavan dökülüyor",
      "Çatı kaçağı",
      "Temel çatlağı",
      "Balkon hasarı",
      "Yapısal problem",
      "Merdiven hasarı",
      "Kapı bozuk",
      "Pencere arızalı",
      "Çatı hasarı",
      "Teras hasarı",
      "Garaj kapısı bozuk",
      "Bodrum su basması",
      "Yapısal çatlak",
      "Temel hasarı",
      "Duvar yıkılması",
      "Tavan çökmesi",
      "Çatı çökmesi",
      "Balkon çökmesi",
      "Yapısal bozulma"
    ],
    descriptions: [
      "Duvar çatlağı var, yapısal hasar oluşmuş.",
      "Tavan dökülüyor, yapısal problem var.",
      "Çatı kaçağı var, su damlıyor.",
      "Temel çatlağı var, yapısal hasar oluşmuş.",
      "Balkon hasarı var, güvenlik riski oluşmuş.",
      "Yapısal problem var, bina hasarlı.",
      "Merdiven hasarı var, kullanılamıyor.",
      "Kapı bozuk, açılıp kapanmıyor.",
      "Pencere arızalı, açılıp kapanmıyor.",
      "Çatı hasarı var, su kaçağı oluşmuş.",
      "Teras hasarı var, kullanılamıyor.",
      "Garaj kapısı bozuk, açılıp kapanmıyor.",
      "Bodrum su basması var, su birikiyor.",
      "Yapısal çatlak var, bina hasarlı.",
      "Temel hasarı var, yapısal problem oluşmuş.",
      "Duvar yıkılması var, tehlikeli durum.",
      "Tavan çökmesi var, tehlikeli durum.",
      "Çatı çökmesi var, tehlikeli durum.",
      "Balkon çökmesi var, tehlikeli durum.",
      "Yapısal bozulma var, bina hasarlı."
    ]
  },
  "Güvenlik": {
    titles: [
      "Kapı kilidi bozuk",
      "Güvenlik kamerası çalışmıyor",
      "Alarm sistemi arızalı",
      "Güvenlik kartı çalışmıyor",
      "Hırsız alarmı",
      "Yangın alarmı",
      "Gaz alarmı",
      "Güvenlik sistemi bozuk",
      "Kapı kilidi gevşek",
      "Pencere kilidi bozuk",
      "Güvenlik kartı kayıp",
      "Şifre çalışmıyor",
      "Parmak izi okuyucu bozuk",
      "Güvenlik görevlisi yok",
      "Güvenlik kapısı bozuk",
      "Güvenlik bariyeri arızalı",
      "Kamera görüntüsü yok",
      "Alarm sesi gelmiyor",
      "Güvenlik sistemi kapalı",
      "Güvenlik kartı arızalı"
    ],
    descriptions: [
      "Kapı kilidi bozuk, kapı açılamıyor.",
      "Güvenlik kamerası çalışmıyor, görüntü yok.",
      "Alarm sistemi arızalı, alarm çalmıyor.",
      "Güvenlik kartı çalışmıyor, kapı açılmıyor.",
      "Hırsız alarmı çalışmıyor, güvenlik riski var.",
      "Yangın alarmı çalışmıyor, güvenlik riski var.",
      "Gaz alarmı çalışmıyor, güvenlik riski var.",
      "Güvenlik sistemi bozuk, güvenlik riski var.",
      "Kapı kilidi gevşek, kapı kapanmıyor.",
      "Pencere kilidi bozuk, pencere kilitlenemiyor.",
      "Güvenlik kartı kayıp, kapı açılamıyor.",
      "Şifre çalışmıyor, kapı açılamıyor.",
      "Parmak izi okuyucu bozuk, kapı açılamıyor.",
      "Güvenlik görevlisi yok, güvenlik riski var.",
      "Güvenlik kapısı bozuk, açılıp kapanmıyor.",
      "Güvenlik bariyeri arızalı, çalışmıyor.",
      "Kamera görüntüsü yok, güvenlik riski var.",
      "Alarm sesi gelmiyor, güvenlik riski var.",
      "Güvenlik sistemi kapalı, güvenlik riski var.",
      "Güvenlik kartı arızalı, kapı açılamıyor."
    ]
  },
  "Isıtma/Soğutma": {
    titles: [
      "Kalorifer peteği çalışmıyor",
      "Klima arızalı",
      "Isıtma sistemi bozuk",
      "Nem problemi var",
      "Havalandırma çalışmıyor",
      "Sıcaklık ayarı",
      "Kalorifer sistemi bozuk",
      "Klima soğutmuyor",
      "Isıtma çalışmıyor",
      "Nem alma cihazı bozuk",
      "Havalandırma sistemi arızalı",
      "Kalorifer peteği soğuk",
      "Klima ısıtmıyor",
      "Isıtma sistemi kapalı",
      "Nem oranı yüksek",
      "Havalandırma kapalı",
      "Kalorifer peteği sıcak",
      "Klima çalışmıyor",
      "Isıtma sistemi arızalı",
      "Nem alma cihazı çalışmıyor"
    ],
    descriptions: [
      "Kalorifer peteği çalışmıyor, ev soğuk.",
      "Klima arızalı, soğutma yapmıyor.",
      "Isıtma sistemi bozuk, ev ısınmıyor.",
      "Nem problemi var, ev nemli.",
      "Havalandırma çalışmıyor, hava sirkülasyonu yok.",
      "Sıcaklık ayarı yapılamıyor, sıcaklık kontrol edilemiyor.",
      "Kalorifer sistemi bozuk, ısıtma yapmıyor.",
      "Klima soğutmuyor, sıcak hava geliyor.",
      "Isıtma çalışmıyor, ev soğuk.",
      "Nem alma cihazı bozuk, nem alınmıyor.",
      "Havalandırma sistemi arızalı, hava sirkülasyonu yok.",
      "Kalorifer peteği soğuk, ısıtma yapmıyor.",
      "Klima ısıtmıyor, soğuk hava geliyor.",
      "Isıtma sistemi kapalı, ev soğuk.",
      "Nem oranı yüksek, ev nemli.",
      "Havalandırma kapalı, hava sirkülasyonu yok.",
      "Kalorifer peteği sıcak, aşırı ısınıyor.",
      "Klima çalışmıyor, hiç çalışmıyor.",
      "Isıtma sistemi arızalı, ısıtma yapmıyor.",
      "Nem alma cihazı çalışmıyor, nem alınmıyor."
    ]
  },
  "Asansör": {
    titles: [
      "Asansör çalışmıyor",
      "Asansör takıldı",
      "Asansör kapısı açılmıyor",
      "Asansör düğmesi bozuk",
      "Asansör arızası",
      "Asansör bakımı",
      "Asansör kabini bozuk",
      "Asansör motoru arızalı",
      "Asansör halatı kopmuş",
      "Asansör freni bozuk",
      "Asansör ağırlık problemi",
      "Asansör hızı yavaş",
      "Asansör güvenlik sistemi bozuk",
      "Asansör kapısı kapanmıyor",
      "Asansör düğmesi çalışmıyor",
      "Asansör kabini sallanıyor",
      "Asansör motoru ses yapıyor",
      "Asansör halatı gevşek",
      "Asansör freni çalışmıyor",
      "Asansör ağırlık limiti aşıldı"
    ],
    descriptions: [
      "Asansör çalışmıyor, hiç hareket etmiyor.",
      "Asansör takıldı, katlar arası kaldı.",
      "Asansör kapısı açılmıyor, içeride kaldık.",
      "Asansör düğmesi bozuk, kat seçilemiyor.",
      "Asansör arızası var, kullanılamıyor.",
      "Asansör bakımı gerekiyor, düzenli bakım yapılmamış.",
      "Asansör kabini bozuk, kullanılamıyor.",
      "Asansör motoru arızalı, çalışmıyor.",
      "Asansör halatı kopmuş, tehlikeli durum.",
      "Asansör freni bozuk, güvenlik riski var.",
      "Asansör ağırlık problemi var, aşırı yük.",
      "Asansör hızı yavaş, yavaş hareket ediyor.",
      "Asansör güvenlik sistemi bozuk, güvenlik riski var.",
      "Asansör kapısı kapanmıyor, açık kalıyor.",
      "Asansör düğmesi çalışmıyor, kat seçilemiyor.",
      "Asansör kabini sallanıyor, tehlikeli durum.",
      "Asansör motoru ses yapıyor, gürültü var.",
      "Asansör halatı gevşek, tehlikeli durum.",
      "Asansör freni çalışmıyor, güvenlik riski var.",
      "Asansör ağırlık limiti aşıldı, aşırı yük."
    ]
  },
  "Çevre Düzenleme": {
    titles: [
      "Bahçe bakımı gerekiyor",
      "Çim biçimi",
      "Ağaç budama",
      "Çevre temizliği",
      "Peyzaj düzenleme",
      "Yeşil alan bakımı",
      "Yol tamiri",
      "Park düzenleme",
      "Otopark düzenleme",
      "Çöp toplama",
      "Temizlik hizmeti",
      "Spor alanı bakımı",
      "Oyun alanı düzenleme",
      "Yürüyüş yolu tamiri",
      "Bisiklet yolu düzenleme",
      "Çevre temizliği",
      "Bahçe sulama",
      "Peyzaj bakımı",
      "Yeşil alan sulama",
      "Çevre düzenleme"
    ],
    descriptions: [
      "Bahçe bakımı gerekiyor, çimler uzamış.",
      "Çim biçimi gerekiyor, çimler çok uzun.",
      "Ağaç budama gerekiyor, dallar uzamış.",
      "Çevre temizliği gerekiyor, çöp var.",
      "Peyzaj düzenleme gerekiyor, düzen bozuk.",
      "Yeşil alan bakımı gerekiyor, bakım yapılmamış.",
      "Yol tamiri gerekiyor, yol bozuk.",
      "Park düzenleme gerekiyor, park düzensiz.",
      "Otopark düzenleme gerekiyor, otopark düzensiz.",
      "Çöp toplama gerekiyor, çöp birikmiş.",
      "Temizlik hizmeti gerekiyor, temizlik yapılmamış.",
      "Spor alanı bakımı gerekiyor, spor alanı bozuk.",
      "Oyun alanı düzenleme gerekiyor, oyun alanı düzensiz.",
      "Yürüyüş yolu tamiri gerekiyor, yol bozuk.",
      "Bisiklet yolu düzenleme gerekiyor, yol bozuk.",
      "Çevre temizliği gerekiyor, çevre kirli.",
      "Bahçe sulama gerekiyor, bahçe kuru.",
      "Peyzaj bakımı gerekiyor, peyzaj bozuk.",
      "Yeşil alan sulama gerekiyor, alan kuru.",
      "Çevre düzenleme gerekiyor, çevre düzensiz."
    ]
  }
};

// Rastgele öğe seçme fonksiyonu
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Rastgele sayı üretme fonksiyonu
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Öncelik seviyeleri
const priorities = ["Düşük", "Normal", "Yüksek", "Kritik"];

// Training data oluşturma
function generateTrainingData() {
  const trainingData = [];
  let id = 1;

  // Her kategori için veri oluştur
  categoriesData.categories.forEach(category => {
    const categoryName = category.ad;
    const templates = faultTemplates[categoryName];
    
    if (!templates) {
      console.warn(`Templates not found for category: ${categoryName}`);
      return;
    }

    // Her kategori için 60-70 örnek oluştur (toplam ~500)
    const samplesPerCategory = Math.floor(500 / categoriesData.categories.length);
    
    for (let i = 0; i < samplesPerCategory; i++) {
      const title = getRandomElement(templates.titles);
      const description = getRandomElement(templates.descriptions);
      const priority = getRandomElement(priorities);
      
      // Rastgele varyasyonlar ekle
      const variations = [
        "acil",
        "sürekli",
        "sık sık",
        "bazen",
        "genellikle",
        "çoğu zaman",
        "nadiren",
        "hiç",
        "tamamen",
        "kısmen"
      ];
      
      const variation = getRandomElement(variations);
      const modifiedTitle = `${variation} ${title}`;
      const modifiedDescription = `${description} ${variation} oluyor.`;
      
      trainingData.push({
        id: id++,
        baslik: modifiedTitle,
        aciklama: modifiedDescription,
        kategori: categoryName,
        kategori_id: category.id,
        oncelik: priority,
        keywords: category.keywords,
        confidence: 0.85 + Math.random() * 0.15, // 0.85-1.0 arası
        created_at: new Date().toISOString()
      });
    }
  });

  return trainingData;
}

// Ana fonksiyon
function main() {
  console.log('🚀 Training data oluşturuluyor...');
  
  const trainingData = generateTrainingData();
  
  // Dosyaya yaz
  const outputPath = path.join(__dirname, '../data/training-data.json');
  fs.writeFileSync(outputPath, JSON.stringify(trainingData, null, 2), 'utf8');
  
  console.log(`✅ ${trainingData.length} örnek veri oluşturuldu`);
  console.log(`📁 Dosya kaydedildi: ${outputPath}`);
  
  // Kategori dağılımını göster
  const categoryDistribution = {};
  trainingData.forEach(item => {
    categoryDistribution[item.kategori] = (categoryDistribution[item.kategori] || 0) + 1;
  });
  
  console.log('\n📊 Kategori Dağılımı:');
  Object.entries(categoryDistribution).forEach(([category, count]) => {
    console.log(`  ${category}: ${count} örnek`);
  });
  
  console.log('\n🎯 Training data hazır!');
}

main(); 