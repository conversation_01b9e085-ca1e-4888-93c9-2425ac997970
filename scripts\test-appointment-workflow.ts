import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testAppointmentWorkflow() {
  console.log('🚀 Randevu İş Akışı Testi Başlıyor...\n');

  try {
    // 1. Mevcut bir proje, blok ve daire bul
    console.log('📋 1. <PERSON>je, Blok ve Daire Seçiliyor...');
    
    const project = await prisma.proje.findFirst({
      where: { silindi_mi: false },
      include: {
        bloklar: {
          where: { silindi_mi: false },
          include: {
            daireler: {
              where: { silindi_mi: false },
              take: 1
            }
          },
          take: 1
        }
      }
    });

    if (!project || !project.bloklar[0] || !project.bloklar[0].daireler[0]) {
      throw new Error('Test için proje, blok veya daire bulunamadı!');
    }

    const block = project.bloklar[0];
    const apartment = block.daireler[0];

    console.log(`✅ Proje: ${project.ad}`);
    console.log(`✅ Blok: ${block.ad}`);
    console.log(`✅ Daire: ${apartment.numara}\n`);

    // 2. Arıza tipi ve durumu bul
    console.log('📋 2. Arıza Tipi ve Durumu Seçiliyor...');
    
    const arizaTip = await prisma.arizaTip.findFirst({
      where: { silindi_mi: false }
    });

    const aciliyetSeviye = await prisma.aciliyetSeviye.findFirst({
      where: { silindi_mi: false }
    });

    const status = await prisma.arizaDurum.findFirst({
      where: { ad: 'Açık' }
    });

    if (!arizaTip || !aciliyetSeviye || !status) {
      throw new Error('Test için arıza tipi, aciliyet seviyesi veya durum bulunamadı!');
    }

    console.log(`✅ Arıza Tipi: ${arizaTip.ad}`);
    console.log(`✅ Aciliyet Seviyesi: ${aciliyetSeviye.ad}`);
    console.log(`✅ Durum: ${status.ad}\n`);

    // 3. Arıza kaydı oluştur
    console.log('📋 3. Arıza Kaydı Oluşturuluyor...');
    
    const faultTitle = `Test Arızası - Su Kaçağı ${Date.now()}`;
    const faultDescription = 'Test amaçlı oluşturulan arıza kaydı. Otomatik test scripti tarafından oluşturuldu.';

    const fault = await prisma.ariza.create({
      data: {
        baslik: faultTitle,
        aciklama: faultDescription,
        slug: `test-ariza-${Date.now()}`,
        daire_id: apartment.id,
        ariza_tip_id: arizaTip.id,
        aciliyet_id: aciliyetSeviye.id,
        durum_id: status.id,
        bildiren_ad_soyad: 'Test Kullanıcı',
        bildiren_telefon: '0555 123 45 67',
        olusturan_id: 'admin-user-id', // Admin kullanıcı ID'si
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date()
      }
    });

    console.log(`✅ Arıza oluşturuldu: ${fault.baslik}`);
    console.log(`✅ Arıza ID: ${fault.id}\n`);

    // 4. Teknisyen bul (User tablosundan)
    console.log('📋 4. Teknisyen Seçiliyor...');
    
    const technician = await prisma.user.findFirst({
      where: { 
        silindi_mi: false,
        rol: 'TECHNICIAN'
      }
    });

    if (!technician) {
      throw new Error('Test için teknisyen bulunamadı!');
    }

    console.log(`✅ Teknisyen: ${technician.ad} ${technician.soyad}\n`);

    // 5. Randevu oluştur
    console.log('📋 5. Randevu Oluşturuluyor...');
    
    const appointmentDate = new Date();
    appointmentDate.setHours(appointmentDate.getHours() + 1); // 1 saat sonra

    const appointment = await prisma.randevu.create({
      data: {
        randevu_tarihi: appointmentDate,
        durum: 'PLANLI',
        aciklama: 'Test randevusu - otomatik oluşturuldu',
        ariza_id: fault.id,
        olusturan_id: 'admin-user-id',
        olusturulma_tarihi: new Date(),
        guncelleme_tarihi: new Date()
      }
    });

    console.log(`✅ Randevu oluşturuldu: ${appointment.id}`);
    console.log(`✅ Randevu tarihi: ${appointmentDate.toLocaleString('tr-TR')}\n`);

    // 6. Teknisyeni randevuya ata
    console.log('📋 6. Teknisyen Randevuya Atanıyor...');
    
    await prisma.randevuTeknisyen.create({
      data: {
        randevu_id: appointment.id,
        teknisyen_id: technician.id
      }
    });

    console.log(`✅ Teknisyen randevuya atandı\n`);

    // 7. Arıza altındaki aktif randevuları kontrol et
    console.log('📋 7. Arıza Altındaki Aktif Randevular Kontrol Ediliyor...');
    
    const activeAppointments = await prisma.randevu.count({
      where: {
        ariza_id: fault.id,
        id: { not: appointment.id },
        durum: { in: ['PLANLI', 'DEVAM_EDIYOR'] },
        silindi_mi: false
      }
    });

    console.log(`✅ Arıza altında ${activeAppointments} aktif randevu var`);
    
    if (activeAppointments === 0) {
      console.log('🎯 Arıza kapatma önerisi gösterilmeli!');
    } else {
      console.log('⚠️ Arıza kapatma önerisi gösterilmemeli!');
    }

    // 8. Test sonuçlarını özetle
    console.log('\n📊 TEST SONUÇLARI:');
    console.log('==================');
    console.log(`📍 Konum: ${project.ad} > ${block.ad} > Daire ${apartment.numara}`);
    console.log(`🔧 Arıza: ${fault.baslik} (ID: ${fault.id})`);
    console.log(`📅 Randevu: ${appointment.id} - ${appointmentDate.toLocaleString('tr-TR')}`);
    console.log(`👨‍🔧 Teknisyen: ${technician.ad} ${technician.soyad}`);
    console.log(`📈 Aktif Randevu Sayısı: ${activeAppointments}`);
    console.log(`🎯 Modal Gösterilmeli: ${activeAppointments === 0 ? 'EVET' : 'HAYIR'}`);

    console.log('\n✅ Test tamamlandı!');
    console.log('\n🔗 Test etmek için:');
    console.log(`   Randevu detayı: /projeler/${project.slug}/bloklar/${block.slug}/${apartment.slug}/arizalar/${fault.id}/randevular/${appointment.id}`);

  } catch (error) {
    console.error('❌ Test sırasında hata oluştu:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Scripti çalıştır
testAppointmentWorkflow(); 