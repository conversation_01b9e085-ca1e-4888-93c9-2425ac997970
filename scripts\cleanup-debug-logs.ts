#!/usr/bin/env node

/**
 * Production Debug Log Cleaner
 * Bu script production build öncesi debug kodlarını temizler
 */

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs'
import { join, extname } from 'path'

interface CleanupResult {
  file: string
  removedLogs: number
  removedErrors: number
}

const PRODUCTION_ONLY_LOGS = [
  // Development ve debug amaçlı log'lar
  /console\.log\([^)]*\);?\s*$/gm,
  /console\.debug\([^)]*\);?\s*$/gm,
  /console\.info\([^)]*\);?\s*$/gm,
  
  // Development auth bypass comments
  /\/\/\s*Temporarily disable auth.*$/gm,
  /\/\/\s*TODO:\s*Remove.*debug.*$/gm,
  /\/\/\s*Development.*only.*$/gm,
]

const KEEP_LOGS = [
  // Production'da saklanacak log'lar
  /console\.error\(/,
  /console\.warn\(/,
  // Error handling için gerekli log'lar
  /console\.error\(".*error.*"/i,
  /console\.warn\(".*warning.*"/i,
]

function shouldKeepLog(line: string): boolean {
  return KEEP_LOGS.some(pattern => pattern.test(line))
}

function cleanupFile(filePath: string): CleanupResult {
  const content = readFileSync(filePath, 'utf-8')
  const lines = content.split('\n')
  
  let removedLogs = 0
  let removedErrors = 0
  
  const cleanedLines = lines.filter(line => {
    const trimmedLine = line.trim()
    
    // Boş satırları koru
    if (!trimmedLine) return true
    
    // Production'da saklanacak log'ları kontrol et
    if (shouldKeepLog(trimmedLine)) return true
    
    // Debug log'ları temizle
    for (const pattern of PRODUCTION_ONLY_LOGS) {
      if (pattern.test(trimmedLine)) {
        if (trimmedLine.includes('console.log')) removedLogs++
        if (trimmedLine.includes('console.error') && !shouldKeepLog(trimmedLine)) removedErrors++
        return false
      }
    }
    
    return true
  })
  
  // Dosyayı güncelle
  writeFileSync(filePath, cleanedLines.join('\n'))
  
  return {
    file: filePath,
    removedLogs,
    removedErrors
  }
}

function findTypeScriptFiles(dir: string): string[] {
  const files: string[] = []
  
  try {
    const items = readdirSync(dir)
    
    for (const item of items) {
      const fullPath = join(dir, item)
      const stat = statSync(fullPath)
      
      if (stat.isDirectory()) {
        // node_modules klasörünü atla
        if (item !== 'node_modules') {
          files.push(...findTypeScriptFiles(fullPath))
        }
      } else if (stat.isFile()) {
        const ext = extname(item)
        if (ext === '.ts' || ext === '.tsx') {
          files.push(fullPath)
        }
      }
    }
  } catch (error) {
    // Klasör okunamazsa sessizce atla
  }
  
  return files
}

async function main() {
  console.log('🧹 Production Debug Log Cleaner başlatılıyor...')
  
  try {
    // TypeScript ve React dosyalarını bul
    const files = findTypeScriptFiles('src')
    
    console.log(`📁 ${files.length} dosya bulundu`)
    
    const results: CleanupResult[] = []
    let totalRemovedLogs = 0
    let totalRemovedErrors = 0
    
    for (const file of files) {
      const result = cleanupFile(file)
      if (result.removedLogs > 0 || result.removedErrors > 0) {
        results.push(result)
        totalRemovedLogs += result.removedLogs
        totalRemovedErrors += result.removedErrors
      }
    }
    
    console.log('\n📊 Temizlik Raporu:')
    console.log(`🗑️  Toplam kaldırılan console.log: ${totalRemovedLogs}`)
    console.log(`⚠️  Toplam kaldırılan debug console.error: ${totalRemovedErrors}`)
    console.log(`📄 Etkilenen dosya sayısı: ${results.length}`)
    
    if (results.length > 0) {
      console.log('\n📝 Detaylı Rapor:')
      results.forEach(result => {
        console.log(`  ${result.file}: ${result.removedLogs} log, ${result.removedErrors} error`)
      })
    }
    
    console.log('\n✅ Debug log temizliği tamamlandı!')
    
  } catch (error) {
    console.error('❌ Hata:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
} 