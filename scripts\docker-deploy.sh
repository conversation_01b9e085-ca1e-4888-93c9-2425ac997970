#!/bin/bash

echo "🐳 Bakım Onarım Sistemi - Development Docker Deployment Başlıyor..."

# Environment kontrolü
if [ ! -f .env.local ]; then
    echo "⚠️  .env.local dosyası bulunamadı!"
    echo "📝 Örnek .env.local dosyası oluşturuluyor..."
    cat > .env.local << EOF
# Database
DATABASE_URL="postgresql://admin:admin123@localhost:5433/bakimonarim"

# NextAuth
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET="your-secret-key-here"

# ML Service
ML_SERVICE_URL="http://localhost:3050"

# App
NEXT_PUBLIC_APP_URL="http://localhost:3001"
EOF
    echo "✅ .env.local dosyası oluşturuldu!"
    echo "⚠️  Lütfen NEXTAUTH_SECRET değerini güncelleyin!"
fi

# Docker Compose ile sadece ML servisi ve PostgreSQL'i başlat
echo "🚀 Docker Compose ile ML servisi ve PostgreSQL başlatılıyor..."
echo "📝 Not: Ana proje development modunda çalışacak (npm run dev)"
docker-compose up -d

if [ $? -eq 0 ]; then
    echo "✅ ML servisi ve PostgreSQL başarıyla başlatıldı!"
    echo ""
    echo "📊 Servis Durumları:"
    echo "   🗄️  PostgreSQL: http://localhost:5433"
    echo "   🤖 ML Service: http://localhost:3050"
    echo ""
    echo "🚀 Ana projeyi başlatmak için:"
    echo "   npm run dev"
    echo "   🌐 Web App: http://localhost:3001"
    echo ""
    echo "📝 Logları görüntülemek için:"
    echo "   docker-compose logs -f"
    echo ""
    echo "🛑 Durdurmak için:"
    echo "   docker-compose down"
    echo ""
    echo "🔧 ML Service logları:"
    echo "   docker logs bakimonarim_ml_service -f"
else
    echo "❌ Docker Compose hatası!"
    exit 1
fi 