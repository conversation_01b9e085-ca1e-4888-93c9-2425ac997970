import redis from './redis'

// Cache TTL constants
export const CACHE_TTL = {
  STATS: 300,        // 5 minutes
  USERS: 600,        // 10 minutes
  PROJECTS: 1800,    // 30 minutes
  CATEGORIES: 3600,  // 1 hour
  TECHNICIANS: 900,  // 15 minutes
  STATIC: 3600,      // 1 hour
  DEFAULT: 300,      // 5 minutes
} as const

// Cache key prefixes
export const CACHE_PREFIXES = {
  API: 'api',
  USERS: 'api:users',
  FAULTS: 'api:faults',
  PROJECTS: 'api:projects',
  CATEGORIES: 'api:categories',
  TECHNICIANS: 'api:technicians',
  STATS: 'api:stats',
  TAGS: 'tag',
} as const

/**
 * Check if Redis is ready for operations
 */
function isRedisReady(): boolean {
  return redis.status === 'ready'
}

/**
 * Wait for Redis to be ready with timeout
 */
async function waitForRedis(timeoutMs: number = 3000): Promise<boolean> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeoutMs) {
    if (isRedisReady()) {
      return true
    }
    
    // If <PERSON><PERSON> is disconnected, try to connect
    if (redis.status === 'end' || redis.status === 'close') {
      try {
        await redis.connect()
      } catch (error) {
        // Ignore connection errors, keep waiting
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  return false
}

export class CacheManager {
  /**
   * Get value from cache
   */
  static async get<T>(key: string): Promise<T | null> {
    try {
      // Check Redis readiness
      if (!isRedisReady()) {
        const ready = await waitForRedis(1000) // Wait max 1 second
        if (!ready) {
          console.warn(`Cache get skipped - Redis not ready for key: ${key}`)
          return null
        }
      }

      const value = await redis.get(key)
      if (value === null) return null
      
      return JSON.parse(value) as T
    } catch (error) {
      // Check for specific connection errors
      if (error instanceof Error && 
          (error.message.includes("isn't writeable") || 
           error.message.includes("ECONNREFUSED") ||
           error.message.includes("Connection is closed"))) {
        console.warn(`Cache get skipped - Redis connection issue for key ${key}`)
        return null
      }
      
      console.warn(`Cache get error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set value in cache with optional TTL
   */
  static async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      // Check Redis readiness
      if (!isRedisReady()) {
        const ready = await waitForRedis(1000) // Wait max 1 second
        if (!ready) {
          console.warn(`Cache set skipped - Redis not ready for key: ${key}`)
          return
        }
      }

      const serializedValue = JSON.stringify(value)
      if (ttl) {
        await redis.setex(key, ttl, serializedValue)
      } else {
        await redis.set(key, serializedValue)
      }
    } catch (error) {
      // Check for specific connection errors
      if (error instanceof Error && 
          (error.message.includes("isn't writeable") || 
           error.message.includes("ECONNREFUSED") ||
           error.message.includes("Connection is closed"))) {
        console.warn(`Cache set skipped - Redis connection issue for key ${key}`)
        return
      }
      
      console.warn(`Cache set error for key ${key}:`, error)
    }
  }

  /**
   * Delete a specific key
   */
  static async delete(key: string): Promise<void> {
    try {
      if (!isRedisReady()) {
        const ready = await waitForRedis(1000)
        if (!ready) {
          console.warn(`Cache delete skipped - Redis not ready for key: ${key}`)
          return
        }
      }

      await redis.del(key)
    } catch (error) {
      if (error instanceof Error && 
          (error.message.includes("isn't writeable") || 
           error.message.includes("ECONNREFUSED") ||
           error.message.includes("Connection is closed"))) {
        console.warn(`Cache delete skipped - Redis connection issue for key ${key}`)
        return
      }
      
      console.warn(`Cache delete error for key ${key}:`, error)
    }
  }

  /**
   * Delete multiple keys by pattern
   */
  static async deletePattern(pattern: string): Promise<void> {
    try {
      if (!isRedisReady()) {
        const ready = await waitForRedis(1000)
        if (!ready) {
          console.warn(`Cache delete pattern skipped - Redis not ready for pattern: ${pattern}`)
          return
        }
      }

      const keys = await redis.keys(pattern)
      if (keys.length > 0) {
        await redis.del(...keys)
        console.log(`Deleted ${keys.length} cache keys matching pattern: ${pattern}`)
      }
    } catch (error) {
      if (error instanceof Error && 
          (error.message.includes("isn't writeable") || 
           error.message.includes("ECONNREFUSED") ||
           error.message.includes("Connection is closed"))) {
        console.warn(`Cache delete pattern skipped - Redis connection issue for pattern ${pattern}`)
        return
      }
      
      console.warn(`Cache delete pattern error for pattern ${pattern}:`, error)
    }
  }

  /**
   * Generate cache key with parameters
   */
  static generateKey(prefix: string, params: Record<string, any> = {}): string {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join(':')
    
    return sortedParams ? `${prefix}:${sortedParams}` : prefix
  }

  /**
   * Invalidate cache by tag
   */
  static async invalidateByTag(tag: string): Promise<void> {
    const pattern = `${CACHE_PREFIXES.TAGS}:${tag}:*`
    await this.deletePattern(pattern)
  }

  /**
   * Get cache statistics
   */
  static async getStats(): Promise<{
    totalKeys: number
    memoryUsage: string
    hitRate?: number
  }> {
    try {
      if (!isRedisReady()) {
        return {
          totalKeys: 0,
          memoryUsage: 'Redis not ready',
        }
      }

      const info = await redis.info('memory')
      const keys = await redis.dbsize()
      
      // Parse memory usage from info
      const memoryMatch = info.match(/used_memory_human:(\S+)/)
      const memoryUsage = memoryMatch ? memoryMatch[1] : 'unknown'
      
      return {
        totalKeys: keys,
        memoryUsage,
      }
    } catch (error) {
      console.warn('Cache stats error:', error)
      return {
        totalKeys: 0,
        memoryUsage: 'error',
      }
    }
  }

  /**
   * Clear all cache
   */
  static async clearAll(): Promise<void> {
    try {
      if (!isRedisReady()) {
        const ready = await waitForRedis(2000)
        if (!ready) {
          console.warn('Cache clear skipped - Redis not ready')
          return
        }
      }

      await redis.flushdb()
      console.log('All cache cleared')
    } catch (error) {
      console.warn('Cache clear error:', error)
    }
  }

  /**
   * Health check
   */
  static async healthCheck(): Promise<boolean> {
    try {
      if (!isRedisReady()) {
        return false
      }
      
      await redis.ping()
      return true
    } catch (error) {
      console.error('Cache health check failed:', error)
      return false
    }
  }
}

// Convenience functions for common cache operations
export const cache = {
  /**
   * Cache API response
   */
  async apiResponse<T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl: number = CACHE_TTL.DEFAULT
  ): Promise<T> {
    // Try to get from cache first
    const cached = await CacheManager.get<T>(key)
    if (cached !== null) {
      console.log(`Cache hit for key: ${key}`)
      return cached
    }

    // Fetch from source
    console.log(`Cache miss for key: ${key}`)
    const data = await fetchFn()
    
    // Store in cache
    await CacheManager.set(key, data, ttl)
    
    return data
  },

  /**
   * Invalidate related caches
   */
  async invalidateRelated(tags: string[]): Promise<void> {
    await Promise.all(
      tags.map(tag => CacheManager.invalidateByTag(tag))
    )
  },

  /**
   * Generate API cache key
   */
  apiKey(endpoint: string, params: Record<string, any> = {}): string {
    return CacheManager.generateKey(`api:${endpoint}`, params)
  },
}

// Export default instance
export default CacheManager 