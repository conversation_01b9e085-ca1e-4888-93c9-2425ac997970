# 🚀 Development Kurulum Kılavuzu

Bu kılavuz, Bakım Onarım Sistemi'ni development modunda nasıl çalıştıracağınızı açıklar.

## 📋 Sistem Mimarisi (Development)

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web App       │    │   ML Service    │    │   PostgreSQL    │
│   (Port 3001)   │◄──►│   (Port 3050)   │    │   (Port 5433)   │
│   Next.js       │    │   Express.js    │    │   Database      │
│   (Local)       │    │   (Docker)      │    │   (Docker)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Hızlı Başlangıç

### 1. Gereksinimler
- Node.js 18+
- Docker & Docker Compose
- Git

### 2. <PERSON><PERSON>yi <PERSON>ı<PERSON>
```bash
git clone <repository-url>
cd bakimonarim
```

### 3. ML Servisi ve PostgreSQL'i Başlatın
```bash
# Docker ile ML servisi ve PostgreSQL'i başlat
npm run docker:deploy
```

Bu komut:
- PostgreSQL veritabanını başlatır
- M<PERSON> servisini build eder ve başlatır
- `.env.local` dosyasını oluşturur

### 4. Ana Projeyi Başlatın
```bash
# Bağımlılıkları yükleyin
npm install

# Development modunda başlatın
npm run dev
```

### 5. Veritabanını Hazırlayın
```bash
# Migration'ları çalıştırın
npm run db:migrate

# Seed verilerini yükleyin
npm run db:seed
```

## 📊 Servis URL'leri

| Servis | URL | Durum |
|--------|-----|-------|
| Web App | http://localhost:3001 | Development (npm run dev) |
| ML Service | http://localhost:3050 | Docker Container |
| PostgreSQL | localhost:5433 | Docker Container |

## 🔧 Yönetim Komutları

### Docker Servisleri
```bash
# ML servisi ve PostgreSQL'i başlat
npm run docker:up

# ML servisi ve PostgreSQL'i durdur
npm run docker:down

# Logları izle
npm run docker:logs
npm run docker:ml-logs
```

### Ana Proje
```bash
# Development modunda başlat
npm run dev

# Build
npm run build

# Production modunda başlat
npm run start
```

### Veritabanı
```bash
# Migration'ları çalıştır
npm run db:migrate

# Seed verilerini yükle
npm run db:seed

# Prisma client'ı yenile
npm run db:generate
```

## 🔍 Test ve Kontrol

### Health Check
```bash
# ML servisi durumu
curl http://localhost:3050/health

# Web app ML API'si
curl http://localhost:3001/api/ml-predict
```

### Veritabanı Bağlantısı
```bash
# PostgreSQL'e bağlan
docker exec -it bakimonarim_postgres psql -U admin -d bakimonarim
```

## 🐛 Sorun Giderme

### ML Servisi Başlamıyor
```bash
# Container durumunu kontrol et
docker ps | grep ml-fault-classifier

# Logları kontrol et
docker logs bakimonarim_ml_service

# Container'ı yeniden başlat
docker restart bakimonarim_ml_service
```

### Veritabanı Bağlantı Hatası
```bash
# PostgreSQL durumunu kontrol et
docker ps | grep postgres

# Veritabanını yeniden başlat
docker restart bakimonarim_postgres
```

### Port Çakışması
```bash
# Kullanılan portları kontrol et
netstat -tulpn | grep :3050
netstat -tulpn | grep :3001
netstat -tulpn | grep :5433
```

## 🔄 Güncelleme

### ML Servisi Güncelleme
```bash
# Git pull
git pull origin main

# Docker Compose ile yeniden build
docker-compose down
docker-compose up -d --build
```

### Ana Proje Güncelleme
```bash
# Git pull
git pull origin main

# Bağımlılıkları güncelle
npm install

# Development modunda yeniden başlat
npm run dev
```

## 📝 Environment Değişkenleri

### .env.local
```env
# Database
DATABASE_URL="postgresql://admin:admin123@localhost:5433/bakimonarim"

# NextAuth
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET="your-secret-key-here"

# ML Service
ML_SERVICE_URL="http://localhost:3050"

# App
NEXT_PUBLIC_APP_URL="http://localhost:3001"
```

## 🎯 Geliştirme İpuçları

### Hot Reload
- Ana proje Next.js hot reload ile çalışır
- ML servisi değişikliklerinde container'ı yeniden başlatın
- Veritabanı değişikliklerinde migration çalıştırın

### Debug
```bash
# ML servisi logları
npm run docker:ml-logs

# Ana proje logları (terminal'de görünür)
npm run dev
```

### Performance
- ML servisi Docker'da izole çalışır
- Ana proje local'de çalışır (daha hızlı)
- Veritabanı Docker'da çalışır

## 🆘 Destek

Sorun yaşarsanız:
1. Logları kontrol edin: `npm run docker:logs`
2. Container durumlarını kontrol edin: `docker ps`
3. Network bağlantılarını test edin: `docker network ls`
4. Environment dosyasını kontrol edin: `.env.local` 