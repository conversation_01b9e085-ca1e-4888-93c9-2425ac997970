"use client"

import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  Wrench, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  TrendingUp,
  Activity,
  Users,
  Building
} from "lucide-react"

const fetcher = async () => {
  const res = await fetch("/api/faults/stats")
  return res.json()
}

const projectsFetcher = async () => {
  const res = await fetch("/api/projects")
  return res.json()
}

const usersFetcher = async () => {
  const res = await fetch("/api/users")
  return res.json()
}

function DashboardOverview() {
  const { data: faultStats, isLoading: faultsLoading } = useQuery({
    queryKey: ["faults", "stats"],
    queryFn: fetcher
  })

  const { data: projectsData, isLoading: projectsLoading } = useQuery({
    queryKey: ["projects"],
    queryFn: projectsFetcher
  })

  const { data: usersData, isLoading: usersLoading } = useQuery({
    queryKey: ["users"],
    queryFn: usersFetcher
  })

  if (faultsLoading || projectsLoading || usersLoading || !faultStats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-32 bg-white/50 rounded-xl animate-pulse"></div>
        ))}
      </div>
    )
  }

  const stats = [
    {
      title: "Toplam Arıza",
      value: faultStats.toplam,
      change: `${(faultStats.yuzdelik || 0) >= 0 ? '+' : ''}${(faultStats.yuzdelik || 0).toFixed(1)}%`,
      trend: (faultStats.yuzdelik || 0) >= 0 ? "up" : "down",
      icon: Wrench,
      gradient: "from-blue-500 to-cyan-500",
      bgGradient: "from-blue-50 to-cyan-50",
      description: "Sistemdeki toplam arıza"
    },
    {
      title: "Beklemede",
      value: faultStats.beklemede,
      change: "+12%",
      trend: "up",
      icon: Clock,
      gradient: "from-amber-500 to-orange-500",
      bgGradient: "from-amber-50 to-orange-50",
      description: "Atanmayı bekleyen arızalar"
    },
    {
      title: "Devam Ediyor",
      value: faultStats.devamEdiyor,
      change: "-8%",
      trend: "down",
      icon: Activity,
      gradient: "from-purple-500 to-pink-500",
      bgGradient: "from-purple-50 to-pink-50",
      description: "Çözüm aşamasında"
    },
    {
      title: "Tamamlandı",
      value: faultStats.tamamlandi,
      change: "+18%",
      trend: "up",
      icon: CheckCircle,
      gradient: "from-green-500 to-emerald-500",
      bgGradient: "from-green-50 to-emerald-50",
      description: "Başarıyla çözülen"
    }
  ]

  const additionalStats = [
    {
      title: "Aktif Projeler",
      value: projectsData?.projects?.length || 0,
      change: "+2",
      trend: "up",
      icon: Building,
      gradient: "from-indigo-500 to-purple-500",
      bgGradient: "from-indigo-50 to-purple-50",
      description: "Yönetilen proje sayısı"
    },
    {
      title: "Toplam Kullanıcı",
      value: usersData?.users?.length || 0,
      change: "+5",
      trend: "up",
      icon: Users,
      gradient: "from-teal-500 to-cyan-500",
      bgGradient: "from-teal-50 to-cyan-50",
      description: "Sistem kullanıcıları"
    },
    {
      title: "Bu Ay Açılan",
      value: faultStats.buAy,
      change: `${(faultStats.yuzdelik || 0) >= 0 ? '+' : ''}${(faultStats.yuzdelik || 0).toFixed(1)}%`,
      trend: (faultStats.yuzdelik || 0) >= 0 ? "up" : "down",
      icon: TrendingUp,
      gradient: "from-rose-500 to-pink-500",
      bgGradient: "from-rose-50 to-pink-50",
      description: "Bu ay bildirilen"
    },
    {
      title: "İptal Edilen",
      value: faultStats.iptal,
      change: "-3%",
      trend: "down",
      icon: AlertTriangle,
      gradient: "from-gray-500 to-slate-500",
      bgGradient: "from-gray-50 to-slate-50",
      description: "İptal edilen arızalar"
    }
  ]

  const allStats = [...stats, ...additionalStats]

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card 
              key={stat.title}
              className={`relative overflow-hidden border-0 shadow-lg bg-gradient-to-br ${stat.bgGradient} hover:shadow-xl transition-all duration-300`}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg bg-gradient-to-r ${stat.gradient}`}>
                  <Icon className="h-4 w-4 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">
                      {stat.value || 0}
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      {stat.description}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {stat.trend === "up" ? (
                      <ArrowUpIcon className="h-4 w-4 text-green-500" />
                    ) : (
                      <ArrowDownIcon className="h-4 w-4 text-red-500" />
                    )}
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${
                        stat.trend === "up" ? "text-green-700 bg-green-100" : "text-red-700 bg-red-100"
                      }`}
                    >
                      {stat.change}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {additionalStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card 
              key={stat.title}
              className={`relative overflow-hidden border-0 shadow-lg bg-gradient-to-br ${stat.bgGradient} hover:shadow-xl transition-all duration-300`}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg bg-gradient-to-r ${stat.gradient}`}>
                  <Icon className="h-4 w-4 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">
                      {stat.value || 0}
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      {stat.description}
                    </p>
                  </div>
                  <div className="flex items-center space-x-1">
                    {stat.trend === "up" ? (
                      <ArrowUpIcon className="h-4 w-4 text-green-500" />
                    ) : (
                      <ArrowDownIcon className="h-4 w-4 text-red-500" />
                    )}
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${
                        stat.trend === "up" ? "text-green-700 bg-green-100" : "text-red-700 bg-red-100"
                      }`}
                    >
                      {stat.change}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}

export default DashboardOverview 