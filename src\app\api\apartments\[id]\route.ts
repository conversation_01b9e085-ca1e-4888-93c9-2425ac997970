import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

// Validation schema for apartment updates
const updateApartmentSchema = z.object({
  numara: z.string().min(1, "Daire numarası gereklidir").max(20, "Daire numarası çok uzun").optional(),
  kat: z.number().int().optional(),
  tipi: z.string().optional(),
  metrekare: z.number().positive().optional(),
  malik_ad: z.string().optional(),
  malik_telefon: z.string().optional(),
  malik_email: z.string().email("Geçerli bir email adresi gereklidir").optional().or(z.literal("")),
  aciklama: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    const apartment = await prisma.daire.findFirst({
      where: {
        id,
        silindi_mi: false,
      },
      include: {
        blok: {
          select: {
            id: true,
            ad: true,
            proje: {
              select: {
                id: true,
                ad: true,
              },
            },
          },
        },
        _count: {
          select: {
            arizalar: {
              where: { silindi_mi: false },
            },
          },
        },
      },
    })

    if (!apartment) {
      return NextResponse.json(
        { message: "Daire bulunamadı" },
        { status: 404 }
      )
    }

    return NextResponse.json(apartment)
  } catch (error) {
    console.error("Error fetching apartment:", error)
    return NextResponse.json(
      { message: "Daire getirilirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Validate input
    const validation = updateApartmentSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        { 
          message: "Geçersiz veri",
          errors: validation.error.format(),
        },
        { status: 400 }
      )
    }

    // Check if apartment exists
    const existingApartment = await prisma.daire.findFirst({
      where: {
        id,
        silindi_mi: false,
      },
    })

    if (!existingApartment) {
      return NextResponse.json(
        { message: "Daire bulunamadı" },
        { status: 404 }
      )
    }

    const { numara, kat, tipi, metrekare, malik_ad, malik_telefon, malik_email, aciklama } = validation.data

    // If apartment number is being updated, check for duplicates in the same block
    if (numara && numara !== existingApartment.numara) {
      const duplicateApartment = await prisma.daire.findFirst({
        where: {
          numara,
          blok_id: existingApartment.blok_id,
          silindi_mi: false,
          id: { not: id },
        },
      })

      if (duplicateApartment) {
        return NextResponse.json(
          { message: "Bu blokta aynı numarada bir daire zaten mevcut" },
          { status: 409 }
        )
      }
    }

    // Update the apartment
    const updatedApartment = await prisma.daire.update({
      where: { id },
      data: {
        ...(numara && { numara }),
        ...(kat !== undefined && { kat }),
        ...(tipi !== undefined && { tipi }),
        ...(metrekare !== undefined && { metrekare }),
        ...(malik_ad !== undefined && { malik_ad }),
        ...(malik_telefon !== undefined && { malik_telefon }),
        ...(malik_email !== undefined && { malik_email: malik_email || null }),
        ...(aciklama !== undefined && { aciklama }),
        guncelleme_tarihi: new Date(),
      },
      include: {
        blok: {
          select: {
            id: true,
            ad: true,
            proje: {
              select: {
                id: true,
                ad: true,
              },
            },
          },
        },
        _count: {
          select: {
            arizalar: {
              where: { silindi_mi: false },
            },
          },
        },
      },
    })

    return NextResponse.json(updatedApartment)
  } catch (error) {
    console.error("Error updating apartment:", error)
    return NextResponse.json(
      { message: "Daire güncellenirken hata oluştu" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Check if apartment exists
    const existingApartment = await prisma.daire.findFirst({
      where: {
        id,
        silindi_mi: false,
      },
    })

    if (!existingApartment) {
      return NextResponse.json(
        { message: "Daire bulunamadı" },
        { status: 404 }
      )
    }

    // Check if apartment has faults - prevent deletion if there are active faults
    const faultCount = await prisma.ariza.count({
      where: {
        daire_id: id,
        silindi_mi: false,
      },
    })

    if (faultCount > 0) {
      return NextResponse.json(
        { 
          message: `Bu daire silinemez. ${faultCount} adet aktif arıza bulunuyor. Önce arızaları tamamlamalı veya silmelisiniz.`,
        },
        { status: 409 }
      )
    }

    // Soft delete the apartment
    await prisma.daire.update({
      where: { id },
      data: {
        silindi_mi: true,
        guncelleme_tarihi: new Date(),
      },
    })

    return NextResponse.json(
      { message: "Daire başarıyla silindi" },
      { status: 200 }
    )
  } catch (error) {
    console.error("Error deleting apartment:", error)
    return NextResponse.json(
      { message: "Daire silinirken hata oluştu" },
      { status: 500 }
    )
  }
}
