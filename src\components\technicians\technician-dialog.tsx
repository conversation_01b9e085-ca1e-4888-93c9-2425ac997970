import { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"

interface ExpertiseArea {
  id: string;
  ad: string;
  renk: string;
}

interface TechnicianDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  initialData?: any;
}

interface ExpertiseSelection {
  id: string;
  seviye: 'BASLANGIC' | 'ORTA' | 'UZMAN';
}

export function TechnicianDialog({ open, onClose, onSave, initialData }: TechnicianDialogProps) {
  const [ad, setAd] = useState(initialData?.ad || "")
  const [soyad, setSoyad] = useState(initialData?.soyad || "")
  const [telefon, setTelefon] = useState(initialData?.telefon || "")
  const [aktif, setAktif] = useState(initialData?.aktif ?? true)
  const [selectedExpertise, setSelectedExpertise] = useState<ExpertiseSelection[]>(initialData?.uzmanliklar || [])
  const [expertiseAreas, setExpertiseAreas] = useState<ExpertiseArea[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    fetch("/api/expertise-areas")
      .then(res => res.json())
      .then(data => setExpertiseAreas(data))
  }, [])

  useEffect(() => {
    if (initialData) {
      setAd(initialData.ad || "")
      setSoyad(initialData.soyad || "")
      setTelefon(initialData.telefon || "")
      setAktif(initialData.aktif ?? true)
      setSelectedExpertise(initialData.uzmanliklar || [])
    }
  }, [initialData])

  const handleSave = () => {
    setLoading(true)
    onSave({ 
      ad, 
      soyad, 
      telefon, 
      aktif, 
      uzmanliklar: selectedExpertise 
    })
    setLoading(false)
  }

  const toggleExpertise = (id: string, seviye: 'BASLANGIC' | 'ORTA' | 'UZMAN' = 'BASLANGIC') => {
    setSelectedExpertise(prev => {
      const exists = prev.find(x => x.id === id)
      if (exists) {
        // Eğer varsa çıkar
        return prev.filter(x => x.id !== id)
      } else {
        // Eğer yoksa ekle
        return [...prev, { id, seviye }]
      }
    })
  }
  
  const updateExpertiseLevel = (id: string, seviye: 'BASLANGIC' | 'ORTA' | 'UZMAN') => {
    setSelectedExpertise(prev => 
      prev.map(x => x.id === id ? { ...x, seviye } : x)
    )
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{initialData ? "Teknisyen Düzenle" : "Yeni Teknisyen Ekle"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Input placeholder="Ad" value={ad} onChange={e => setAd(e.target.value)} />
          <Input placeholder="Soyad" value={soyad} onChange={e => setSoyad(e.target.value)} />
          <Input placeholder="Telefon" value={telefon} onChange={e => setTelefon(e.target.value)} />
          <div className="flex items-center space-x-2">
            <Checkbox checked={aktif} onCheckedChange={v => setAktif(!!v)} id="aktif" />
            <label htmlFor="aktif" className="text-sm">Aktif</label>
          </div>
          <div>
            <div className="mb-1 font-medium text-sm">Uzmanlık Alanları</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {expertiseAreas.map(area => {
                const isSelected = selectedExpertise.find(x => x.id === area.id)
                return (
                  <div key={area.id} className="border rounded-lg p-3 space-y-2">
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <Checkbox 
                        checked={!!isSelected} 
                        onCheckedChange={() => toggleExpertise(area.id)} 
                      />
                      <span 
                        style={{ background: area.renk }} 
                        className="px-2 py-1 rounded text-xs text-white font-medium"
                      >
                        {area.ad}
                      </span>
                    </label>
                    
                    {isSelected && (
                      <div className="ml-6 space-y-1">
                        <div className="text-xs text-gray-600 font-medium">Seviye:</div>
                        <div className="flex gap-2">
                          {(['BASLANGIC', 'ORTA', 'UZMAN'] as const).map(seviye => (
                            <label key={seviye} className="flex items-center space-x-1 cursor-pointer">
                              <input 
                                type="radio" 
                                name={`seviye-${area.id}`}
                                checked={isSelected.seviye === seviye}
                                onChange={() => updateExpertiseLevel(area.id, seviye)}
                                className="text-xs"
                              />
                              <span className={`text-xs px-2 py-1 rounded ${
                                seviye === 'UZMAN' ? 'bg-green-100 text-green-700' :
                                seviye === 'ORTA' ? 'bg-yellow-100 text-yellow-700' :
                                'bg-gray-100 text-gray-700'
                              }`}>
                                {seviye === 'UZMAN' ? '⭐ Uzman' : 
                                 seviye === 'ORTA' ? '○ Orta' : 
                                 '△ Başlangıç'}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
          <div className="flex justify-end space-x-2 pt-2">
            <Button variant="outline" onClick={onClose}>İptal</Button>
            <Button onClick={handleSave} disabled={loading}>{loading ? "Kaydediliyor..." : "Kaydet"}</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 