"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var client_1 = require("@prisma/client");
var enums_1 = require("../src/lib/enums");
var prisma = new client_1.PrismaClient();
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var testDaire, yeniDurum, testAriza, testTeknisyen, testRandevu, randevuSonuc, guncelAriza, testBasarili, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 13, 14, 16]);
                    console.log('🔍 Test başlatılıyor...');
                    return [4 /*yield*/, prisma.daire.findFirst({
                            include: {
                                blok: true,
                                proje: true
                            }
                        })];
                case 1:
                    testDaire = _a.sent();
                    if (!testDaire) {
                        throw new Error('Test için daire bulunamadı');
                    }
                    console.log("\uD83D\uDCCD Test dairesi: ".concat(testDaire.blok.ad, " - ").concat(testDaire.daire_no));
                    return [4 /*yield*/, prisma.arizaDurum.findFirst({
                            where: {
                                ad: enums_1.FAULT_STATUS_LABELS[enums_1.FaultStatus.ACIK]
                            }
                        })];
                case 2:
                    yeniDurum = _a.sent();
                    if (!yeniDurum) {
                        throw new Error('Yeni durum bulunamadı');
                    }
                    return [4 /*yield*/, prisma.ariza.create({
                            data: {
                                baslik: '[TEST] Arıza Kaydı',
                                aciklama: 'Bu bir test arızasıdır',
                                daire_id: testDaire.id,
                                durum_id: yeniDurum.id,
                                oncelik: 'NORMAL',
                                kategori_id: '1', // Varsayılan kategori
                                bildiren_id: '1', // Varsayılan kullanıcı
                            }
                        })];
                case 3:
                    testAriza = _a.sent();
                    console.log("\u2705 Test ar\u0131zas\u0131 olu\u015Fturuldu: ".concat(testAriza.id));
                    return [4 /*yield*/, prisma.teknisyen.findFirst()];
                case 4:
                    testTeknisyen = _a.sent();
                    if (!testTeknisyen) {
                        throw new Error('Test için teknisyen bulunamadı');
                    }
                    return [4 /*yield*/, prisma.randevu.create({
                            data: {
                                ariza_id: testAriza.id,
                                baslangic: new Date(),
                                bitis: new Date(Date.now() + 1000 * 60 * 60), // 1 saat sonra
                                durum: 'BEKLEMEDE',
                                teknisyenler: {
                                    create: {
                                        teknisyen_id: testTeknisyen.id
                                    }
                                }
                            }
                        })];
                case 5:
                    testRandevu = _a.sent();
                    console.log("\u2705 Test randevusu olu\u015Fturuldu: ".concat(testRandevu.id));
                    return [4 /*yield*/, prisma.randevuSonuc.create({
                            data: {
                                randevu_id: testRandevu.id,
                                durum: 'TAMAMEN_COZULDU',
                                ariza_cozuldu_mu: true,
                                tamamlanma_orani: 100,
                                sonraki_randevu_gerekli: false,
                                teknisyen_notlari: 'Test randevusu başarıyla tamamlandı',
                                musteri_memnuniyet: 5,
                                musteri_yorumu: 'Test yorumu',
                                gercek_baslangic: new Date(),
                                gercek_bitis: new Date(),
                                toplam_sure_dk: 60,
                            }
                        })];
                case 6:
                    randevuSonuc = _a.sent();
                    console.log("\u2705 Randevu sonucu kaydedildi");
                    // 7. Randevuyu tamamlandı olarak işaretle
                    return [4 /*yield*/, prisma.randevu.update({
                            where: { id: testRandevu.id },
                            data: {
                                durum: 'TAMAMLANDI',
                                guncelleme_tarihi: new Date()
                            }
                        })];
                case 7:
                    // 7. Randevuyu tamamlandı olarak işaretle
                    _a.sent();
                    console.log("\u2705 Randevu tamamland\u0131 olarak i\u015Faretlendi");
                    return [4 /*yield*/, prisma.ariza.findUnique({
                            where: { id: testAriza.id },
                            include: {
                                durum: true
                            }
                        })];
                case 8:
                    guncelAriza = _a.sent();
                    if (!guncelAriza) {
                        throw new Error('Güncel arıza bulunamadı');
                    }
                    console.log("\n\uD83D\uDCCA Test Sonu\u00E7lar\u0131:");
                    console.log("- Ar\u0131za Durumu: ".concat(guncelAriza.durum.ad));
                    console.log("- Beklenen: ".concat(enums_1.FAULT_STATUS_LABELS[enums_1.FaultStatus.COZULDU]));
                    testBasarili = guncelAriza.durum.ad === enums_1.FAULT_STATUS_LABELS[enums_1.FaultStatus.COZULDU];
                    console.log("\n".concat(testBasarili ? '✅ TEST BAŞARILI' : '❌ TEST BAŞARISIZ'));
                    // 9. Test verilerini temizle
                    return [4 /*yield*/, prisma.randevuSonuc.delete({ where: { randevu_id: testRandevu.id } })];
                case 9:
                    // 9. Test verilerini temizle
                    _a.sent();
                    return [4 /*yield*/, prisma.randevuTeknisyen.deleteMany({ where: { randevu_id: testRandevu.id } })];
                case 10:
                    _a.sent();
                    return [4 /*yield*/, prisma.randevu.delete({ where: { id: testRandevu.id } })];
                case 11:
                    _a.sent();
                    return [4 /*yield*/, prisma.ariza.delete({ where: { id: testAriza.id } })];
                case 12:
                    _a.sent();
                    console.log("\n\uD83E\uDDF9 Test verileri temizlendi");
                    return [3 /*break*/, 16];
                case 13:
                    error_1 = _a.sent();
                    console.error('❌ Test sırasında hata oluştu:', error_1);
                    return [3 /*break*/, 16];
                case 14: return [4 /*yield*/, prisma.$disconnect()];
                case 15:
                    _a.sent();
                    return [7 /*endfinally*/];
                case 16: return [2 /*return*/];
            }
        });
    });
}
main();
