"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON>, AlertTriangle, CheckCircle, XCircle, TrendingUp } from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface FaultStats {
  beklemede: number
  devamEdiyor: number
  tamamlandi: number
  iptal: number
  toplam: number
  buAy: number
  oncekiAy: number
  yuzdelik: number
}

interface FaultsStatsProps {
  apartmentId?: string
}

export function FaultsStats({ apartmentId }: FaultsStatsProps) {
  const [stats, setStats] = useState<FaultStats | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchStats = useCallback(async () => {
    try {
      const url = apartmentId 
        ? `/api/faults/stats?daire=${apartmentId}`
        : "/api/faults/stats"
      const response = await fetch(url)
      const data = await response.json()

      if (response.ok) {
        setStats(data)
      } else {
        console.error("İstatistikler yüklenirken hata:", data.message)
      }
    } catch (error) {
      console.error("İstatistikler yüklenirken hata:", error)
    } finally {
      setLoading(false)
    }
  }, [apartmentId])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardTitle>
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
              <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardContent className="flex items-center justify-center h-24">
            <p className="text-sm text-muted-foreground">
              İstatistik verileri yüklenemedi
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const statCards = [
    {
      title: "Beklemede",
      value: stats.beklemede,
      icon: Clock,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
      description: "Atanmayı bekleyen",
    },
    {
      title: "Devam Ediyor",
      value: stats.devamEdiyor,
      icon: AlertTriangle,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      description: "Çözüm aşamasında",
    },
    {
      title: "Tamamlandı",
      value: stats.tamamlandi,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-50",
      description: "Bu ay tamamlanan",
    },
    {
      title: "İptal Edildi",
      value: stats.iptal,
      icon: XCircle,
      color: "text-gray-600",
      bgColor: "bg-gray-50",
      description: "İptal edilen",
    },
    {
      title: "Toplam Arıza",
      value: stats.toplam,
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      description: `${stats.yuzdelik > 0 ? "+" : ""}${stats.yuzdelik.toFixed(1)}% önceki aya göre`,
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
      {statCards.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-md ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
