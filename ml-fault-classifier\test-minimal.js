console.log('✅ Test başladı!');
console.log('Node.js version:', process.version);
console.log('Çalışan platform:', process.platform);

try {
  const express = require('express');
  console.log('✅ Express yüklendi');
  
  const app = express();
  console.log('✅ Express app oluşturuldu');
  
  const PORT = 3050;
  
  app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
  });
  console.log('✅ Route tanımlandı');
  
  const server = app.listen(PORT, () => {
    console.log(`✅ Server ${PORT} portunda başlatıldı`);
  });
  
  server.on('error', (error) => {
    console.error('❌ Server hatası:', error);
  });
  
} catch (error) {
  console.error('❌ Hata:', error.message);
  console.error('Stack:', error.stack);
} 