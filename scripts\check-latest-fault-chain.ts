import { prisma } from "../src/lib/prisma";

async function main() {
  const fault = await prisma.ariza.findFirst({
    orderBy: { olus<PERSON><PERSON>ma_tarihi: "desc" },
    include: {
      daire: {
        include: {
          blok: {
            include: {
              proje: true
            }
          }
        }
      }
    }
  });
  if (!fault) {
    console.log("Hiç arıza kaydı yok.");
    return;
  }
  console.log("Arıza slug:", fault.slug, '| id:', fault.id, '| silindi_mi:', fault.silindi_mi);
  console.log("Daire slug:", fault.daire.slug, '| id:', fault.daire.id, '| silindi_mi:', fault.daire.silindi_mi);
  console.log("Blok slug:", fault.daire.blok.slug, '| id:', fault.daire.blok.id, '| silindi_mi:', fault.daire.blok.silindi_mi);
  console.log("Proje slug:", fault.daire.blok.proje.slug, '| id:', fault.daire.blok.proje.id, '| silindi_mi:', fault.daire.blok.proje.silindi_mi);
}

main().then(() => prisma.$disconnect()); 