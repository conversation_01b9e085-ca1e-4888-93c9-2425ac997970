"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

// Custom hook for authentication
export function useAuth() {
  const { data: session, status } = useSession()
  
  return {
    user: session?.user,
    isLoading: status === "loading",
    isAuthenticated: !!session,
    isAdmin: session?.user?.role === "ADMIN",
    isTechnician: session?.user?.role === "TEKNISYEN",
    isResident: session?.user?.role === "SAKIN",
    status: session?.user?.status,
  }
}

// Hook to require authentication on a page
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/signin")
    }
  }, [isAuthenticated, isLoading, router])

  return { isAuthenticated, isLoading }
}

// Hook to require specific role
export function useRequireRole(allowedRoles: string[]) {
  const { user, isLoading, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.role || !allowedRoles.includes(user.role)) {
        router.push("/dashboard") // Redirect to dashboard if insufficient permissions
      }
    }
  }, [user, isLoading, isAuthenticated, allowedRoles, router])

  return { 
    hasPermission: user?.role && allowedRoles.includes(user.role),
    isLoading,
    isAuthenticated 
  }
}
