/*
  Warnings:

  - A unique constraint covering the columns `[slug]` on the table `daireler` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `slug` to the `daireler` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "daireler" ADD COLUMN "slug" TEXT NOT NULL DEFAULT 'gecici-slug';

-- Her daireye ben<PERSON>iz slug ata
UPDATE "daireler" SET "slug" = 'daire-' || "id";

-- Unique index en sona
CREATE UNIQUE INDEX "daireler_slug_key" ON "daireler"("slug");
