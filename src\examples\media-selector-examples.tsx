"use client";

import { useState } from "react";
import { 
  GenericMediaSelector, 
  MediaSelectorProvider, 
  useGenericMediaSelector,
  MediaFile,
  createMediaSelectorConfig
} from "@/components/media/GenericMediaSelector";
import { 
  blogMediaConfig, 
  ecommerceMediaConfig, 
  corporateMediaConfig,
  BlogMediaMetadata,
  ProductMediaMetadata
} from "@/config/media-configs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// 📝 EXAMPLE 1: Simple Blog Featured Image Selector
export function BlogFeaturedImageExample() {
  const { selectedMedia, handleSelect, clearSelection } = useGenericMediaSelector<BlogMediaMetadata>();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Blog Featured Image</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <GenericMediaSelector
          onSelect={handleSelect}
          selectedMedia={selectedMedia}
          categoryKey="blog-featured"
          config={blogMediaConfig}
          buttonText="Choose Featured Image"
          showPreview={true}
        />
        
        {selectedMedia && (
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              Selected: {selectedMedia.originalName}
            </p>
            <Button variant="outline" size="sm" onClick={clearSelection}>
              Clear Selection
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 🛒 EXAMPLE 2: E-commerce Product Gallery with Multi-Select
export function ProductGalleryExample() {
  const { selectedItems, handleMultiSelect } = useGenericMediaSelector<ProductMediaMetadata>();

  return (
    <MediaSelectorProvider config={ecommerceMediaConfig}>
      <Card>
        <CardHeader>
          <CardTitle>Product Gallery ({selectedItems.length} images)</CardTitle>
        </CardHeader>
        <CardContent>
          <GenericMediaSelector
            onMultiSelect={handleMultiSelect}
            selectedItems={selectedItems}
            categoryKey="product-gallery"
            multiSelect={true}
            buttonText="Add Product Images"
            showPreview={true}
          />
          
          {selectedItems.length > 0 && (
            <div className="mt-4 grid grid-cols-3 gap-2">
              {selectedItems.map((item) => (
                <div key={item.id} className="aspect-square">
                  <img 
                    src={item.thumbnailSmall || item.url} 
                    alt={item.alt || item.originalName}
                    className="w-full h-full object-cover rounded"
                  />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </MediaSelectorProvider>
  );
}

// 🏢 EXAMPLE 3: Corporate Media with Category Restriction
export function CorporateProjectMediaExample() {
  const [selectedProjectImage, setSelectedProjectImage] = useState<MediaFile | null>(null);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Media (Category Restricted)</CardTitle>
      </CardHeader>
      <CardContent>
        <GenericMediaSelector
          onSelect={setSelectedProjectImage}
          selectedMedia={selectedProjectImage}
          categoryKey="project-images"
          restrictToCategory={true} // Only show project images
          config={corporateMediaConfig}
          buttonText="Proje Görseli Seç"
        />
      </CardContent>
    </Card>
  );
}

// 🎨 EXAMPLE 4: Custom Configuration on the Fly
export function CustomConfigExample() {
  const { selectedMedia, handleSelect } = useGenericMediaSelector();

  // Create custom config for avatars
  const avatarConfig = createMediaSelectorConfig([
    {
      id: 'user-avatars',
      key: 'user-avatars',
      name: 'User Avatars',
      title: 'Select Profile Picture',
      description: 'Choose your profile avatar',
      acceptedTypes: ['image/jpeg', 'image/png'],
      folder: 'users/avatars',
      maxSize: 500 * 1024, // 500KB
      targetDimensions: { width: 200, height: 200 }
    }
  ], {
    showPreview: true,
    translations: {
      selectButton: 'Choose Avatar',
      selectTitle: 'Profile Picture',
      selectDescription: 'Select a square image for your profile'
    }
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Custom Avatar Selector</CardTitle>
      </CardHeader>
      <CardContent>
        <GenericMediaSelector
          onSelect={handleSelect}
          selectedMedia={selectedMedia}
          categoryKey="user-avatars"
          config={avatarConfig}
          targetWidth={200}
          targetHeight={200}
          className="aspect-square w-32"
        />
      </CardContent>
    </Card>
  );
}

// 🎯 EXAMPLE 5: Advanced Usage with Error Handling
export function AdvancedMediaSelectorExample() {
  const { 
    selectedMedia, 
    selectedItems,
    loading, 
    error, 
    handleSelect, 
    handleMultiSelect,
    setLoading,
    setError 
  } = useGenericMediaSelector();

  const handleUploadProgress = (progress: number) => {
  };

  const handleUploadComplete = (media: MediaFile) => {
    handleSelect(media);
  };

  const handleError = (error: Error) => {
    console.error('Media selector error:', error);
    setError(error);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Advanced Media Selector</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <GenericMediaSelector
          onSelect={handleSelect}
          onMultiSelect={handleMultiSelect}
          selectedMedia={selectedMedia}
          selectedItems={selectedItems}
          categoryKey="blog-inline"
          config={blogMediaConfig}
          multiSelect={true}
          loading={loading}
          onUploadProgress={handleUploadProgress}
          onUploadComplete={handleUploadComplete}
          onError={handleError}
          maxFileSize={2 * 1024 * 1024} // 2MB
        />

        {loading && (
          <p className="text-blue-600">Processing...</p>
        )}

        {error && (
          <p className="text-red-600">Error: {error.message}</p>
        )}

        {selectedItems.length > 0 && (
          <div>
            <p className="font-medium">Selected {selectedItems.length} items:</p>
            <ul className="text-sm text-gray-600">
              {selectedItems.map(item => (
                <li key={item.id}>{item.originalName}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 📱 EXAMPLE 6: Mobile-Optimized Selector
export function MobileMediaSelectorExample() {
  const { selectedMedia, handleSelect } = useGenericMediaSelector();

  return (
    <Card className="w-full max-w-sm mx-auto">
      <CardHeader>
        <CardTitle className="text-center">Mobile Upload</CardTitle>
      </CardHeader>
      <CardContent>
        <GenericMediaSelector
          onSelect={handleSelect}
          selectedMedia={selectedMedia}
          categoryKey="blog-featured"
          config={blogMediaConfig}
          className="w-full"
          buttonText="📸 Take Photo"
          acceptedTypes={['image/*']}
          targetWidth={400}
          targetHeight={300}
        />
        
        {selectedMedia && (
          <div className="mt-4 text-center">
            <img 
              src={selectedMedia.thumbnailMedium || selectedMedia.url}
              alt={selectedMedia.alt}
              className="w-full rounded-lg"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 🔄 EXAMPLE 7: Dynamic Category Switching
export function DynamicCategorySelectorExample() {
  const [currentCategory, setCurrentCategory] = useState<string>('blog-featured');
  const { selectedMedia, handleSelect, clearSelection } = useGenericMediaSelector();

  const categories = [
    { key: 'blog-featured', label: 'Featured' },
    { key: 'blog-inline', label: 'Inline' },
    { key: 'blog-gallery', label: 'Gallery' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Dynamic Category Selector</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Category Switcher */}
        <div className="flex gap-2">
          {categories.map(cat => (
            <Button
              key={cat.key}
              variant={currentCategory === cat.key ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setCurrentCategory(cat.key);
                clearSelection(); // Clear when switching categories
              }}
            >
              {cat.label}
            </Button>
          ))}
        </div>

        {/* Media Selector */}
        <GenericMediaSelector
          onSelect={handleSelect}
          selectedMedia={selectedMedia}
          categoryKey={currentCategory}
          config={blogMediaConfig}
          restrictToCategory={true}
          key={currentCategory} // Force re-render when category changes
        />

        {selectedMedia && (
          <p className="text-sm text-gray-600">
            Selected from {currentCategory}: {selectedMedia.originalName}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

// 🧪 EXAMPLE 8: Testing Different Configurations
export function ConfigTestingExample() {
  const [configType, setConfigType] = useState<'blog' | 'ecommerce' | 'corporate'>('blog');
  const { selectedMedia, handleSelect } = useGenericMediaSelector();

  const configs = {
    blog: blogMediaConfig,
    ecommerce: ecommerceMediaConfig,
    corporate: corporateMediaConfig
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuration Testing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          {Object.keys(configs).map(type => (
            <Button
              key={type}
              variant={configType === type ? "default" : "outline"}
              size="sm"
              onClick={() => setConfigType(type as any)}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Button>
          ))}
        </div>

        <GenericMediaSelector
          onSelect={handleSelect}
          selectedMedia={selectedMedia}
          config={configs[configType]}
          key={configType}
        />
      </CardContent>
    </Card>
  );
}

// 📋 MAIN DEMO COMPONENT
export function MediaSelectorDemoPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold mb-8">Generic Media Selector Examples</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <BlogFeaturedImageExample />
        <ProductGalleryExample />
        <CorporateProjectMediaExample />
        <CustomConfigExample />
        <AdvancedMediaSelectorExample />
        <MobileMediaSelectorExample />
        <DynamicCategorySelectorExample />
        <ConfigTestingExample />
      </div>
    </div>
  );
} 