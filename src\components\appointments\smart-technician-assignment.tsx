"use client"

import { useState, useEffect } from "react"
import { Brain, User, MapPin, Clock, Star, Zap, CheckCircle, AlertCircle, Users, Target, Phone, Mail } from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
  expertiseAreas?: string[]
  isAvailable?: boolean
  currentLocation?: string
  rating?: number
  uzmanlikAlanlari?: { ad: string; seviye: string }[]
  durum?: string
  aktifRandevuSayisi?: number
}

interface TechnicianWithScore extends Technician {
  matchScore: number
  matchReasons: string[]
  estimatedDistance?: number
  estimatedTime?: string
}

interface SmartTechnicianAssignmentProps {
  faultCategory: string
  faultPriority: string
  faultLocation: string
  onTechniciansSelected: (technicians: Technician[]) => void
  allowMultiple?: boolean
}

export function SmartTechnicianAssignment({ 
  faultCategory, 
  faultPriority, 
  faultLocation, 
  onTechniciansSelected,
  allowMultiple = false
}: SmartTechnicianAssignmentProps) {
  const [technicians, setTechnicians] = useState<TechnicianWithScore[]>([])
  const [selectedTechnicians, setSelectedTechnicians] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  // Load and score technicians
  useEffect(() => {
    const loadTechnicians = async () => {
      try {
        setLoading(true)
        const response = await fetch("/api/technicians")
        if (response.ok) {
          const data: Technician[] = await response.json()
          
          // Apply smart scoring algorithm
          const scoredTechnicians = data.map(technician => {
            const score = calculateMatchScore(technician, faultCategory, faultPriority)
            return {
              ...technician,
              matchScore: score.score,
              matchReasons: score.reasons,
              // Gerçek veriler (mock kaldırıldı)
              isAvailable: technician.durum === 'ACTIVE', // Gerçek durum kontrolü
              // estimatedDistance ve rating gerçek API'den gelecek
            }
          })

          // Sort by match score (highest first)
          scoredTechnicians.sort((a, b) => b.matchScore - a.matchScore)
          setTechnicians(scoredTechnicians)
        }
      } catch (error) {
        console.error("Failed to load technicians:", error)
      } finally {
        setLoading(false)
      }
    }

    loadTechnicians()
  }, [faultCategory, faultPriority])

  // Enhanced smart matching algorithm
  const calculateMatchScore = (technician: Technician, category: string, priority: string) => {
    let score = 40 // Base score
    const reasons: string[] = []

    // 1. Uzmanlık Alanı Eşleştirmesi (En Önemli - 40 puan)
    const expertiseAreas = technician.uzmanlikAlanlari || []
    let expertiseBonus = 0
    let hasDirectMatch = false

    expertiseAreas.forEach(expertise => {
      const categoryLower = category.toLowerCase()
      const expertiseLower = expertise.ad.toLowerCase()
      
      // Direkt eşleşme
      if (expertiseLower.includes(categoryLower) || categoryLower.includes(expertiseLower)) {
        expertiseBonus += 40
        hasDirectMatch = true
        reasons.push(`🎯 ${expertise.ad} uzmanlığı`)
      }
      
      // Seviye bonusu
      if (hasDirectMatch) {
        switch (expertise.seviye) {
          case 'UZMAN':
            expertiseBonus += 15
            reasons.push("⭐ Uzman seviye")
            break
          case 'ORTA':
            expertiseBonus += 8
            reasons.push("○ Orta seviye")
            break
          case 'BASLANGIC':
            expertiseBonus += 3
            reasons.push("△ Başlangıç seviye")
            break
        }
      }
    })
    
    score += Math.min(expertiseBonus, 50) // Maksimum 50 puan

    // 2. Öncelik Eşleştirmesi (15 puan)
    const priorityLower = priority.toLowerCase()
    if (priorityLower.includes('acil') || priorityLower.includes('yüksek')) {
      score += 15
      reasons.push("⚡ Acil arıza deneyimi")
    } else if (priorityLower.includes('orta')) {
      score += 8
      reasons.push("📊 Orta öncelik uyumu")
    }

    // 3. Teknisyen Durumu (10 puan)
    if (technician.durum === 'ACTIVE') {
      score += 10
      reasons.push("✅ Aktif teknisyen")
    }

    // 4. İş Yükü Analizi (10 puan)
    const activeJobs = technician.aktifRandevuSayisi || 0
    if (activeJobs === 0) {
      score += 10
      reasons.push("🆓 Müsait teknisyen")
    } else if (activeJobs <= 2) {
      score += 5
      reasons.push("📅 Az iş yükü")
    }

    // 5. Genel Güvenilirlik (5 puan)
    if (expertiseAreas.length >= 2) {
      score += 5
      reasons.push("🔧 Çok yönlü")
    }

    // Eğer hiç uzmanlık eşleşmesi yoksa, genel puanı düşür
    if (!hasDirectMatch) {
      score = Math.max(score - 30, 10) // Minimum 10 puan
      reasons.push("⚠️ Genel teknisyen")
    }

    // Ensure score is between 10-100
    score = Math.max(10, Math.min(100, score))

    return { score, reasons: reasons.slice(0, 4) } // En fazla 4 sebep göster
  }

  const handleTechnicianSelect = (technicianId: string, selected: boolean) => {
    if (allowMultiple) {
      if (selected) {
        setSelectedTechnicians(prev => [...prev, technicianId])
      } else {
        setSelectedTechnicians(prev => prev.filter(id => id !== technicianId))
      }
    } else {
      setSelectedTechnicians(selected ? [technicianId] : [])
    }
  }

  const handleAssign = () => {
    const selectedTechnicianObjects = technicians.filter(t => selectedTechnicians.includes(t.id))
    onTechniciansSelected(selectedTechnicianObjects)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-green-500"
    if (score >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 animate-pulse" />
            Akıllı Eşleştirme Hesaplanıyor...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-20 bg-muted rounded-lg"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full">
      <div className="mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Brain className="h-5 w-5" />
          🤖 Gerçek AI Teknisyen Eşleştirme
        </h3>
        <div className="text-sm text-muted-foreground mt-2">
          <p>📍 <strong>Konum:</strong> {faultLocation}</p>
          <p>🏷️ <strong>Arıza:</strong> {faultCategory} - {faultPriority} Öncelik</p>
          <p className="mt-1 text-xs bg-blue-50 px-2 py-1 rounded">
            ✨ <strong>Akıllı Analiz:</strong> Uzmanlık alanı, deneyim seviyesi, iş yükü ve müsaitlik durumu analiz ediliyor
          </p>
        </div>
      </div>
      
      <div className="space-y-3 max-h-[500px] overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {technicians.slice(0, 8).map(technician => (
            <div 
              key={technician.id}
              className={`border rounded-lg p-4 transition-all duration-200 hover:shadow-md ${
                selectedTechnicians.includes(technician.id) 
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950 shadow-md' 
                  : 'border-border hover:border-blue-300'
              }`}
            >
              <div className="flex items-center gap-4">
                <Checkbox
                  checked={selectedTechnicians.includes(technician.id)}
                  onCheckedChange={(checked) => 
                    handleTechnicianSelect(technician.id, checked as boolean)
                  }
                />
                <Avatar className="h-14 w-14">
                  <AvatarImage src={technician.resim || ""} />
                  <AvatarFallback className="text-lg font-semibold">
                    {technician.ad.charAt(0)}{technician.soyad.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-lg font-semibold">
                        {technician.ad} {technician.soyad}
                      </h4>
                      <div className="flex items-center gap-2 text-muted-foreground mt-1">
                        {technician.telefon && (
                          <div className="flex items-center gap-1">
                            <Phone className="h-4 w-4" />
                            <span className="text-sm">{technician.telefon}</span>
                          </div>
                        )}
                        <Badge 
                          variant={technician.isAvailable ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {technician.isAvailable ? "Müsait" : "Meşgul"}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-3xl font-bold ${getScoreColor(technician.matchScore)}`}>
                        %{technician.matchScore}
                      </div>
                      <div className="text-xs text-muted-foreground">Eşleşme Skoru</div>
                    </div>
                  </div>
                  <div className="mt-3">
                    <Progress 
                      value={technician.matchScore} 
                      className="h-2"
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {technician.matchReasons.slice(0, 3).map((reason, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {reason}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedTechnicians.length > 0 && (
        <div className="flex items-center justify-between pt-6 border-t mt-6">
          <div className="text-sm text-muted-foreground">
            <strong>{selectedTechnicians.length}</strong> teknisyen seçildi
          </div>
          <Button 
            onClick={handleAssign}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-8"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Teknisyenleri Ata
          </Button>
        </div>
      )}
    </div>
  )
}