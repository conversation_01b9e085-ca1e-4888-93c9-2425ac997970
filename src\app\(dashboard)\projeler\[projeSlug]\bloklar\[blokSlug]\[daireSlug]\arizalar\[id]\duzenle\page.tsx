"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON>, usePara<PERSON> } from "next/navigation"
import { ArrowLeft, ChevronRight, MapPin } from "lucide-react"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { EditFaultForm } from "@/components/faults/edit-fault-form"
import { Skeleton } from "@/components/ui/skeleton"


interface FaultData {
  id: string
  numara: string
  baslik: string
  aciklama: string
  ariza_tip_id: string
  durum_id: string
  aciliyet_id: string
  resimler: string[]
  daire: {
    id: string
    numara: string
    slug: string
    blok: {
      id: string
      ad: string
      slug: string
      proje: {
        id: string
        ad: string
        slug: string
      }
    }
  }
  tip: {
    id: string
    ad: string
    renk: string
  }
  durum: {
    id: string
    ad: string
    renk: string
  }
  aciliyet: {
    id: string
    ad: string
    renk: string
    seviye: number
  }
  randevular: any[]
}

interface EditFaultPageProps {
  params: Promise<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string }>
}

export default function EditFaultPage({ params }: EditFaultPageProps) {
  const router = useRouter()
  const [fault, setFault] = useState<FaultData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [resolvedParams, setResolvedParams] = useState<{ projeSlug: string; blokSlug: string; daireSlug: string; id: string } | null>(null)

  useEffect(() => {
    const loadParams = async () => {
      const resolved = await params
      setResolvedParams(resolved)
    }
    loadParams()
  }, [params])

  useEffect(() => {
    async function fetchFault() {
      if (!resolvedParams) return
      
      try {
        setLoading(true)
        const response = await fetch(`/api/faults/${resolvedParams.id}`)
        
        if (!response.ok) {
          throw new Error("Arıza bulunamadı")
        }

        const faultData = await response.json()
        setFault(faultData)
      } catch (err) {
        console.error("Arıza yüklenirken hata:", err)
        setError(err instanceof Error ? err.message : "Bir hata oluştu")
      } finally {
        setLoading(false)
      }
    }

    if (resolvedParams) {
      fetchFault()
    }
  }, [resolvedParams])

  const handleCancel = () => {
    if (resolvedParams && fault) {
      router.push(`/projeler/${resolvedParams.projeSlug}/bloklar/${resolvedParams.blokSlug}/${resolvedParams.daireSlug}/arizalar/${resolvedParams.id}`)
    } else {
      router.back()
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <div className="bg-white border-b border-gray-200 px-4 sm:px-6 lg:px-8 py-4">
          {/* Breadcrumb Skeleton */}
          <div className="flex items-center space-x-2 text-sm mb-4">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-16" />
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <Skeleton className="h-9 w-24" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-64" />
            </div>
          </div>
        </div>
        {/* Form Section */}
        <div className="flex-1 px-4 sm:px-6 lg:px-8 py-6">
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    )
  }

  if (error || !fault || !resolvedParams) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <div className="bg-white border-b border-gray-200 px-4 sm:px-6 lg:px-8 py-4">
          {/* Simple Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4">
            <Link href="/dashboard" className="hover:text-foreground transition-colors">
              Ana Sayfa
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="font-medium text-foreground">Arıza Düzenle</span>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Geri Dön</span>
              <span className="sm:hidden">Geri</span>
            </Button>
            <div className="flex-1">
              <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Hata</h2>
              <p className="text-muted-foreground text-sm sm:text-base">
                {error || "Arıza bulunamadı"}
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 px-4 sm:px-6 lg:px-8 py-4">
        {/* Custom Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-4 overflow-x-auto">
          <Link href="/dashboard" className="hover:text-foreground transition-colors whitespace-nowrap">
            Ana Sayfa
          </Link>
          <ChevronRight className="h-4 w-4 flex-shrink-0" />
          <Link href={`/projeler`} className="hover:text-foreground transition-colors whitespace-nowrap">
            Projeler
          </Link>
          <ChevronRight className="h-4 w-4 flex-shrink-0" />
          <Link href={`/projeler/${fault.daire.blok.proje.slug}`} className="hover:text-foreground transition-colors whitespace-nowrap">
            {fault.daire.blok.proje.ad}
          </Link>
          <ChevronRight className="h-4 w-4 flex-shrink-0" />
          <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}`} className="hover:text-foreground transition-colors whitespace-nowrap">
            {fault.daire.blok.ad}
          </Link>
          <ChevronRight className="h-4 w-4 flex-shrink-0" />
          <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}`} className="hover:text-foreground transition-colors whitespace-nowrap">
            Daire {fault.daire.numara}
          </Link>
          <ChevronRight className="h-4 w-4 flex-shrink-0" />
          <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.id}`} className="hover:text-foreground transition-colors whitespace-nowrap">
            Arıza #{fault.numara}
          </Link>
          <ChevronRight className="h-4 w-4 flex-shrink-0" />
          <span className="font-medium text-foreground whitespace-nowrap">Düzenle</span>
        </div>
        
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/projeler/${fault.daire.blok.proje.slug}/bloklar/${fault.daire.blok.slug}/${fault.daire.slug}/arizalar/${fault.id}`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Arıza Detayına Dön</span>
              <span className="sm:hidden">Geri</span>
            </Link>
          </Button>
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Arıza Düzenle</h2>
            <p className="text-muted-foreground text-sm sm:text-base">
              #{fault.numara} - {fault.baslik}
            </p>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <MapPin className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">{fault.daire.blok.proje.ad} - {fault.daire.blok.ad} - Daire {fault.daire.numara}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Form Section */}
      <div className="flex-1 px-4 sm:px-6 lg:px-8 py-6">
        <EditFaultForm 
          fault={fault}
          onCancel={handleCancel}
        />
      </div>
    </div>
  )
} 