const Redis = require('ioredis')

async function testRedisConnection() {
  console.log('🔍 Testing Redis connection...')
  
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: 3,
  })

  try {
    // Test connection
    await redis.ping()
    console.log('✅ Redis connection successful!')
    
    // Test basic operations
    await redis.set('test:key', 'Hello Redis!')
    const value = await redis.get('test:key')
    console.log('✅ Set/Get test successful:', value)
    
    // Test with TTL
    await redis.setex('test:ttl', 10, 'TTL test value')
    const ttlValue = await redis.get('test:ttl')
    console.log('✅ TTL test successful:', ttlValue)
    
    // Clean up
    await redis.del('test:key', 'test:ttl')
    console.log('✅ Cleanup successful')
    
    // Test pattern matching
    await redis.set('api:users:list:page=1', 'user data')
    await redis.set('api:faults:stats', 'stats data')
    const keys = await redis.keys('api:*')
    console.log('✅ Pattern matching test successful:', keys)
    
    // Clean up pattern test
    await redis.del(...keys)
    
    console.log('🎉 All Redis tests passed!')
    
  } catch (error) {
    console.error('❌ Redis test failed:', error)
  } finally {
    await redis.quit()
    console.log('🔌 Redis connection closed')
  }
}

testRedisConnection() 