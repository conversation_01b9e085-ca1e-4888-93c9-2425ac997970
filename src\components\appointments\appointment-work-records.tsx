"use client"

import { useState, useEffect } from "react"
import { Plus, Clock, User, FileText, Camera, CheckCircle2, Loader2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useToast } from "@/hooks/use-toast"

// Enum değerleri
const ISLEM_DURUM = {
  PLANLI: "PLANLI",
  DEVAM_EDIYOR: "DEVAM_EDIYOR", 
  TAMAMLANDI: "TAMAMLANDI",
  BEKLEMEDE: "BEKLEMEDE",
  IPTAL: "IPTAL"
} as const

const ISLEM_DURUM_LABELS = {
  PLANLI: "Planlı",
  DEVAM_EDIYOR: "Devam Ediyor",
  TAMAMLANDI: "Tamamlandı", 
  BEKLEMEDE: "Beklemede",
  IPTAL: "İptal"
}

const ISLEM_DURUM_COLORS = {
  PLANLI: "#6B7280",
  DEVAM_EDIYOR: "#F59E0B",
  TAMAMLANDI: "#10B981",
  BEKLEMEDE: "#8B5CF6",
  IPTAL: "#EF4444"
}

interface IslemTuru {
  id: string
  ad: string
  aciklama: string | null
  kategori: string
  renk: string
  sure_dk: number | null
}

interface Teknisyen {
  id: string
  ad: string
  soyad: string
  email: string
  resim: string | null
}

interface WorkRecord {
  id: string
  islem_turu: IslemTuru
  teknisyen: Teknisyen
  aciklama: string | null
  baslangic_saat: string | null
  bitis_saat: string | null
  durum: keyof typeof ISLEM_DURUM
  notlar: string | null
  resimler: string[]
  olusturulma_tarihi: string
  guncelleme_tarihi: string
}

interface AppointmentWorkRecordsProps {
  appointmentId: string
  appointmentTechnicians: Array<{
    teknisyen: Teknisyen
  }>
}

const formSchema = z.object({
  islem_turu_id: z.string().min(1, "İşlem türü seçmelisiniz"),
  teknisyen_id: z.string().min(1, "Teknisyen seçmelisiniz"),
  aciklama: z.string().optional(),
  baslangic_saat: z.string().optional(),
  bitis_saat: z.string().optional(),
  durum: z.nativeEnum(ISLEM_DURUM),
  notlar: z.string().optional(),
})

type FormData = z.infer<typeof formSchema>

export function AppointmentWorkRecords({ appointmentId, appointmentTechnicians }: AppointmentWorkRecordsProps) {
  const [workRecords, setWorkRecords] = useState<WorkRecord[]>([])
  const [islemTurleri, setIslemTurleri] = useState<IslemTuru[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const { addToast } = useToast()

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      islem_turu_id: "",
      teknisyen_id: "",
      aciklama: "",
      baslangic_saat: "",
      bitis_saat: "",
      durum: ISLEM_DURUM.PLANLI,
      notlar: "",
    },
  })

  // Veri yükleme
  const loadData = async () => {
    try {
      setLoading(true)
      
      // İşlem kayıtlarını yükle
      const workResponse = await fetch(`/api/appointments/${appointmentId}/islemler`)
      if (workResponse.ok) {
        const workData = await workResponse.json()
        setWorkRecords(workData)
      }

      // İşlem türlerini yükle
      const typesResponse = await fetch('/api/islem-turleri')
      if (typesResponse.ok) {
        const typesData = await typesResponse.json()
        setIslemTurleri(typesData)
      }
    } catch (error) {
      console.error("Error loading data:", error)
      addToast({
        title: "Hata",
        message: "Veriler yüklenirken bir hata oluştu",
        type: "error",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [appointmentId])

  // Form gönderimi
  const onSubmit = async (data: FormData) => {
    try {
      setSubmitting(true)
      
      const response = await fetch(`/api/appointments/${appointmentId}/islemler`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          baslangic_saat: data.baslangic_saat || null,
          bitis_saat: data.bitis_saat || null,
        }),
      })

      if (!response.ok) {
        throw new Error("İşlem kaydedilemedi")
      }

      addToast({
        title: "Başarılı",
        message: "İşlem kaydı başarıyla oluşturuldu",
        type: "success",
      })

      // Formu sıfırla ve dialog'u kapat
      form.reset()
      setIsAddDialogOpen(false)
      
      // Verileri yeniden yükle
      loadData()
    } catch (error) {
      console.error("Error submitting form:", error)
      addToast({
        title: "Hata",
        message: error instanceof Error ? error.message : "İşlem kaydedilirken bir hata oluştu",
        type: "error",
      })
    } finally {
      setSubmitting(false)
    }
  }

  const formatDateTime = (dateTimeString: string | null) => {
    if (!dateTimeString) return "Belirtilmemiş"
    return new Date(dateTimeString).toLocaleString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatDuration = (start: string | null, end: string | null) => {
    if (!start || !end) return null
    const startTime = new Date(start)
    const endTime = new Date(end)
    const durationMs = endTime.getTime() - startTime.getTime()
    const durationMinutes = Math.round(durationMs / (1000 * 60))
    
    if (durationMinutes < 60) {
      return `${durationMinutes} dakika`
    } else {
      const hours = Math.floor(durationMinutes / 60)
      const minutes = durationMinutes % 60
      return `${hours} saat${minutes > 0 ? ` ${minutes} dakika` : ""}`
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            İşlem Kayıtları
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              İşlem Kayıtları
            </CardTitle>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              İşlem Ekle
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {workRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>Henüz işlem kaydı bulunmuyor</p>
              <p className="text-sm">Yeni işlem eklemek için yukarıdaki butona tıklayın</p>
            </div>
          ) : (
            <div className="space-y-4">
              {workRecords.map((record) => (
                <div key={record.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: record.islem_turu.renk }}
                      />
                      <div>
                        <h4 className="font-medium">{record.islem_turu.ad}</h4>
                        <p className="text-sm text-muted-foreground">{record.islem_turu.kategori}</p>
                      </div>
                    </div>
                    <Badge 
                      style={{ 
                        backgroundColor: ISLEM_DURUM_COLORS[record.durum] + "20",
                        borderColor: ISLEM_DURUM_COLORS[record.durum],
                        color: ISLEM_DURUM_COLORS[record.durum]
                      }}
                    >
                      {ISLEM_DURUM_LABELS[record.durum]}
                    </Badge>
                  </div>

                  {record.aciklama && (
                    <div>
                      <p className="text-sm font-medium mb-1">Açıklama:</p>
                      <p className="text-sm text-muted-foreground">{record.aciklama}</p>
                    </div>
                  )}

                  <div className="flex items-center gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={record.teknisyen.resim || ""} />
                        <AvatarFallback className="text-xs">
                          {record.teknisyen.ad.charAt(0)}{record.teknisyen.soyad.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span>{record.teknisyen.ad} {record.teknisyen.soyad}</span>
                    </div>
                    
                    {record.baslangic_saat && (
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>{formatDateTime(record.baslangic_saat)}</span>
                        {record.bitis_saat && (
                          <>
                            <span>-</span>
                            <span>{formatDateTime(record.bitis_saat)}</span>
                            {formatDuration(record.baslangic_saat, record.bitis_saat) && (
                              <span className="text-muted-foreground">
                                ({formatDuration(record.baslangic_saat, record.bitis_saat)})
                              </span>
                            )}
                          </>
                        )}
                      </div>
                    )}
                  </div>

                  {record.notlar && (
                    <div>
                      <p className="text-sm font-medium mb-1">Notlar:</p>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{record.notlar}</p>
                    </div>
                  )}

                  {record.resimler.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-2 flex items-center gap-2">
                        <Camera className="h-4 w-4" />
                        Medya ({record.resimler.length})
                      </p>
                      <div className="flex gap-2">
                        {record.resimler.map((resim, index) => (
                          <div key={index} className="w-16 h-16 bg-muted rounded border">
                            {/* TODO: Resim preview implementasyonu */}
                            <div className="w-full h-full flex items-center justify-center">
                              <Camera className="h-6 w-6 text-muted-foreground" />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* İşlem Ekleme Dialog'u */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Yeni İşlem Kaydı
            </DialogTitle>
            <DialogDescription>
              Randevu için yeni bir işlem kaydı oluşturun.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="islem_turu_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>İşlem Türü</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="İşlem türü seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {islemTurleri.map((tur) => (
                            <SelectItem key={tur.id} value={tur.id}>
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: tur.renk }}
                                />
                                {tur.ad} 
                                <span className="text-xs text-muted-foreground">({tur.kategori})</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="teknisyen_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Teknisyen</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Teknisyen seçin" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {appointmentTechnicians.map(({ teknisyen }) => (
                            <SelectItem key={teknisyen.id} value={teknisyen.id}>
                              {teknisyen.ad} {teknisyen.soyad}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="aciklama"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Açıklama</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="İşlemle ilgili detayları açıklayın..."
                        className="resize-none"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="baslangic_saat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Başlangıç Saati</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="bitis_saat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bitiş Saati</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="durum"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Durum</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Durum seçin" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(ISLEM_DURUM_LABELS).map(([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notlar"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notlar (İsteğe bağlı)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Ek notlar, karşılaşılan zorluklar vs..."
                        className="resize-none"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  disabled={submitting}
                >
                  İptal
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Kaydediliyor...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Kaydet
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}
