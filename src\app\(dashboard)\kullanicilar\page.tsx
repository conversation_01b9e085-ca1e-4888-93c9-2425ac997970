"use client"

import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  UserCheck,
  UserX,
  Edit,
  Trash2,
  Shield,
  UserCog
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Breadcrumb } from "@/components/layout/breadcrumb"
import { UserRole, UserStatus, USER_ROLE_LABELS } from "@/lib/enums"

const usersFetcher = async () => {
  const res = await fetch("/api/users")
  return res.json()
}

const USER_STATUS_LABELS = {
  [UserStatus.PENDING]: "Beklemede",
  [UserStatus.ACTIVE]: "Aktif",
  [UserStatus.INACTIVE]: "Pasif",
  [UserStatus.BLOCKED]: "Engellenmiş",
} as const

const STATUS_COLORS = {
  [UserStatus.PENDING]: "bg-yellow-100 text-yellow-800 border-yellow-200",
  [UserStatus.ACTIVE]: "bg-green-100 text-green-800 border-green-200",
  [UserStatus.INACTIVE]: "bg-gray-100 text-gray-800 border-gray-200",
  [UserStatus.BLOCKED]: "bg-red-100 text-red-800 border-red-200",
} as const

const ROLE_COLORS = {
  [UserRole.ADMIN]: "bg-purple-100 text-purple-800 border-purple-200",
  [UserRole.MANAGER]: "bg-blue-100 text-blue-800 border-blue-200",
  [UserRole.TECHNICIAN]: "bg-orange-100 text-orange-800 border-orange-200",
  [UserRole.USER]: "bg-gray-100 text-gray-800 border-gray-200",
} as const

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  const { data: usersData, isLoading } = useQuery({
    queryKey: ["users"],
    queryFn: usersFetcher
  })

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-96 bg-gray-200 rounded animate-pulse"></div>
      </div>
    )
  }

  const users = usersData?.users || []

  // Filter users based on search and filters
  const filteredUsers = users.filter((user: any) => {
    const matchesSearch = searchTerm === "" || 
      user.ad.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.soyad.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = roleFilter === "all" || user.rol === roleFilter
    const matchesStatus = statusFilter === "all" || user.durum === statusFilter

    return matchesSearch && matchesRole && matchesStatus
  })

  // Calculate stats
  const stats = {
    total: users.length,
    active: users.filter((u: any) => u.durum === UserStatus.ACTIVE).length,
    pending: users.filter((u: any) => u.durum === UserStatus.PENDING).length,
    admins: users.filter((u: any) => u.rol === UserRole.ADMIN).length,
    technicians: users.filter((u: any) => u.rol === UserRole.TECHNICIAN).length,
    regularUsers: users.filter((u: any) => u.rol === UserRole.USER).length,
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
            👥 Kullanıcı Yönetimi
          </h1>
          <p className="text-muted-foreground mt-1">
            Sistem kullanıcılarını yönetin, roller ve izinleri düzenleyin
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Yeni Kullanıcı
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Toplam Kullanıcı</p>
                <p className="text-2xl font-bold text-blue-900">{stats.total}</p>
                <p className="text-xs text-blue-600 mt-1">Sistemde kayıtlı</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Aktif Kullanıcı</p>
                <p className="text-2xl font-bold text-green-900">{stats.active}</p>
                <p className="text-xs text-green-600 mt-1">Giriş yapabiliyor</p>
              </div>
              <UserCheck className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Teknisyen</p>
                <p className="text-2xl font-bold text-orange-900">{stats.technicians}</p>
                <p className="text-xs text-orange-600 mt-1">Teknik personel</p>
              </div>
              <UserCog className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Yönetici</p>
                <p className="text-2xl font-bold text-purple-900">{stats.admins}</p>
                <p className="text-xs text-purple-600 mt-1">Tam yetki</p>
              </div>
              <Shield className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-indigo-600" />
            Kullanıcı Listesi
          </CardTitle>
          <CardDescription>
            Tüm sistem kullanıcılarını görüntüleyin ve yönetin
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Ad, soyad veya email ile ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Rol filtrele" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Roller</SelectItem>
                {Object.entries(USER_ROLE_LABELS).map(([key, label]) => (
                  <SelectItem key={key} value={key}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Durum filtrele" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Durumlar</SelectItem>
                {Object.entries(USER_STATUS_LABELS).map(([key, label]) => (
                  <SelectItem key={key} value={key}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* User Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[60px]"></TableHead>
                  <TableHead>Kullanıcı</TableHead>
                  <TableHead>Rol</TableHead>
                  <TableHead>Durum</TableHead>
                  <TableHead>Departman</TableHead>
                  <TableHead>Daire</TableHead>
                  <TableHead>Son Aktivite</TableHead>
                  <TableHead className="w-[100px]">İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">Kullanıcı bulunamadı</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user: any) => (
                    <TableRow key={user.id} className="hover:bg-muted/50">
                      <TableCell>
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={user.resim} />
                          <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                            {user.ad?.[0]}{user.soyad?.[0]}
                          </AvatarFallback>
                        </Avatar>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{user.ad} {user.soyad}</p>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                          {user.telefon && (
                            <p className="text-xs text-muted-foreground">{user.telefon}</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={ROLE_COLORS[user.rol as UserRole] || "border"}>
                          {USER_ROLE_LABELS[user.rol as UserRole] || user.rol}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={STATUS_COLORS[user.durum as UserStatus] || "border"}>
                          {USER_STATUS_LABELS[user.durum as UserStatus] || user.durum}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.departman ? (
                          <span className="text-sm">{user.departman.ad}</span>
                        ) : (
                          <span className="text-sm text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {user.daire ? (
                          <div className="text-sm">
                            <span className="font-medium">{user.daire.blok?.proje?.ad}</span>
                            <br />
                            <span className="text-muted-foreground">
                              {user.daire.blok?.ad} - {user.daire.numara}
                            </span>
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {new Date(user.guncelleme_tarihi).toLocaleDateString('tr-TR')}
                        </span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Düzenle
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <UserCheck className="mr-2 h-4 w-4" />
                              Durumu Değiştir
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <UserCog className="mr-2 h-4 w-4" />
                              Rol Değiştir
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Sil
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Results Summary */}
          {filteredUsers.length > 0 && (
            <div className="flex items-center justify-between pt-4">
              <p className="text-sm text-muted-foreground">
                {filteredUsers.length} kullanıcı gösteriliyor (toplam {users.length})
              </p>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  Önceki
                </Button>
                <Button variant="outline" size="sm">
                  Sonraki
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 