import packageJson from '../../package.json';

export const getAppVersion = () => {
  return packageJson.version;
};

export const getAppName = () => {
  return packageJson.name;
};

// Use a fixed build time for SSR/CSR consistency
export const getBuildTime = () => {
  // Use env variable if set, otherwise fallback to a fixed ISO string
  return process.env.BUILD_TIME || '2024-07-05T10:00:00.000Z';
};

export const getBuildInfo = () => {
  const buildTime = getBuildTime();
  const date = new Date(buildTime);
  return {
    version: getAppVersion(),
    appName: getAppName(),
    buildTime: buildTime,
    buildDate: date.toLocaleDateString('en-CA', { timeZone: 'UTC' }),
    buildTimeFormatted: date.toLocaleString('en-CA', { timeZone: 'UTC' }),
  };
};

export const formatVersionDisplay = (version: string) => {
  // v1.0.0 -> 1.0.0 formatına çevir
  return version.startsWith('v') ? version : `v${version}`;
};

// Yeni sürüm özelliklerini döndüren fonksiyon
export const getVersionFeatures = () => {
  const version = getAppVersion();
  
  // Sürüm bazlı özellikler
  const features: Record<string, {
    tag: string;
    color: string;
    features: string[];
  }> = {
    '1.9.0': {
      tag: '🔗 Slug-Based API & Appointments',
      color: 'green',
      features: [
        'Slug tabanlı API endpoint\'leri',
        'Randevu oluşturma sorunları düzeltildi',
        'Edit fault form\'da randevu oluşturma',
        'API route optimizasyonları',
        'Frontend-backend uyumluluğu artırıldı'
      ]
    },
    '1.8.0': {
      tag: '🚀 Enhanced UX & Loading',
      color: 'purple',
      features: [
        'Gelişmiş loading sistemi',
        'Sayfa geçişlerinde smooth animasyonlar',
        'Breadcrumb navigasyon iyileştirmeleri',
        'Proje yönetimi UX geliştirmeleri'
      ]
    },
    '1.7.1': {
      tag: '🔧 Bug Fixes & Stability',
      color: 'blue',
      features: [
        'Hydration hataları düzeltildi',
        'Performans iyileştirmeleri',
        'Stabilite artırıldı'
      ]
    }
  };
  
  return features[version] || {
    tag: '📦 General Update',
    color: 'gray',
    features: ['Genel güncellemeler']
  };
}; 