"use client"

import { useState, useEffect, use<PERSON>allback } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { Plus, Search, MoreHorizontal, Edit, Trash2, Home } from "lucide-react"
import { useLoading } from "@/contexts/loading-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ApartmentDialog } from "./apartment-dialog"
import { DeleteApartmentDialog } from "./delete-apartment-dialog"

interface Project {
  id: string
  ad: string
  slug: string
}

interface Block {
  id: string
  ad: string
  proje: Project
  slug: string
}

interface Apartment {
  id: string
  numara: string
  blok_id: string
  kat?: number
  aciklama?: string
  blok: Block
  _count: {
    arizalar: number
  }
  olusturulma_tarihi: string
  slug: string
}

interface ApiResponse {
  apartments: Apartment[]
  pagination: {
    currentPage: number
    totalPages: number
    totalCount: number
    limit: number
  }
}

export function ApartmentManagement({ showPageHeader = false }: { showPageHeader?: boolean }) {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { startLoading } = useLoading()
  const preselectedBlockId = searchParams.get('blokId')
  
  const [apartments, setApartments] = useState<Apartment[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [blocks, setBlocks] = useState<Block[]>([])
  const [selectedBlockInfo, setSelectedBlockInfo] = useState<Block | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedProject, setSelectedProject] = useState<string>("all")
  const [selectedBlock, setSelectedBlock] = useState<string>(preselectedBlockId || "all")
  const [preselectedProjectId, setPreselectedProjectId] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [showApartmentDialog, setShowApartmentDialog] = useState(false)
  const [editingApartment, setEditingApartment] = useState<Apartment | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingApartment, setDeletingApartment] = useState<Apartment | null>(null)

  const limit = 10
  const handleRowClick = (apartment: Apartment) => {
    startLoading()
    router.push(`/projeler/${apartment.blok.proje.slug}/bloklar/${apartment.blok.slug}/${apartment.slug}`)
  }

  const fetchProjects = useCallback(async () => {
    try {
      const response = await fetch("/api/projects?mode=dropdown")
      if (!response.ok) {
        throw new Error("Failed to fetch projects")
      }
      const data = await response.json()
      setProjects(data.projects)
    } catch (error) {
      console.error("Error fetching projects:", error)
    }
  }, [])
  const fetchBlocks = useCallback(async () => {
    try {
      const params = new URLSearchParams({
        mode: "dropdown",
        ...(preselectedProjectId && { projeId: preselectedProjectId }),
      })
      const response = await fetch(`/api/blocks?${params}`)
      if (!response.ok) {
        throw new Error("Failed to fetch blocks")
      }
      const data = await response.json()
      setBlocks(data.blocks)
    } catch (error) {
      console.error("Error fetching blocks:", error)
    }
  }, [preselectedProjectId])
  
  const fetchApartments = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        mode: "management",
        page: currentPage.toString(),
        limit: limit.toString(),
        ...(searchQuery && { search: searchQuery }),
        blok_id: selectedBlock,
      })

      const response = await fetch(`/api/apartments?${params}`)
      if (!response.ok) {
        throw new Error("Failed to fetch apartments")
      }

      const data: ApiResponse = await response.json()
      setApartments(data.apartments)
      setTotalPages(data.pagination.totalPages)
      setTotalCount(data.pagination.totalCount)
    } catch (error) {
      console.error("Error fetching apartments:", error)
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchQuery, selectedBlock, limit])
  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  useEffect(() => {
    fetchBlocks()
  }, [fetchBlocks])
  useEffect(() => {
    if (preselectedBlockId && preselectedBlockId !== selectedBlock) {
      setSelectedBlock(preselectedBlockId)
    }
  }, [preselectedBlockId, selectedBlock])

  // Fetch project ID and block info from preselected block
  useEffect(() => {
    if (preselectedBlockId) {
      const fetchBlockInfo = async () => {
        try {
          const response = await fetch(`/api/blocks/${preselectedBlockId}`)
          if (response.ok) {
            const block = await response.json()
            setPreselectedProjectId(block.proje_id)
            setSelectedBlockInfo(block)
          }
        } catch (error) {
          console.error("Error fetching block info:", error)
        }
      }
      fetchBlockInfo()
    }
  }, [preselectedBlockId])

  // Bloklar yüklendikten sonra, preselectedBlockId bloklar arasında varsa, value'yu set et
  useEffect(() => {
    if (
      preselectedBlockId &&
      Array.isArray(blocks) &&
      blocks.length > 0 &&
      blocks.some(b => b.id === preselectedBlockId) &&
      selectedBlock !== preselectedBlockId
    ) {
      setSelectedBlock(preselectedBlockId);
    }
  }, [preselectedBlockId, blocks, selectedBlock]);

  useEffect(() => {
    fetchApartments()
  }, [fetchApartments])

  const handleSearch = (value: string) => {
    setSearchQuery(value)
    setCurrentPage(1)
  }
  
  const handleEdit = (apartment: Apartment) => {
    setEditingApartment(apartment)
    setShowApartmentDialog(true)
  }

  const handleDelete = (apartment: Apartment) => {
    setDeletingApartment(apartment)
    setShowDeleteDialog(true)
  }

  const handleCreateNew = () => {
    setEditingApartment(null)
    setShowApartmentDialog(true)
  }

  const handleDialogClose = () => {
    setShowApartmentDialog(false)
    setEditingApartment(null)
    fetchApartments()
  }
  const handleDeleteDialogClose = () => {
    setShowDeleteDialog(false)
    setDeletingApartment(null)
    fetchApartments()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Yükleniyor...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      {showPageHeader && (
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {preselectedBlockId
              ? `${selectedBlockInfo?.ad || (Array.isArray(blocks) ? blocks.find(b => b.id === preselectedBlockId)?.ad : null) || "Blok"} Blokuna Ait Daireler`
              : "Daireler"
            }
          </h1>
          <p className="text-muted-foreground">
            {preselectedBlockId
              ? `${selectedBlockInfo?.ad || (Array.isArray(blocks) ? blocks.find(b => b.id === preselectedBlockId)?.ad : null) || "Blok"} bloğuna ait daireleri ekleyin, düzenleyin ve yönetin.`
              : "Daireleri ekleyin, düzenleyin ve yönetin."
            }
          </p>
        </div>
      )}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Daire ara..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-8 w-80"
            />
          </div>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="h-4 w-4 mr-2" />
          Yeni Daire
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Daire</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Proje</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-medium">
              {selectedBlockInfo?.proje?.ad || 
               (Array.isArray(projects) 
                ? projects.find(p => p.id === preselectedProjectId)?.ad || "Proje"
                : "Proje"
               )
              }
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blok</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-medium">
              {selectedBlockInfo?.ad || 
               (Array.isArray(blocks)
                ? blocks.find(b => b.id === preselectedBlockId)?.ad || "Blok"
                : "Blok"
               )
              }
            </div>
          </CardContent>
        </Card>
      </div>      {/* Table */}      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Daire No</TableHead>
              <TableHead>Blok</TableHead>
              <TableHead>Proje</TableHead>
              <TableHead>Kat</TableHead>
              <TableHead>Arıza Sayısı</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {apartments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                  {searchQuery || selectedProject !== "all" 
                    ? "Arama kriterlerine uygun daire bulunamadı" 
                    : "Henüz daire eklenmemiş"
                  }
                </TableCell>
              </TableRow>
            ) : (apartments.map((apartment) => (
                <TableRow 
                  key={apartment.id}                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleRowClick(apartment)}
                >
                  <TableCell>
                    <div className="font-medium">{apartment.numara}</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{apartment.blok.ad}</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{apartment.blok.proje.ad}</div>
                  </TableCell>
                  <TableCell>{apartment.kat || "-"}</TableCell>
                  <TableCell>{apartment._count.arizalar}</TableCell>
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEdit(apartment)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Düzenle
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDelete(apartment)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Sil
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Toplam {totalCount} daireden {((currentPage - 1) * limit) + 1}-{Math.min(currentPage * limit, totalCount)} arası gösteriliyor
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Önceki
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter(page => 
                  page === 1 || 
                  page === totalPages || 
                  (page >= currentPage - 1 && page <= currentPage + 1)
                )
                .map((page, index, array) => (
                  <div key={page} className="flex items-center">
                    {index > 0 && array[index - 1] !== page - 1 && (
                      <span className="px-2 text-muted-foreground">...</span>
                    )}
                    <Button
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  </div>
                ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Sonraki
            </Button>          </div>
        </div>
      )}

      {/* Dialogs */}      <ApartmentDialog
        open={showApartmentDialog}
        onClose={handleDialogClose}
        apartment={editingApartment}
        preselectedProjectId={preselectedProjectId}
        preselectedBlockId={preselectedBlockId}
      />

      <DeleteApartmentDialog
        open={showDeleteDialog}
        onClose={handleDeleteDialogClose}
        apartment={deletingApartment}
      />
    </div>
  )
}
