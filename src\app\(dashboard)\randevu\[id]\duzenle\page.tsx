"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useLoading } from "@/contexts/loading-context"
import { <PERSON>Lef<PERSON>, Calendar, Save, X } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { AppointmentStatus, APPOINTMENT_STATUS_LABELS, APPOINTMENT_STATUS_COLORS } from "@/lib/enums"
import { Breadcrumb } from "@/components/layout/breadcrumb"

interface EditAppointmentPageProps {
  params: Promise<{ id: string }>
}

interface AppointmentData {
  id: string
  randevu_tarihi: string
  durum: AppointmentStatus
  aciklama: string | null
  ariza: {
    id: string
    numara: string
    baslik: string
    slug: string
    daire: {
      numara: string
      slug: string
      blok: {
        ad: string
        slug: string
        proje: { 
          ad: string
          slug: string
        }
      }
    }
  }
}

const formatDateTimeForInput = (dateInput: string | Date) => {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput
  return date.toISOString().slice(0, 16) // YYYY-MM-DDTHH:mm format
}

export default function EditAppointmentPage({ params }: EditAppointmentPageProps) {
  const [appointment, setAppointment] = useState<AppointmentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [appointmentId, setAppointmentId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    randevu_tarihi: "",
    durum: "",
    aciklama: "",
  })

  const router = useRouter()
  const { startLoading } = useLoading()

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParams = await params
      setAppointmentId(resolvedParams.id)
    }
    loadParams()
  }, [params])

  const loadAppointmentData = async () => {
    if (!appointmentId) return
    
    try {
      setLoading(true)
      const response = await fetch(`/api/appointments/${appointmentId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch appointment data')
      }
      const data = await response.json()
      setAppointment(data)
      
      // Set form data
      setFormData({
        randevu_tarihi: formatDateTimeForInput(data.randevu_tarihi),
        durum: data.durum,
        aciklama: data.aciklama || "",
      })
    } catch (error) {
      console.error('Error loading appointment data:', error)
      toast.error("Randevu bilgileri yüklenirken hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (appointmentId) {
      loadAppointmentData()
    }
  }, [appointmentId])
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!appointmentId) return

    // Validation
    if (!formData.randevu_tarihi) {
      toast.error("Randevu tarihi ve saati gereklidir")
      return
    }

    if (!formData.durum) {
      toast.error("Randevu durumu seçmelisiniz")
      return
    }

    // Check if date is in the past (only for new scheduled appointments)
    const selectedDate = new Date(formData.randevu_tarihi)
    const now = new Date()
    if (selectedDate < now && formData.durum === "PLANLI") {
      toast.warning("Geçmiş bir tarih için 'Planlı' durum seçemezsiniz")
      return
    }

    try {
      setSaving(true)
      
      const response = await fetch(`/api/appointments/${appointmentId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          randevu_tarihi: new Date(formData.randevu_tarihi).toISOString(),
          durum: formData.durum,
          aciklama: formData.aciklama || null,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update appointment")
      }      toast.success("Randevu başarıyla güncellendi")

      startLoading()
      if (appointment) {
        router.push(`/randevu/${appointment.id}`)
      }
    } catch (error) {
      console.error("Error updating appointment:", error)
      toast.error(error instanceof Error ? error.message : "Randevu güncellenirken bir hata oluştu")
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Randevu bilgileri yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (!appointment) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">Randevu bulunamadı</p>
          <Button asChild>
            <Link href="/dashboard">
              Dashboard&apos;a Dön
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" asChild>
          <Link href={`/randevu/${appointment.id}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Randevu Detayına Dön
          </Link>
        </Button>
        <div className="flex-1">
          <h2 className="text-3xl font-bold tracking-tight">
            Randevu Düzenle
          </h2>
          <p className="text-muted-foreground">
            {appointment.ariza.baslik} - Arıza #{appointment.ariza.id}
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Form */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Randevu Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">                  <div className="space-y-2">
                    <Label htmlFor="randevu_tarihi">Randevu Tarihi & Saati</Label>
                    <Input
                      id="randevu_tarihi"
                      type="datetime-local"
                      value={formData.randevu_tarihi}
                      onChange={(e) => setFormData(prev => ({ ...prev, randevu_tarihi: e.target.value }))}
                      min={new Date().toISOString().slice(0, 16)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="durum">Durum</Label>
                    <Select
                      value={formData.durum}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, durum: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Durum seçin" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PLANLI">Planlı</SelectItem>
                        <SelectItem value="DEVAM_EDIYOR">Devam Ediyor</SelectItem>
                        <SelectItem value="TAMAMLANDI">Tamamlandı</SelectItem>
                        <SelectItem value="IPTAL">İptal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>                <div className="space-y-2">
                  <Label htmlFor="aciklama">Açıklama</Label>
                  <Textarea
                    id="aciklama"
                    placeholder="Randevu hakkında notlar..."
                    value={formData.aciklama}
                    onChange={(e) => setFormData(prev => ({ ...prev, aciklama: e.target.value }))}
                    rows={4}
                    maxLength={500}
                  />
                  <p className="text-xs text-muted-foreground text-right">
                    {formData.aciklama.length}/500 karakter
                  </p>
                </div>

                <div className="flex gap-2">
                  <Button type="submit" disabled={saving}>
                    {saving ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Kaydediliyor...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Kaydet
                      </>
                    )}
                  </Button>
                  <Button type="button" variant="outline" asChild>
                    <Link href={`/randevu/${appointment.id}`}>
                      <X className="mr-2 h-4 w-4" />
                      İptal
                    </Link>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* İlgili Arıza */}
          <Card>
            <CardHeader>
              <CardTitle>İlgili Arıza</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium">Arıza Numarası</p>
                  <p className="text-sm text-muted-foreground">{appointment.ariza.id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Başlık</p>
                  <p className="text-sm text-muted-foreground">{appointment.ariza.baslik}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Konum</p>
                  <p className="text-sm text-muted-foreground">
                    {appointment.ariza.daire.blok.proje.ad} - {appointment.ariza.daire.blok.ad} - Daire {appointment.ariza.daire.numara}
                  </p>
                </div>
                <Button asChild variant="outline" size="sm" className="w-full">
                  <Link href={`/projeler/${appointment.ariza.daire.blok.proje.slug}/bloklar/${appointment.ariza.daire.blok.slug}/${appointment.ariza.daire.slug}/arizalar/${appointment.ariza.id}`}>
                    Arıza #{appointment.ariza.numara}
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Mevcut Durum */}
          <Card>
            <CardHeader>
              <CardTitle>Mevcut Durum</CardTitle>
            </CardHeader>
            <CardContent>
              <Badge 
                style={{ 
                  backgroundColor: APPOINTMENT_STATUS_COLORS[appointment.durum as AppointmentStatus] || '#6B7280',
                  color: 'white'
                }}
              >
                {APPOINTMENT_STATUS_LABELS[appointment.durum as AppointmentStatus] || appointment.durum}
              </Badge>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
