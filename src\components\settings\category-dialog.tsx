import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Wrench, Zap, Droplets, Home, Settings } from 'lucide-react'

interface CategoryData {
  ad: string
  aciklama: string
  icon: string
  renk: string
}

interface CategoryDialogProps {
  category?: any
  isOpen: boolean
  onClose: () => void
  onSave: (data: CategoryData) => void
}

export function CategoryDialog({ category, isOpen, onClose, onSave }: CategoryDialogProps) {
  const [formData, setFormData] = useState<CategoryData>({
    ad: category?.ad || '',
    aciklama: category?.aciklama || '',
    icon: category?.icon || '🔧',
    renk: category?.renk || '#6B7280'
  })

  const basicIcons = [
    { name: '🔧', label: 'Onarım' },
    { name: '⚡', label: 'Elektrik' },
    { name: '💧', label: 'Su' },
    { name: '🏠', label: 'Ev' },
    { name: '⚙️', label: 'Ayarlar' },
    { name: '🔨', label: 'Çekiç' },
    { name: '🎨', label: 'Boya' },
    { name: '🔐', label: 'Kilit' },
  ]

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {category ? 'Kategori Düzenle' : 'Yeni Kategori'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Kategori Adı *</Label>
              <Input
                id="name"
                value={formData.ad}
                onChange={(e) => setFormData(prev => ({ ...prev, ad: e.target.value }))}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="color">Renk</Label>
              <Input
                id="color"
                type="color"
                value={formData.renk}
                onChange={(e) => setFormData(prev => ({ ...prev, renk: e.target.value }))}
              />
            </div>
          </div>

          <div>
            <Label>İkon Seç</Label>
            <div className="grid grid-cols-8 gap-2 mt-2">
              {basicIcons.map((icon) => (
                <Button
                  key={icon.name}
                  type="button"
                  variant={formData.icon === icon.name ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFormData(prev => ({ ...prev, icon: icon.name }))}
                  className="h-10 text-lg"
                >
                  {icon.name}
                </Button>
              ))}
            </div>
          </div>

          <div>
            <Label htmlFor="description">Açıklama</Label>
            <Textarea
              id="description"
              value={formData.aciklama}
              onChange={(e) => setFormData(prev => ({ ...prev, aciklama: e.target.value }))}
              placeholder="Kategori açıklaması..."
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              İptal
            </Button>
            <Button type="submit">
              {category ? 'Güncelle' : 'Kaydet'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 