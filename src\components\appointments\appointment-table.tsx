"use client"

import { useState } from "react"
import { Plus, Calendar, Clock } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AddAppointmentDialog } from "./add-appointment-dialog"
import { AppointmentStatus, APPOINTMENT_STATUS_LABELS, APPOINTMENT_STATUS_COLORS } from "@/lib/enums"

interface Technician {
  id: string
  ad: string
  soyad: string
  email: string
  telefon: string | null
  resim: string | null
}

interface AppointmentTechnician {
  teknisyen: Technician
}

interface Appointment {
  id: string
  randevu_tarihi: Date
  durum: AppointmentStatus
  aciklama: string | null
  teknisyenler: AppointmentTechnician[]
  daire: {
    blok: {
      proje: {
        slug: string
      }
      slug: string
    }
    slug: string
  }
  ariza_id: string
}

interface AppointmentTableProps {
  faultId: string
  appointments: Appointment[]
  onAppointmentAdded: () => void
  faultStatus: string
}

const formatDate = (dateInput: string | Date) => {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput
  return date.toLocaleDateString("tr-TR", {
    day: "2-digit",
    month: "2-digit", 
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })
}

export function AppointmentTable({ faultId, appointments, onAppointmentAdded, faultStatus }: AppointmentTableProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const isOpen = faultStatus === "Açık"

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Randevular
          </CardTitle>
          {isOpen ? (
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Randevu Ekle
            </Button>
          ) : (
            <div className="group relative">
              <Button disabled>
                <Plus className="mr-2 h-4 w-4" />
                Randevu Ekle
              </Button>
              <div className="absolute left-1/2 -translate-x-1/2 mt-2 w-max bg-muted text-xs text-muted-foreground rounded px-2 py-1 shadow z-50 opacity-0 group-hover:opacity-100 transition-opacity">
                Sadece "Açık" durumdaki arızalara randevu eklenebilir.
              </div>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {appointments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>Henüz randevu bulunmuyor</p>
            <p className="text-sm">Yeni randevu eklemek için yukarıdaki butona tıklayın</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tarih & Saat</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead>Teknisyenler</TableHead>
                <TableHead>Açıklama</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {appointments.map((appointment) => {
                const isCompleted = appointment.durum === AppointmentStatus.TAMAMLANDI
                
                return (
                  <TableRow 
                    key={appointment.id} 
                    className={`cursor-pointer transition-colors ${
                      isCompleted 
                        ? "bg-green-50 hover:bg-green-100 border-l-4 border-l-green-500" 
                        : "hover:bg-muted/50"
                    }`}
                    onClick={() => {
                      // Navigate to appointment detail page
                      window.location.href = `/projeler/${appointment.daire.blok.proje.slug}/bloklar/${appointment.daire.blok.slug}/${appointment.daire.slug}/arizalar/${appointment.ariza_id}/randevular/${appointment.id}`
                    }}
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        {formatDate(appointment.randevu_tarihi)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        style={{ 
                          backgroundColor: APPOINTMENT_STATUS_COLORS[appointment.durum] + "20",
                          borderColor: APPOINTMENT_STATUS_COLORS[appointment.durum],
                          color: APPOINTMENT_STATUS_COLORS[appointment.durum]
                        }}
                      >
                        {APPOINTMENT_STATUS_LABELS[appointment.durum]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex -space-x-2">
                        {appointment.teknisyenler.slice(0, 3).map((at) => (
                          <Avatar key={at.teknisyen.id} className="h-8 w-8 border-2 border-background">
                            <AvatarImage src={at.teknisyen.resim || ""} />
                            <AvatarFallback className="text-xs">
                              {at.teknisyen.ad.charAt(0)}{at.teknisyen.soyad.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                        {appointment.teknisyenler.length > 3 && (
                          <div className="h-8 w-8 rounded-full border-2 border-background bg-muted flex items-center justify-center text-xs font-medium">
                            +{appointment.teknisyenler.length - 3}
                          </div>
                        )}
                      </div>
                      {appointment.teknisyenler.length === 0 && (
                        <span className="text-muted-foreground text-sm">Teknisyen atanmamış</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {appointment.aciklama || "Açıklama yok"}
                      </span>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        )}
      </CardContent>

      <AddAppointmentDialog
        faultId={faultId}
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSuccess={() => {
          setIsAddDialogOpen(false)
          onAppointmentAdded()
        }}
      />
    </Card>
  )
}
