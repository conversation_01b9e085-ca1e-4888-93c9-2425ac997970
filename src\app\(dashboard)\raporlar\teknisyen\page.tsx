"use client"

import { useQuery } from "@tanstack/react-query"
import { useState } from "react"
import { 
  Users, 
  TrendingUp, 
  Download, 
  Star, 
  BarChart3,
  Clock,
  Award,
  Target,
  Calendar
} from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from "recharts"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Breadcrumb } from "@/components/layout/breadcrumb"

const techniciansFetcher = async () => {
  const res = await fetch("/api/technicians")
  return res.json()
}

const appointmentsFetcher = async () => {
  const res = await fetch("/api/appointments")
  return res.json()
}

export default function TechnicianReportsPage() {
  const [dateFilter, setDateFilter] = useState("this_month")
  
  const { data: technicians, isLoading: techniciansLoading } = useQuery({
    queryKey: ["technicians"],
    queryFn: techniciansFetcher
  })

  const { data: appointments, isLoading: appointmentsLoading } = useQuery({
    queryKey: ["appointments"],
    queryFn: appointmentsFetcher
  })

  if (techniciansLoading || appointmentsLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-white/50 rounded-xl animate-pulse"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
          <div className="h-80 bg-white/50 rounded-xl animate-pulse"></div>
        </div>
      </div>
    )
  }

  const technicianList = technicians || []
  const appointmentList = appointments || []

  // Calculate technician performance
  const technicianPerformance = technicianList.map((tech: any) => {
    const techAppointments = appointmentList.filter((app: any) => 
      app.teknisyenler?.some((at: any) => at.teknisyen?.id === tech.id)
    )
    const completedAppointments = techAppointments.filter((app: any) => app.durum === 'TAMAMLANDI')
    
    return {
      id: tech.id,
      name: `${tech.ad} ${tech.soyad}`,
      email: tech.email,
      total: techAppointments.length,
      completed: completedAppointments.length,
      success_rate: techAppointments.length > 0 ? Math.round((completedAppointments.length / techAppointments.length) * 100) : 0,
      expertise: tech.uzmanlikAlanlari || []
    }
  })

  // Performance data for charts
  const performanceData = technicianPerformance.map(tech => ({
    name: tech.name.split(' ')[0], // İlk isim
    tamamlanan: tech.completed,
    toplam: tech.total,
    basari_orani: tech.success_rate
  }))

  // Monthly performance data (mock)
  const monthlyData = [
    { month: 'Oca', tamamlanan: 45, hedef: 50 },
    { month: 'Şub', tamamlanan: 52, hedef: 50 },
    { month: 'Mar', tamamlanan: 48, hedef: 50 },
    { month: 'Nis', tamamlanan: 58, hedef: 50 },
    { month: 'May', tamamlanan: 55, hedef: 50 },
    { month: 'Haz', tamamlanan: 62, hedef: 50 },
  ]

  // Top performers
  const topPerformers = technicianPerformance
    .sort((a, b) => b.success_rate - a.success_rate)
    .slice(0, 5)

  const totalTechnicians = technicianList.length
  const activeTechnicians = technicianList.filter((tech: any) => tech.aktif !== false).length
  const totalAppointments = appointmentList.length
  const avgSuccessRate = technicianPerformance.length > 0 
    ? Math.round(technicianPerformance.reduce((sum, tech) => sum + tech.success_rate, 0) / technicianPerformance.length)
    : 0

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            👨‍🔧 Teknisyen Raporları
          </h1>
          <p className="text-muted-foreground mt-1">
            Teknisyen performansı, iş yükü ve verimlilik analizi
          </p>
        </div>
        <div className="flex gap-3">
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Zaman aralığı" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="this_week">Bu Hafta</SelectItem>
              <SelectItem value="this_month">Bu Ay</SelectItem>
              <SelectItem value="last_month">Geçen Ay</SelectItem>
              <SelectItem value="last_3_months">Son 3 Ay</SelectItem>
            </SelectContent>
          </Select>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Toplam Teknisyen</p>
                <p className="text-2xl font-bold text-blue-900">{totalTechnicians}</p>
                <p className="text-xs text-blue-600 mt-1">{activeTechnicians} aktif</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Ortalama Başarı</p>
                <p className="text-2xl font-bold text-green-900">%{avgSuccessRate}</p>
                <p className="text-xs text-green-600 mt-1">Tamamlama oranı</p>
              </div>
              <Award className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Toplam Randevu</p>
                <p className="text-2xl font-bold text-purple-900">{totalAppointments}</p>
                <p className="text-xs text-purple-600 mt-1">Bu ay</p>
              </div>
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Ortalama Süre</p>
                <p className="text-2xl font-bold text-orange-900">2.4</p>
                <p className="text-xs text-orange-600 mt-1">Saat/randevu</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance Chart */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                Teknisyen Performansı
              </CardTitle>
              <CardDescription>
                Tamamlanan randevu sayısı ve başarı oranları
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="tamamlanan" fill="#10B981" name="Tamamlanan" />
                    <Bar dataKey="toplam" fill="#3B82F6" name="Toplam" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Monthly Trend */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                Aylık Performans Trendi
              </CardTitle>
              <CardDescription>
                Tamamlanan randevu sayısı vs hedef
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="tamamlanan" stroke="#10B981" strokeWidth={3} name="Tamamlanan" />
                    <Line type="monotone" dataKey="hedef" stroke="#F59E0B" strokeWidth={2} strokeDasharray="5 5" name="Hedef" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Performers Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-600" />
                En İyi Performans
              </CardTitle>
              <CardDescription>
                Başarı oranına göre sıralama
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {topPerformers.map((tech, index) => (
                <div key={tech.id} className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center gap-3 flex-1">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-sm font-bold">
                      {index + 1}
                    </div>
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        {tech.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{tech.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Progress value={tech.success_rate} className="h-2 flex-1" />
                        <span className="text-xs text-muted-foreground">%{tech.success_rate}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-green-600" />
                Performans Özeti
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Mükemmel (%90+)</span>
                  <span className="font-medium">
                    {technicianPerformance.filter(t => t.success_rate >= 90).length}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">İyi (%70-89)</span>
                  <span className="font-medium">
                    {technicianPerformance.filter(t => t.success_rate >= 70 && t.success_rate < 90).length}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Orta (%50-69)</span>
                  <span className="font-medium">
                    {technicianPerformance.filter(t => t.success_rate >= 50 && t.success_rate < 70).length}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Düşük (%50 altı)</span>
                  <span className="font-medium">
                    {technicianPerformance.filter(t => t.success_rate < 50).length}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 