#!/bin/bash

echo "🐳 ML Fault Classifier Docker Build Başlıyor..."

# Docker image'ını build et
echo "📦 Docker image build ediliyor..."
docker build -t ml-fault-classifier:latest .

if [ $? -eq 0 ]; then
    echo "✅ Docker image başarıyla build edildi!"
    echo "   Image: ml-fault-classifier:latest"
    echo ""
    echo "🚀 Çalıştırmak için:"
    echo "   docker run -p 3010:3010 ml-fault-classifier:latest"
    echo ""
    echo "🔧 Docker Compose ile:"
    echo "   docker-compose up -d"
else
    echo "❌ Docker build hatası!"
    exit 1
fi 