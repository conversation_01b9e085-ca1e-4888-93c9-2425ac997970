#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Renk kodları console output için
const colors = {
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function updateVersion() {
  // Command line arguments'ları al
  const args = process.argv.slice(2);
  if (args.length !== 2) {
    log('❌ Kullanım: node scripts/update-version.js <yeni-sürüm> <yeni-title>', 'red');
    log('📝 Örnek: node scripts/update-version.js 1.7.0 "Mobile Optimization"', 'yellow');
    process.exit(1);
  }

  const [newVersion, newTitle] = args;
  
  log(`\n🚀 Sürüm güncelleme başlıyor...`, 'blue');
  log(`📦 Yeni sürüm: ${newVersion}`, 'green');
  log(`🏷️  Yeni title: ${newTitle}`, 'green');

  try {
    // 1. package.json güncelle
    log('\n1️⃣ package.json güncelleniyor...', 'blue');
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const oldVersion = packageJson.version;
    packageJson.version = newVersion;
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
    log(`✅ package.json: ${oldVersion} → ${newVersion}`, 'green');

    // 2. Footer component güncelle
    log('\n2️⃣ Footer component güncelleniyor...', 'blue');
    const footerPath = path.join(process.cwd(), 'src/components/ui/app-footer.tsx');
    let footerContent = fs.readFileSync(footerPath, 'utf8');
    
    // Desktop layout tag'ini güncelle
    const desktopTagRegex = /<span className="text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full">\s*[^<]+\s*<\/span>/;
    const newDesktopTag = `<span className="text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full">
              🔧 ${newTitle}
            </span>`;
    footerContent = footerContent.replace(desktopTagRegex, newDesktopTag);

    // Mobile layout tag'ini güncelle
    const mobileTagRegex = /<span className="text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full">\s*[^<]+\s*<\/span>/;
    const newMobileTag = `<span className="text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full">
              🔧 ${newTitle} v${newVersion}
              </span>`;
    footerContent = footerContent.replace(mobileTagRegex, newMobileTag);
    
    fs.writeFileSync(footerPath, footerContent);
    log(`✅ Footer güncellendi: ${newTitle}`, 'green');

    // 3. README.md'de changelog template'i hazırla
    log('\n3️⃣ README.md changelog template hazırlanıyor...', 'blue');
    const readmePath = path.join(process.cwd(), 'README.md');
    let readmeContent = fs.readFileSync(readmePath, 'utf8');
    
    const today = new Date().toLocaleDateString('tr-TR');
    const newChangelogEntry = `### v${newVersion} (${today})
#### 🚀 Yeni Özellikler
- [Buraya yeni özellikler ekleyin]

#### 🐛 Düzeltmeler  
- [Buraya düzeltmeler ekleyin]

#### ✨ İyileştirmeler
- [Buraya iyileştirmeler ekleyin]

`;

    // Changelog bölümünü bul ve yeni entry'yi ekle
    const changelogIndex = readmeContent.indexOf('## 📝 Changelog');
    if (changelogIndex !== -1) {
      const nextSectionIndex = readmeContent.indexOf('\n### v', changelogIndex);
      const insertIndex = nextSectionIndex !== -1 ? nextSectionIndex + 1 : readmeContent.indexOf('\n\n### v1.');
      
      readmeContent = readmeContent.slice(0, insertIndex) + newChangelogEntry + readmeContent.slice(insertIndex);
      fs.writeFileSync(readmePath, readmeContent);
      log(`✅ README.md'de changelog template oluşturuldu`, 'green');
    }

    // 4. Özet
    log(`\n${colors.bold}${colors.green}🎉 Sürüm güncelleme tamamlandı!${colors.reset}`);
    log(`\n📋 Yapılan değişiklikler:`, 'blue');
    log(`   • package.json: v${oldVersion} → v${newVersion}`, 'reset');
    log(`   • Footer title: "${newTitle}"`, 'reset');
    log(`   • README.md changelog template eklendi`, 'reset');
    
    log(`\n🔄 Sonraki adımlar:`, 'yellow');
    log(`   1. README.md'de changelog detaylarını doldurun`, 'reset');
    log(`   2. Değişiklikleri test edin`, 'reset');
    log(`   3. Git commit yapın: "chore: bump version to v${newVersion}"`, 'reset');

  } catch (error) {
    log(`\n❌ Hata oluştu: ${error.message}`, 'red');
    process.exit(1);
  }
}

updateVersion(); 