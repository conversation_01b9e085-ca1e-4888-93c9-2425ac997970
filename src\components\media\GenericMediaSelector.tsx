"use client";

import { useState, use<PERSON><PERSON>back, create<PERSON>ontex<PERSON>, use<PERSON>ontext, ReactNode, useEffect } from "react";
// import { MediaSelector } from "@/components/media/MediaSelector"; // To be implemented
import { Button } from "@/components/ui/button";
import { Image } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import Cropper from "react-easy-crop";
import { useRef } from "react";

// Generic media file interface
export interface GenericMediaFile<T = any> {
  id: number | string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  alt?: string;
  caption?: string;
  thumbnailSmall?: string;
  thumbnailMedium?: string;
  thumbnailLarge?: string;
  categoryId?: number | string;
  category?: {
    id: number | string;
    name: string;
    [key: string]: any;
  };
  createdAt: string;
  updatedAt?: string;
  metadata?: T; // Generic metadata field
  [key: string]: any; // Allow additional properties
}

// Generic category configuration
export interface MediaCategory<T = any> {
  id: number | string;
  key: string;
  name: string;
  description?: string;
  title?: string;
  acceptedTypes?: string[];
  folder?: string;
  maxSize?: number;
  targetDimensions?: {
    width: number;
    height: number;
  };
  metadata?: T;
}

// Configuration for the media selector
export interface MediaSelectorConfig<T = any> {
  categories: MediaCategory<T>[];
  defaultCategory?: string;
  acceptedTypes?: string[];
  maxFileSize?: number;
  uploadEndpoint?: string;
  apiEndpoint?: string;
  customFolder?: string;
  allowMultiSelect?: boolean;
  allowCategorySwitch?: boolean;
  showPreview?: boolean;
  showMetadata?: boolean;
  translations?: {
    selectButton?: string;
    selectTitle?: string;
    selectDescription?: string;
    noCategory?: string;
    [key: string]: string | undefined;
  };
}

// Context for sharing configuration
interface MediaSelectorContextValue<T = any> {
  config: MediaSelectorConfig<T>;
  updateConfig: (newConfig: Partial<MediaSelectorConfig<T>>) => void;
}

const MediaSelectorContext = createContext<MediaSelectorContextValue | null>(null);

// Provider component
export function MediaSelectorProvider<T = any>({ 
  children, 
  config 
}: { 
  children: ReactNode;
  config: MediaSelectorConfig<T>;
}) {
  const [currentConfig, setCurrentConfig] = useState(config);

  const updateConfig = useCallback((newConfig: Partial<MediaSelectorConfig<T>>) => {
    setCurrentConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  return (
    <MediaSelectorContext.Provider value={{ config: currentConfig, updateConfig }}>
      {children}
    </MediaSelectorContext.Provider>
  );
}

// Hook to use context
export function useMediaSelectorConfig<T = any>() {
  const context = useContext(MediaSelectorContext);
  if (!context) {
    throw new Error('useMediaSelectorConfig must be used within MediaSelectorProvider');
  }
  return context as MediaSelectorContextValue<T>;
}

// Generic selector props
export interface GenericMediaSelectorProps<T = any> {
  onSelect?: (media: GenericMediaFile<T>) => void;
  onMultiSelect?: (media: GenericMediaFile<T>[]) => void;
  trigger?: ReactNode;
  title?: string;
  description?: string;
  categoryKey?: string;
  categoryId?: number | string;
  restrictToCategory?: boolean;
  acceptedTypes?: string[];
  targetWidth?: number;
  targetHeight?: number;
  width?: number;
  height?: number;
  buttonText?: string;
  showPreview?: boolean;
  selectedMedia?: GenericMediaFile<T> | null;
  selectedItems?: GenericMediaFile<T>[];
  multiSelect?: boolean;
  customFolder?: string;
  maxFileSize?: number;
  onUploadProgress?: (progress: number) => void;
  onUploadComplete?: (media: GenericMediaFile<T>) => void;
  onError?: (error: Error) => void;
  config?: Partial<MediaSelectorConfig<T>>;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
}

// Helper function to find category
function findCategory<T>(
  categories: MediaCategory<T>[], 
  keyOrId: string | number
): MediaCategory<T> | undefined {
  return categories.find(cat => 
    cat.key === keyOrId || 
    cat.id === keyOrId || 
    cat.id.toString() === keyOrId.toString()
  );
}

// Main generic component
export function GenericMediaSelector<T = any>({
  onSelect,
  trigger,
  title,
  description,
  categoryKey,
  acceptedTypes,
  buttonText,
  selectedMedia,
  customFolder,
  disabled = false,
  ...rest
}: GenericMediaSelectorProps<T>) {
  const [open, setOpen] = useState(false);
  const [tab, setTab] = useState<'gallery' | 'upload'>("gallery");
  const [mediaList, setMediaList] = useState<GenericMediaFile<T>[]>([]);
  const [uploading, setUploading] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Try to get config from context, fallback to props
  let config: MediaSelectorConfig<T>;
  try {
    const { config: contextConfig } = useMediaSelectorConfig<T>();
    config = { ...contextConfig, ...rest.config };
  } catch {
    // Not in provider context, use props config or defaults
    const defaultConfig: MediaSelectorConfig<T> = {
      categories: [],
      acceptedTypes: ['image/*'],
      translations: {}
    };
    config = { ...defaultConfig, ...rest.config };
  }

  // Find the target category
  const targetCategory = categoryKey 
    ? findCategory(config.categories, categoryKey)
    : config.categories.find(cat => cat.key === config.defaultCategory);

  // Determine final values
  const finalTitle = title || 
    targetCategory?.title || 
    config.translations?.selectTitle || 
    "Select Media";
    
  const finalDescription = description || 
    targetCategory?.description || 
    config.translations?.selectDescription || 
    "Choose media from gallery or upload new";
    
  const finalButtonText = buttonText || 
    config.translations?.selectButton || 
    "Select Media";
    
  const finalAcceptedTypes = acceptedTypes || 
    targetCategory?.acceptedTypes || 
    config.acceptedTypes || 
    ['image/*'];
    
  const finalCustomFolder = customFolder || 
    targetCategory?.folder || 
    config.customFolder || 
    'media';

  const finalTargetWidth = targetCategory?.targetDimensions?.width || 800;
  const finalTargetHeight = targetCategory?.targetDimensions?.height || 600;

  // Default trigger
  const defaultTrigger = (
    <Button 
      type="button"
      variant="outline" 
      className={`w-full ${rest.className || ''}`}
      disabled={disabled || rest.loading}
    >
      <Image className="w-4 h-4 mr-2" />
      {rest.loading ? "Loading..." : 
       selectedMedia ? selectedMedia.originalName : 
       finalButtonText}
    </Button>
  );

  // Galeri verisini çek
  const fetchMedia = async () => {
    const res = await fetch(`/api/media?customFolder=${finalCustomFolder}`);
    const data = await res.json();
    setMediaList(data.data || []);
  };
  useEffect(() => { if (open && tab === "gallery") fetchMedia(); }, [open, tab]);

  // Dosya seçildiğinde önizleme hazırla
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return;
    setSelectedFile(e.target.files[0]);
    setPreviewUrl(URL.createObjectURL(e.target.files[0]));
    setTab("upload");
  };

  // Kırpma tamamlandığında alanı kaydet
  const onCropComplete = (croppedArea: any, croppedAreaPixels: any) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };

  // Kırpılmış görseli blob olarak al
  const getCroppedImg = async () => {
    if (!previewUrl || !croppedAreaPixels) return null;
    const image = await createImage(previewUrl);
    const canvas = document.createElement('canvas');
    canvas.width = croppedAreaPixels.width;
    canvas.height = croppedAreaPixels.height;
    const ctx = canvas.getContext('2d');
    ctx?.drawImage(
      image,
      croppedAreaPixels.x,
      croppedAreaPixels.y,
      croppedAreaPixels.width,
      croppedAreaPixels.height,
      0,
      0,
      croppedAreaPixels.width,
      croppedAreaPixels.height
    );
    return new Promise<Blob | null>((resolve) => {
      canvas.toBlob((blob) => resolve(blob), 'image/jpeg');
    });
  };

  // Kırpılmış görseli yükle
  const handleUpload = async () => {
    setUploading(true);
    try {
      const croppedBlob = await getCroppedImg();
      if (!croppedBlob) throw new Error("Kırpılmış görsel alınamadı");
      const formData = new FormData();
      formData.append("file", new File([croppedBlob], selectedFile?.name || "cropped.jpg"));
      formData.append("folder", finalCustomFolder);
      const res = await fetch("/api/upload", { method: "POST", body: formData });
      const data = await res.json();
      setUploading(false);
      setTab("gallery");
      setSelectedFile(null);
      setPreviewUrl(null);
      if (data && data.media) {
        setMediaList((prev) => [data.media, ...prev]);
        handleSelectMedia(data.media);
      } else {
        alert(data?.error || "Yükleme başarısız!");
        fetchMedia();
      }
    } catch (err) {
      setUploading(false);
      alert("Yükleme sırasında hata oluştu: " + (err as Error).message);
      console.error("Yükleme hatası:", err);
    }
  };

  // Görsel seçildiğinde üst bileşene ilet
  const handleSelectMedia = (media: GenericMediaFile<T>) => {
    onSelect?.(media);
    setOpen(false);
  };

  // Görsel yükleme için yardımcı
  function createImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new window.Image();
      img.addEventListener('load', () => resolve(img));
      img.addEventListener('error', (err) => reject(err));
      img.setAttribute('crossOrigin', 'anonymous');
      img.src = url;
    });
  }

  return (
    <>
      <span onClick={() => setOpen(true)}>{trigger || defaultTrigger}</span>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{finalTitle}</DialogTitle>
            {finalDescription && <div className="text-sm text-gray-500">{finalDescription}</div>}
          </DialogHeader>
          <Tabs value={tab} onValueChange={setTab as any}>
            <TabsList>
              <TabsTrigger value="gallery">Galeri</TabsTrigger>
              <TabsTrigger value="upload">Yükle</TabsTrigger>
            </TabsList>
            <TabsContent value="gallery">
              <div className="grid grid-cols-3 gap-4 mt-4">
                {mediaList.length === 0 && <div>Hiç dosya yok</div>}
                {mediaList.map((media) => (
                  <div
                    key={media.id}
                    className={`border rounded p-2 cursor-pointer ${selectedMedia?.id === media.id ? "border-blue-500" : ""}`}
                    onClick={() => handleSelectMedia(media)}
                  >
                    <img src={media.url} alt={media.originalName} className="w-full h-24 object-cover" />
                    <div className="text-xs mt-1">{media.originalName}</div>
                  </div>
                ))}
              </div>
            </TabsContent>
            <TabsContent value="upload">
              <div className="mt-4 space-y-4">
                {!previewUrl && (
                  <Input type="file" accept={finalAcceptedTypes.join(",")} onChange={handleFileChange} disabled={uploading} ref={fileInputRef} />
                )}
                {previewUrl && (
                  <div className="relative w-full h-64 bg-gray-100">
                    <Cropper
                      image={previewUrl}
                      crop={crop}
                      zoom={zoom}
                      aspect={finalTargetWidth / finalTargetHeight}
                      onCropChange={setCrop}
                      onZoomChange={setZoom}
                      onCropComplete={onCropComplete}
                    />
                  </div>
                )}
                {previewUrl && (
                  <div className="flex gap-2 mt-2">
                    <Button type="button" onClick={handleUpload} disabled={uploading}>{uploading ? "Yükleniyor..." : "Yükle ve Kaydet"}</Button>
                    <Button type="button" variant="outline" onClick={() => { setPreviewUrl(null); setSelectedFile(null); }}>İptal</Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
}

// Generic hook for media selection
export function useGenericMediaSelector<T = any>(initialValue?: GenericMediaFile<T> | null) {
  const [selectedMedia, setSelectedMedia] = useState<GenericMediaFile<T> | null>(initialValue || null);
  const [selectedItems, setSelectedItems] = useState<GenericMediaFile<T>[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const handleSelect = useCallback((media: GenericMediaFile<T>) => {
    setSelectedMedia(media);
    setError(null);
  }, []);

  const handleMultiSelect = useCallback((media: GenericMediaFile<T>[]) => {
    setSelectedItems(media);
    setError(null);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedMedia(null);
    setSelectedItems([]);
    setError(null);
  }, []);

  const addToSelection = useCallback((media: GenericMediaFile<T>) => {
    setSelectedItems(prev => {
      const exists = prev.find(item => item.id === media.id);
      if (exists) return prev;
      return [...prev, media];
    });
  }, []);

  const removeFromSelection = useCallback((mediaId: number | string) => {
    setSelectedItems(prev => prev.filter(item => item.id !== mediaId));
  }, []);

  return {
    selectedMedia,
    selectedItems,
    loading,
    error,
    handleSelect,
    handleMultiSelect,
    clearSelection,
    addToSelection,
    removeFromSelection,
    setSelectedMedia,
    setSelectedItems,
    setLoading,
    setError
  };
}

// Configuration builder helper
export function createMediaSelectorConfig<T = any>(
  categories: MediaCategory<T>[],
  options?: Partial<MediaSelectorConfig<T>>
): MediaSelectorConfig<T> {
  return {
    categories,
    defaultCategory: categories[0]?.key,
    acceptedTypes: ['image/*'],
    allowMultiSelect: false,
    allowCategorySwitch: true,
    showPreview: true,
    showMetadata: false,
    translations: {
      selectButton: "Select Media",
      selectTitle: "Media Selection",
      selectDescription: "Choose media from gallery or upload new",
      noCategory: "General"
    },
    ...options
  };
}

// Export types with different names to avoid conflicts
export type { GenericMediaFile as MediaFile }; 