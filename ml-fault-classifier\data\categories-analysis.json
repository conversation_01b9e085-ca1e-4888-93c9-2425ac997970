{"categories": [{"id": "1", "ad": "Su Tesisatı", "renk": "#2196f3", "ikon": "droplets", "keywords": ["musluk", "<PERSON><PERSON><PERSON>", "su", "tıkanıklık", "kaçak", "banyo", "mutfak", "lavabo", "klo<PERSON>t", "<PERSON><PERSON>", "sıcak su", "<PERSON><PERSON><PERSON>", "basınç", "sızıntı", "boru", "vanaya", "su sayacı", "su kesintisi", "su basması", "d<PERSON><PERSON>"], "priority": "high", "examples": ["Banyo musluğu <PERSON>", "Mutfak lavabosu tıkandı", "Su kaçağı var", "Klozet su kaçırıyor", "Sıcak su gelmiyor", "Lavabo tıkanıklığı"]}, {"id": "2", "ad": "Elektrik", "renk": "#ff9800", "ikon": "zap", "keywords": ["elektrik", "kesinti", "priz", "si<PERSON><PERSON>", "ampul", "ışık", "çalışmıyor", "kısa de<PERSON>", "elektrik kaçağı", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "fiş", "kablo", "elektrik sayacı", "elektrik kesintisi", "elektrik çarpması", "elektrik düğmesi", "elektrik prizi", "elektrik ka<PERSON>losu"], "priority": "high", "examples": ["Elektrik kesintisi yaşanıyor", "Priz çalışmıyor", "Ampul patladı", "Sigorta atıyor", "Elektrik düğmesi bozuk", "<PERSON><PERSON><PERSON> var"]}, {"id": "3", "ad": "Boyama", "renk": "#9c27b0", "ikon": "paintbrush", "keywords": ["boya", "badana", "sıva", "<PERSON><PERSON>", "tavan", "çatlak", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renk", "fırça", "boya kabı", "boya lekesi", "boya kokusu", "boya kuru<PERSON>ı", "<PERSON><PERSON> boyası", "tavan boyası", "badana ya<PERSON>a", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>van <PERSON>", "boya kabar<PERSON>ı"], "priority": "medium", "examples": ["<PERSON>var boyası dökülüyor", "<PERSON><PERSON> bad<PERSON>ı gerekiyor", "<PERSON><PERSON>ğı var", "<PERSON><PERSON> ka<PERSON>", "<PERSON><PERSON><PERSON>", "Renk değişikliği"]}, {"id": "4", "ad": "Yap<PERSON>", "renk": "#795548", "ikon": "home", "keywords": ["yapı", "inşaat", "çatlak", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasar", "temel", "<PERSON><PERSON>", "tavan", "zemin", "merdiven", "kapı", "pencere", "çatı", "balkon", "teras", "garaj", "bodrum", "çatı kaçağı", "temel çatlağı", "<PERSON><PERSON><PERSON><PERSON> hasar"], "priority": "high", "examples": ["<PERSON><PERSON>ğı var", "<PERSON><PERSON>", "Çatı kaçağı", "Temel çatlağı", "Balkon hasarı", "Ya<PERSON><PERSON><PERSON> problem"]}, {"id": "5", "ad": "Güvenlik", "renk": "#f44336", "ikon": "shield", "keywords": ["güvenlik", "kilit", "kamera", "alarm", "kapı", "pencere", "güvenlik sistemi", "hı<PERSON><PERSON><PERSON>ı", "yangın <PERSON>ı", "gaz alarmı", "güvenlik kamerası", "kapı kilidi", "pencere kilidi", "güvenlik kartı", "<PERSON><PERSON><PERSON>", "parmak izi", "güvenlik görevlisi", "güvenlik kapısı", "güvenlik bariyeri"], "priority": "high", "examples": ["<PERSON><PERSON><PERSON> kilidi bozuk", "Güvenlik kamerası çalışmıyor", "Alarm sistemi arızalı", "Güvenlik kartı çalışmıyor", "<PERSON><PERSON><PERSON><PERSON>z alarmı", "<PERSON><PERSON><PERSON>ı"]}, {"id": "6", "ad": "Isıtma/Soğutma", "renk": "#00bcd4", "ikon": "thermometer", "keywords": ["ısıtma", "<PERSON><PERSON><PERSON><PERSON>", "klima", "kalorifer", "petek", "ısı", "sıcaklık", "<PERSON><PERSON><PERSON>", "sıcak", "fan", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nem", "rut<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "kalorifer <PERSON>", "klima arızası", "ısıtma siste<PERSON>", "so<PERSON><PERSON><PERSON> siste<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nem alma cihazı"], "priority": "medium", "examples": ["Kalor<PERSON> pet<PERSON>ı<PERSON>", "<PERSON><PERSON>a arızalı", "<PERSON><PERSON><PERSON><PERSON> bozuk", "<PERSON><PERSON> problemi var", "Haval<PERSON><PERSON>rma <PERSON>ıyo<PERSON>", "Sıcaklık ayarı"]}, {"id": "7", "ad": "<PERSON><PERSON><PERSON><PERSON>", "renk": "#607d8b", "ikon": "arrow-up-down", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "kabin", "kat", "<PERSON><PERSON><PERSON><PERSON>", "kapı", "<PERSON><PERSON><PERSON><PERSON>", "asansör ta<PERSON>ı<PERSON>", "<PERSON><PERSON><PERSON>r <PERSON>şmıyor", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> freni", "asans<PERSON>r <PERSON>", "<PERSON><PERSON><PERSON><PERSON> hı<PERSON>ı", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> b<PERSON>"], "priority": "high", "examples": ["Asansör <PERSON>ışmıyor", "Asansör ta<PERSON>ıldı", "Asansör kapısı açılmıyor", "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Asansör <PERSON>", "Asansör bakı<PERSON>ı"]}, {"id": "8", "ad": "<PERSON><PERSON><PERSON>", "renk": "#4caf50", "ikon": "tree", "keywords": ["çevre", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bahçe", "<PERSON><PERSON><PERSON><PERSON>", "ağaç", "çiçek", "<PERSON><PERSON>", "yol", "park", "otopark", "çö<PERSON>", "temizlik", "<PERSON><PERSON><PERSON>", "spor alanı", "oyun alanı", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yolu", "bisiklet yolu", "<PERSON><PERSON><PERSON>", "bahçe bakımı", "p<PERSON><PERSON><PERSON>"], "priority": "low", "examples": ["Bahçe bakımı gerekiyor", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> buda<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> bakı<PERSON>ı"]}], "metadata": {"total_categories": 8, "high_priority": 4, "medium_priority": 2, "low_priority": 2, "created_at": "2025-01-15T21:00:00.000Z", "version": "1.0.0"}}