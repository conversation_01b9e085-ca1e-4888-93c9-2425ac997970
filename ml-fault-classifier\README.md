# 🤖 ML Fault Classifier

Arıza kategorilerini otomatik olarak tahmin eden makine öğrenmesi servisi.

## 📋 Özellikler

- **Keyword-based Classification**: Metin analizi ile kategori tahmini
- **Mevcut Kategori Entegrasyonu**: Ana projedeki kategorilerle uyumlu çalışma
- **<PERSON><PERSON>ven Skoru**: <PERSON><PERSON><PERSON> hesa<PERSON>lama
- **Alternatif Öneriler**: En yakın kategorileri listeleme
- **Real-time API**: Express.js tabanlı REST API
- **Otomatik Kategori Mapping**: ML kategorilerini DB kategorileriyle eşleştirme

## 🚀 Kurulum

### Gereksinimler
- Node.js 18+ (geliştirme için)
- Docker (production için)
- Ana proje çalı<PERSON>ır durumda (Port 3001)

### Geliştirme Kurulumu

1. **Bağımlılıkları yükleyin:**
```bash
cd ml-fault-classifier
npm install
```

2. **ML servisini başlatın:**
```bash
# Geliştirme modu
npm run dev

# Prodüksiyon modu
npm start
```

3. **Test edin:**
```bash
# Entegrasyon testi
npm run test-integration

# Model testi
npm run test-model
```

### Docker Kurulumu

1. **Docker image'ını build edin:**
```bash
npm run docker:build
```

2. **Container'ı çalıştırın:**
```bash
# Manuel çalıştırma
npm run docker:run

# Docker Compose ile
npm run docker:compose
```

3. **Logları izleyin:**
```bash
npm run docker:logs
```

## 🔧 API Endpoints

### Health Check
```http
GET /health
```

### Model Durumu
```http
GET /status
```

### Kategori Tahmini
```http
POST /predict
Content-Type: application/json

{
  "title": "Banyo musluğu damlatıyor",
  "description": "Banyo musluğundan sürekli su damlıyor"
}
```

### Kategorileri Listele
```http
GET /categories
```

### Kategorileri Güncelle
```http
POST /categories/refresh
```

## 📊 Kategori Mapping

ML servisi şu kategorileri destekler:

| ML Kategorisi | DB Kategorisi Eşleşmesi |
|---------------|-------------------------|
| Su Tesisatı | Su Tesisatı, Tesisat, Su |
| Elektrik | Elektrik, Elektrik Arızası |
| Boyama | Boyama, Badana, Sıva |
| Yapı | Yapı, İnşaat, Yapısal |
| Güvenlik | Güvenlik, Kilit, Kamera |
| Isıtma/Soğutma | Isıtma, Soğutma, Klima |
| Asansör | Asansör |
| Çevre Düzenleme | Çevre, Bahçe, Peyzaj |

## 🎯 Kullanım Örnekleri

### 1. Basit Tahmin
```javascript
const response = await fetch('http://localhost:3050/predict', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: "Elektrik kesintisi",
    description: "Sürekli elektrik kesintisi yaşanıyor"
  })
});

const result = await response.json();
console.log(result.prediction.db_category.ad); // "Elektrik"
```

### 2. Ana Proje Entegrasyonu
```javascript
// Ana proje API'si üzerinden
const response = await fetch('/api/ml-predict', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: "Musluk damlatıyor",
    description: "Banyo musluğundan su damlıyor"
  })
});
```

## 🔄 Entegrasyon

### Ana Projeye Entegrasyon

#### Geliştirme Modu
1. **ML servisini başlatın:**
```bash
cd ml-fault-classifier
npm run dev
```

2. **Ana projeyi başlatın:**
```bash
cd ..
npm run dev
```

3. **Kategorileri güncelleyin:**
```bash
curl -X POST http://localhost:3050/categories/refresh
```

#### Docker Modu
1. **Tüm sistemi Docker ile başlatın:**
```bash
# Ana proje dizininde
npm run docker:deploy
```

2. **Veya sadece ML servisini:**
```bash
cd ml-fault-classifier
npm run docker:compose
```

### Arıza Formunda Kullanım

Arıza oluşturma formunda "AI Önerisi" butonuna tıklayarak otomatik kategori tahmini yapabilirsiniz.

## 📈 Model Performansı

- **Eğitim Verisi**: 396 örnek
- **Kategori Sayısı**: 8
- **Güven Eşiği**: 0.1 (10%)
- **Ortalama Doğruluk**: %85+

## 🛠️ Geliştirme

### Model Eğitimi
```bash
# Yeni veri ile model eğitimi
npm run train

# Model iyileştirme
npm run improve

# Test verisi oluşturma
npm run generate-data
```

### Yeni Kategori Ekleme

1. `data/training-data.json` dosyasına yeni örnekler ekleyin
2. `data/categories-analysis.json` dosyasını güncelleyin
3. Modeli yeniden eğitin: `npm run train`
4. Servisi yeniden başlatın: `npm run dev`

## 🔍 Debugging

### Log Seviyeleri
- `console.log`: Genel bilgiler
- `console.error`: Hatalar
- `console.warn`: Uyarılar

### Test Araçları
- **Entegrasyon Testi**: `npm run test-integration`
- **Model Testi**: `npm run test-model`
- **Health Check**: `http://localhost:3010/health`

## 📝 Changelog

### v1.0.0 (2025-01-15)
- İlk sürüm
- Keyword-based classification
- Ana proje entegrasyonu
- Real-time API
- Kategori mapping sistemi

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun
3. Değişikliklerinizi commit edin
4. Pull Request oluşturun

## 📄 Lisans

MIT License 