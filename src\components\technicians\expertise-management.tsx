import { useEffect, useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"

interface ExpertiseArea {
  id: string;
  ad: string;
  aciklama?: string;
  renk: string;
}

export function ExpertiseManagement() {
  const [areas, setAreas] = useState<ExpertiseArea[]>([])
  const [loading, setLoading] = useState(true)
  const [showDialog, setShowDialog] = useState(false)
  const [editing, setEditing] = useState<ExpertiseArea | null>(null)
  const [form, setForm] = useState({ ad: "", aciklama: "", renk: "#3B82F6" })
  const [showDelete, setShowDelete] = useState<null | string>(null)

  const fetchAreas = () => {
    setLoading(true)
    fetch("/api/expertise-areas")
      .then(res => res.json())
      .then(data => setAreas(data))
      .finally(() => setLoading(false))
  }
  useEffect(() => { fetchAreas() }, [])

  const openAdd = () => {
    setEditing(null)
    setForm({ ad: "", aciklama: "", renk: "#3B82F6" })
    setShowDialog(true)
  }
  const openEdit = (area: ExpertiseArea) => {
    setEditing(area)
    setForm({ ad: area.ad, aciklama: area.aciklama || "", renk: area.renk })
    setShowDialog(true)
  }
  const handleSave = async () => {
    const method = editing ? "PUT" : "POST"
    const url = "/api/expertise-areas"
    const body = editing ? { ...form, id: editing.id } : form
    await fetch(url, {
      method,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
    })
    setShowDialog(false)
    fetchAreas()
  }
  const handleDelete = async () => {
    await fetch("/api/expertise-areas", {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id: showDelete }),
    })
    setShowDelete(null)
    fetchAreas()
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl">
            <span className="text-white text-xl">🎯</span>
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Uzmanlık Alanları Yönetimi
            </h1>
            <p className="text-gray-600 text-sm">Teknisyenlerin uzmanlık alanlarını tanımlayın ve yönetin</p>
          </div>
        </div>
        <Button 
          onClick={openAdd}
          className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2"
        >
          <span className="text-lg">🎯</span>
          Yeni Uzmanlık Alanı
        </Button>
      </div>



      {/* Expertise Areas Table Card */}
      <Card className="border-0 shadow-xl bg-white rounded-2xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-red-50 border-b border-gray-100 pb-6">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg">
              <span className="text-white text-lg">📋</span>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900">Detaylı Uzmanlık Listesi</CardTitle>
              <p className="text-sm text-gray-600 mt-1">Tüm uzmanlık alanlarının detayları ve renk kodları</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 border-b border-gray-200">
                <th className="text-left py-4 px-6 font-semibold text-gray-700">🎯 Uzmanlık Alanı</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-700">📝 Açıklama</th>
                <th className="text-left py-4 px-6 font-semibold text-gray-700">🎨 Renk Kodu</th>
                <th className="text-center py-4 px-6 font-semibold text-gray-700">⚙️ İşlemler</th>
              </tr>
            </thead>
            <tbody>
              {areas.length === 0 ? (
                <tr>
                  <td colSpan={4} className="py-12 text-center">
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-2xl text-gray-400">🎯</span>
                      </div>
                      <div>
                        <p className="text-gray-600 font-medium">Uzmanlık alanı bulunamadı</p>
                        <p className="text-gray-400 text-sm">Yeni uzmanlık alanı ekleyerek başlayın</p>
                      </div>
                      <Button 
                        onClick={openAdd}
                        className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white"
                      >
                        <span className="mr-2">🎯</span>
                        İlk Uzmanlık Alanını Ekle
                      </Button>
                    </div>
                  </td>
                </tr>
              ) : (
                areas.map(area => (
                  <tr key={area.id} className="hover:bg-orange-50/50 transition-all duration-200 border-b border-gray-100">
                    <td className="py-4 px-6">
                      <div className="flex items-center gap-3">
                        <div 
                          style={{ background: area.renk }} 
                          className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                        ></div>
                        <span className="font-semibold text-gray-900">{area.ad}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className="text-gray-600">{area.aciklama || 'Açıklama eklenmemiş'}</span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center gap-3">
                        <div 
                          style={{ background: area.renk }} 
                          className="w-8 h-8 rounded-lg border-2 border-white shadow-sm"
                        ></div>
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs font-mono text-gray-700">
                          {area.renk}
                        </code>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center gap-2">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => openEdit(area)}
                          className="h-9 px-4 text-sm border-orange-200 text-orange-700 hover:bg-orange-50 hover:border-orange-300 transition-all duration-200 rounded-lg shadow-sm"
                        >
                          <span className="mr-1">✏️</span>
                          Düzenle
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => setShowDelete(area.id)}
                          className="h-9 px-4 text-sm border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300 transition-all duration-200 rounded-lg shadow-sm"
                        >
                          <span className="mr-1">🗑️</span>
                          Sil
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </CardContent>
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editing ? "Uzmanlık Alanı Düzenle" : "Yeni Uzmanlık Alanı"}</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            <Input placeholder="Ad" value={form.ad} onChange={e => setForm(f => ({ ...f, ad: e.target.value }))} />
            <Input placeholder="Açıklama" value={form.aciklama} onChange={e => setForm(f => ({ ...f, aciklama: e.target.value }))} />
            <div className="flex items-center space-x-2">
              <label className="text-sm">Renk:</label>
              <input type="color" value={form.renk} onChange={e => setForm(f => ({ ...f, renk: e.target.value }))} />
              <span className="ml-2 text-xs">{form.renk}</span>
            </div>
            <div className="flex justify-end space-x-2 pt-2">
              <Button variant="outline" onClick={() => setShowDialog(false)}>İptal</Button>
              <Button onClick={handleSave}>{editing ? "Güncelle" : "Kaydet"}</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <Dialog open={!!showDelete} onOpenChange={() => setShowDelete(null)}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle>Silme Onayı</DialogTitle>
          </DialogHeader>
          <div className="py-4">Bu uzmanlık alanını silmek istediğinize emin misiniz?</div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowDelete(null)}>İptal</Button>
            <Button variant="destructive" onClick={handleDelete}>Sil</Button>
          </div>
        </DialogContent>
      </Dialog>
      </Card>
    </div>
  )
} 