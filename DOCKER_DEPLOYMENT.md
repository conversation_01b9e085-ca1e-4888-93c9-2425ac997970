# 🐳 Docker Deployment Kılavuzu

Bu <PERSON>, Bakım Onarım Sistemi'ni Docker ile nasıl deploy edeceğinizi açıklar.

> **Not**: Bu kılavuz development modu için hazırlanmıştır. Ana proje development modunda çalışırken, sadece ML servisi ve PostgreSQL Docker'da çalışacaktır.

## 📋 Sistem Mimarisi

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web App       │    │   ML Service    │    │   PostgreSQL    │
│   (Port 3001)   │◄──►│   (Port 3050)   │    │   (Port 5433)   │
│   Next.js       │    │   Express.js    │    │   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Hızlı Başlangıç

### 1. ML Servisi ve PostgreSQL'i Başlatın
```bash
# Ana proje dizininde
npm run docker:deploy
```

Bu komut:
- PostgreSQL veritabanını başlatır
- ML servisini build eder ve başlatır
- Ana proje için environment dosyasını hazırlar

### 2. Ana Projeyi Başlatın
```bash
# Development modunda
npm run dev
```

### 2. <PERSON><PERSON> Kontrol Edin
```bash
# Tüm servislerin durumu
docker-compose ps

# ML servisi logları
npm run docker:ml-logs

# Tüm loglar
npm run docker:logs
```

## 🔧 Manuel Deployment

### Sadece ML Servisi
```bash
cd ml-fault-classifier

# Build ve çalıştır
npm run docker:build
npm run docker:run

# Veya Docker Compose ile
npm run docker:compose
```

### ML Servisi ve PostgreSQL
```bash
# Ana proje dizininde
docker-compose up -d
```

## 📊 Servis URL'leri

| Servis | URL | Açıklama |
|--------|-----|----------|
| Web App | http://localhost:3001 | Ana uygulama |
| ML Service | http://localhost:3050 | AI tahmin servisi |
| PostgreSQL | localhost:5433 | Veritabanı |

## 🔍 Health Check

### ML Servisi
```bash
curl http://localhost:3050/health
```

### Web App
```bash
curl http://localhost:3001/api/ml-predict
```

## 🛠️ Yönetim Komutları

### Servisleri Başlatma
```bash
# ML servisi ve PostgreSQL
npm run docker:up

# Sadece ML servisi
cd ml-fault-classifier && npm run docker:compose
```

### Servisleri Durdurma
```bash
# ML servisi ve PostgreSQL
npm run docker:down

# Sadece ML servisi
cd ml-fault-classifier && npm run docker:compose:down
```

### Logları İzleme
```bash
# ML servisi ve PostgreSQL logları
npm run docker:logs

# Sadece ML servisi logları
npm run docker:ml-logs
```

## 🔧 Konfigürasyon

### Environment Değişkenleri

#### Ana Proje (.env.local)
```env
# Database
DATABASE_URL="postgresql://admin:admin123@localhost:5433/bakimonarim"

# NextAuth
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET="your-secret-key-here"

# ML Service
ML_SERVICE_URL="http://localhost:3010"

# App
NEXT_PUBLIC_APP_URL="http://localhost:3001"
```

#### ML Servisi (ml-fault-classifier/env.example)
```env
NODE_ENV=production
PORT=3010
MAIN_APP_URL=http://localhost:3001
CONFIDENCE_THRESHOLD=0.1
CORS_ORIGIN=http://localhost:3001
```

## 🐛 Sorun Giderme

### ML Servisi Başlamıyor
```bash
# Logları kontrol et
docker logs bakimonarim_ml_service

# Container'ı yeniden başlat
docker restart bakimonarim_ml_service
```

### Veritabanı Bağlantı Hatası
```bash
# PostgreSQL durumunu kontrol et
docker ps | grep postgres

# Veritabanını yeniden başlat
docker restart bakimonarim_postgres
```

### Port Çakışması
```bash
# Kullanılan portları kontrol et
netstat -tulpn | grep :3050
netstat -tulpn | grep :3001
netstat -tulpn | grep :5433
```

## 📈 Production Deployment

> **Not**: Production deployment için ana proje de Docker container'ına alınacaktır.

### Environment Değişiklikleri
```bash
# Production için environment
export NODE_ENV=production
export ML_SERVICE_URL=https://ml.yourdomain.com
export MAIN_APP_URL=https://app.yourdomain.com
```

### SSL/HTTPS
```bash
# Reverse proxy ile SSL
# nginx veya traefik kullanın
```

### Monitoring
```bash
# Health check endpoint'leri
curl https://ml.yourdomain.com/health
curl https://app.yourdomain.com/api/ml-predict
```

## 🔄 Güncelleme

### ML Servisi Güncelleme
```bash
cd ml-fault-classifier

# Yeni model eğitimi
npm run train

# Docker image'ını yeniden build et
npm run docker:build

# Container'ı yeniden başlat
docker restart ml-fault-classifier
```

### ML Servisi Güncelleme
```bash
# Git pull
git pull origin main

# Docker Compose ile yeniden build
docker-compose down
docker-compose up -d --build
```

### Ana Proje Güncelleme
```bash
# Git pull
git pull origin main

# Development modunda yeniden başlat
npm run dev
```

## 📝 Notlar

- ML servisi ilk başlatıldığında model yüklemesi 10-30 saniye sürebilir
- Veritabanı ilk kez başlatıldığında migration'lar otomatik çalışır
- Health check'ler 30 saniyede bir kontrol edilir
- Container'lar `unless-stopped` restart policy ile çalışır

## 🆘 Destek

Sorun yaşarsanız:
1. Logları kontrol edin: `npm run docker:logs`
2. Container durumlarını kontrol edin: `docker ps`
3. Network bağlantılarını test edin: `docker network ls` 