"use client"

import { useState, useEffect } from "react"
import { Plus, Edit2, Trash2, <PERSON>ertCircle } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "sonner"
import { PriorityDialog } from "@/components/settings/priority-dialog"
import { DeletePriorityDialog } from "@/components/settings/delete-priority-dialog"

interface Priority {
  id: string
  ad: string
  seviye: number
  renk: string
  aciklama?: string
  olusturulma_tarihi: string
  guncelleme_tarihi: string
}

export default function PriorityLevelsPage() {
  const [priorities, setPriorities] = useState<Priority[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPriority, setSelectedPriority] = useState<Priority | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  useEffect(() => {
    fetchPriorities()
  }, [])

  const fetchPriorities = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/priorities")
      
      if (!response.ok) {
        throw new Error("Öncelik seviyeleri yüklenemedi")
      }

      const data = await response.json()
      setPriorities(data.priorities || [])
    } catch (error) {
      console.error("Error fetching priorities:", error)
      toast.error("Öncelik seviyeleri yüklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (priority: Priority) => {
    setSelectedPriority(priority)
    setIsDialogOpen(true)
  }

  const handleDelete = (priority: Priority) => {
    setSelectedPriority(priority)
    setIsDeleteDialogOpen(true)
  }

  const handleAddNew = () => {
    setSelectedPriority(null)
    setIsDialogOpen(true)
  }

  const handleDialogClose = () => {
    setIsDialogOpen(false)
    setSelectedPriority(null)
  }

  const handleDeleteDialogClose = () => {
    setIsDeleteDialogOpen(false)
    setSelectedPriority(null)
  }

  const handleSuccess = () => {
    fetchPriorities()
    handleDialogClose()
    handleDeleteDialogClose()
  }

  const getPriorityColor = (color: string) => {
    return {
      backgroundColor: `${color}20`,
      borderColor: color,
      color: color
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64 mt-2" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            Öncelik Seviyeleri
          </h2>
          <p className="text-muted-foreground">
            Arıza öncelik seviyelerini yönetin. Seviye numarası arızaların önem sırasını belirler.
          </p>
        </div>
        <Button onClick={handleAddNew}>
          <Plus className="mr-2 h-4 w-4" />
          Yeni Seviye
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Öncelik Seviyeleri ({priorities.length})</CardTitle>
          <CardDescription>
            Sistemde tanımlı tüm öncelik seviyeleri. Arızalar bu seviyeler ile önceliklendirilir.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {priorities.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Öncelik seviyesi bulunamadı</h3>
              <p className="mt-2 text-muted-foreground">
                Henüz hiç öncelik seviyesi tanımlanmamış. İlk seviyeyi oluşturun.
              </p>
              <Button className="mt-4" onClick={handleAddNew}>
                <Plus className="mr-2 h-4 w-4" />
                İlk Seviyeyi Ekle
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-20">Seviye</TableHead>
                    <TableHead>Ad</TableHead>
                    <TableHead>Renk</TableHead>
                    <TableHead>Açıklama</TableHead>
                    <TableHead>Son Güncelleme</TableHead>
                    <TableHead className="text-right">İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {priorities.map((priority) => (
                    <TableRow key={priority.id}>
                      <TableCell className="font-mono font-bold">
                        {priority.seviye}
                      </TableCell>
                      <TableCell className="font-medium">
                        {priority.ad}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          style={getPriorityColor(priority.renk)}
                          className="border-2"
                        >
                          {priority.renk}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {priority.aciklama || "-"}
                      </TableCell>
                      <TableCell className="text-muted-foreground text-sm">
                        {new Date(priority.guncelleme_tarihi).toLocaleDateString('tr-TR')}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(priority)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(priority)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <PriorityDialog
        priority={selectedPriority}
        isOpen={isDialogOpen}
        onClose={handleDialogClose}
        onSuccess={handleSuccess}
      />

      <DeletePriorityDialog
        priority={selectedPriority}
        isOpen={isDeleteDialogOpen}
        onClose={handleDeleteDialogClose}
        onSuccess={handleSuccess}
      />
    </div>
  )
} 