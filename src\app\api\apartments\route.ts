import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

// Validation schema for apartment creation and updates
const apartmentSchema = z.object({
  numara: z.string().min(1, "Daire numarası gereklidir").max(20, "Daire numarası çok uzun"),
  blok_id: z.string().min(1, "Blok ID gereklidir"),
  kat: z.union([z.number().int(), z.string()]).optional().transform((val) => {
    if (val === null || val === undefined || val === "") return null;
    return typeof val === "string" ? parseInt(val) : val;
  }),
  aciklama: z.string().optional().nullable(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const mode = searchParams.get("mode")
    const blokId = searchParams.get("blok_id")
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search")
    const skip = (page - 1) * limit

    // Build where clause
    const where = {
      silindi_mi: false,
      ...(blokId && blokId !== "all" && { blok_id: blokId }),
      ...(search && {
        OR: [
          { numara: { contains: search, mode: "insensitive" as const } },
          { aciklama: { contains: search, mode: "insensitive" as const } }
        ]
      })
    }

    // Get total count
    const total = await prisma.daire.count({ where })

    // Get apartments
    const apartments = await prisma.daire.findMany({
      where,
      select: {
        id: true,
        numara: true,
        slug: true,
        kat: true,
        olusturulma_tarihi: true,
        blok: {
          select: {
            id: true,
            ad: true,
            slug: true,
            proje: {
              select: {
                id: true,
                ad: true,
                slug: true
              }
            }
          }
        },
        _count: {
          select: {
            arizalar: {
              where: { silindi_mi: false }
            }
          }
        }
      },
      orderBy: {
        numara: "asc"
      },
      skip: mode === "management" ? skip : undefined,
      take: mode === "management" ? limit : undefined
    })

    // For dropdown mode, return simple format
    if (mode !== "management") {
      return NextResponse.json({ apartments })
    }

    // For management mode, return with pagination
    return NextResponse.json({
      apartments,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalCount: total,
        limit
      }
    })
  } catch (error) {
    console.error("Daireler listelenirken hata:", error)
    return NextResponse.json(
      { error: "Daireler listelenemedi" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validation = apartmentSchema.safeParse(body)
    if (!validation.success) {
      console.log("Validation Error:", validation.error.format())
      return NextResponse.json(
        { 
          message: "Geçersiz veri",
          errors: validation.error.format(),
        },
        { status: 400 }
      )
    }    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { numara, blok_id, kat, aciklama } = validation.data

    // Check if block exists
    const existingBlock = await prisma.blok.findFirst({
      where: { id: blok_id, silindi_mi: false },
    })

    if (!existingBlock) {
      return NextResponse.json(
        { message: "Blok bulunamadı" },
        { status: 404 }
      )
    }

    // Check for duplicate apartment number in the same block
    const existingApartment = await prisma.daire.findFirst({
      where: {
        numara,
        blok_id,
        silindi_mi: false,
      },
    })

    if (existingApartment) {
      return NextResponse.json(
        { message: "Bu blokta aynı numarada bir daire zaten mevcut" },
        { status: 409 }
      )
    }    // Create the apartment
    const apartmentData = {      numara,
      blok_id,
      kat: kat || 1,
      olusturulma_tarihi: new Date(),
      guncelleme_tarihi: new Date(),
    }

    const newApartment = await prisma.daire.create({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      data: apartmentData as any,
      include: {
        blok: {
          select: {
            id: true,
            ad: true,
            proje: {
              select: {
                id: true,
                ad: true,
              },
            },
          },
        },
      },
    })

    return NextResponse.json(newApartment, { status: 201 })
  } catch (error) {
    console.error("Error creating apartment:", error)
    return NextResponse.json(
      { message: "Daire oluşturulurken hata oluştu" },
      { status: 500 }
    )
  }
}
