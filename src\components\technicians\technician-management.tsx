import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { TechnicianDialog } from "./technician-dialog"

import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from "@/components/ui/alert-dialog"

interface Technician {
  id: string;
  ad: string;
  soyad: string;
  telefon: string;
  aktifRandevuSayisi: number;
  uzmanlikAlanlari: {
    id: string;
    ad: string;
    renk: string;
    seviye: string;
  }[];
  durum: string;
}

export function TechnicianManagement() {
  const [technicians, setTechnicians] = useState<Technician[]>([])
  const [loading, setLoading] = useState(true)
  const [showDialog, setShowDialog] = useState(false)
  const [editingTech, setEditingTech] = useState<Technician | null>(null)
  const [deleteId, setDeleteId] = useState<string | null>(null)
  const [search, setSearch] = useState("")
  const [filterActive, setFilterActive] = useState<"all" | "active" | "inactive">("all")
  const [filterExpertise, setFilterExpertise] = useState<string>("all")
  const [expertiseAreas, setExpertiseAreas] = useState<{id: string, ad: string, renk: string}[]>([])

  const fetchTechnicians = () => {
    setLoading(true)
    fetch("/api/technicians")
      .then(res => res.json())
      .then(data => setTechnicians(data))
      .finally(() => setLoading(false))
  }

  useEffect(() => {
    fetchTechnicians()
    // Expertise alanlarını da getir
    fetch("/api/expertise-areas")
      .then(res => res.json())
      .then(data => setExpertiseAreas(data))
  }, [])

  const handleAdd = () => {
    setEditingTech(null)
    setShowDialog(true)
  }
  const handleEdit = (tech: Technician) => {
    setEditingTech(tech)
    setShowDialog(true)
  }
  const handleSave = async (data: any) => {
    const method = editingTech ? "PUT" : "POST"
    const url = "/api/technicians"
    const body = editingTech ? { ...data, id: editingTech.id } : data
    await fetch(url, {
      method,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
    })
    setShowDialog(false)
    fetchTechnicians()
  }

  const handleDelete = async () => {
    if (!deleteId) return
    await fetch("/api/technicians", {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id: deleteId }),
    })
    setDeleteId(null)
    fetchTechnicians()
  }

  // Filtreleme ve arama
  const filtered = technicians.filter(t => {
    if (filterActive === "active" && t.durum !== "ACTIVE") return false
    if (filterActive === "inactive" && t.durum !== "INACTIVE") return false
    if (search && !(t.ad + " " + t.soyad).toLowerCase().includes(search.toLowerCase())) return false
    if (filterExpertise !== "all" && !t.uzmanlikAlanlari.some(ua => ua.id === filterExpertise)) return false
    return true
  })

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
            <span className="text-white text-xl">👨‍🔧</span>
          </div>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Teknisyenler Yönetimi
            </h1>
            <p className="text-gray-600 text-sm">Teknisyenlerin bilgilerini ve yeteneklerini yönetin</p>
          </div>
        </div>
        <Button 
          onClick={handleAdd}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-2"
        >
          <span className="text-lg">➕</span>
          Yeni Teknisyen Ekle
        </Button>
      </div>
      {/* Filters Section */}
      <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4">
          <div className="flex-1 max-w-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">🔍 Teknisyen Ara</label>
            <Input 
              placeholder="Ad, soyad veya telefon ile ara..." 
              value={search} 
              onChange={(e) => setSearch(e.target.value)} 
              className="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex flex-wrap gap-3">
            <div className="min-w-[180px]">
              <label className="block text-sm font-medium text-gray-700 mb-2">📊 Durum Filtresi</label>
              <select 
                className="w-full border border-gray-300 px-3 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white" 
                value={filterActive} 
                onChange={(e) => setFilterActive(e.target.value as any)}
              >
                <option value="all">📋 Tüm Teknisyenler</option>
                <option value="active">✅ Aktif Teknisyenler</option>
                <option value="inactive">❌ Pasif Teknisyenler</option>
              </select>
            </div>
            
            <div className="min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-2">🔧 Uzmanlık Filtresi</label>
              <select 
                className="w-full border border-gray-300 px-3 py-2 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white" 
                value={filterExpertise} 
                onChange={(e) => setFilterExpertise(e.target.value)}
              >
                <option value="all">🎯 Tüm Uzmanlıklar</option>
                {expertiseAreas.map(area => (
                  <option key={area.id} value={area.id}>{area.ad}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="lg:ml-auto">
            <div className="bg-white rounded-lg px-4 py-3 border border-gray-200 shadow-sm">
              <p className="text-sm text-gray-600 font-medium">📊 Sonuç</p>
              <p className="text-xl font-bold text-blue-600">
                {filtered.length} / {technicians.length}
              </p>
              <p className="text-xs text-gray-500">teknisyen bulundu</p>
            </div>
          </div>
        </div>
      </div>
      {/* Technicians Table Card */}
      <Card className="border-0 shadow-xl bg-white rounded-2xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100 pb-6">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
              <span className="text-white text-lg">📋</span>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900">Teknisyen Listesi</CardTitle>
              <p className="text-sm text-gray-600 mt-1">Tüm teknisyenlerin detayları ve uzmanlık bilgileri</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold">👤 Ad Soyad</TableHead>
                  <TableHead className="font-semibold">📞 Telefon</TableHead>
                  <TableHead className="font-semibold">🔧 Uzmanlıklar & Seviye</TableHead>
                  <TableHead className="font-semibold">📅 Aktif Randevu</TableHead>
                  <TableHead className="font-semibold">📊 Durum</TableHead>
                  <TableHead className="font-semibold">⚙️ İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="py-12 text-center">
                      <div className="flex flex-col items-center gap-4">
                        <div className="animate-spin w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full"></div>
                        <p className="text-gray-500 font-medium">Teknisyenler yükleniyor...</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filtered.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="py-12 text-center">
                      <div className="flex flex-col items-center gap-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                          <span className="text-2xl text-gray-400">👨‍🔧</span>
                        </div>
                        <div>
                          <p className="text-gray-600 font-medium">Teknisyen bulunamadı</p>
                          <p className="text-gray-400 text-sm">Filtreleri kontrol edin veya yeni teknisyen ekleyin</p>
                        </div>
                        <Button 
                          onClick={handleAdd}
                          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                        >
                          <span className="mr-2">➕</span>
                          İlk Teknisyeni Ekle
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filtered.map(tech => (
                    <TableRow key={tech.id} className="hover:bg-blue-50/50 transition-all duration-200 border-b border-gray-100">
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg text-white font-bold">
                            {tech.ad.charAt(0)}{tech.soyad.charAt(0)}
                          </div>
                          <div>
                            <p className="font-semibold text-gray-900">{tech.ad} {tech.soyad}</p>
                            <p className="text-sm text-gray-500">Teknisyen</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center gap-2">
                          <span className="text-green-600">📞</span>
                          <span className="font-mono text-gray-700">{tech.telefon}</span>
                        </div>
                      </TableCell>
                      <TableCell className="py-4 px-6">
                        <div className="flex flex-wrap gap-2 max-w-xs">
                          {tech.uzmanlikAlanlari.length === 0 ? (
                            <span className="text-gray-400 text-sm italic">Uzmanlık alanı yok</span>
                          ) : (
                            tech.uzmanlikAlanlari.map(u => (
                              <div key={u.id} className="flex items-center shadow-sm rounded-lg overflow-hidden">
                                <span 
                                  style={{ background: u.renk }} 
                                  className="px-3 py-1.5 text-xs text-white font-medium"
                                >
                                  {u.ad}
                                </span>
                                <span 
                                  className={`px-2 py-1.5 text-xs font-bold ${
                                    u.seviye === 'UZMAN' ? 'bg-green-500 text-white' :
                                    u.seviye === 'ORTA' ? 'bg-yellow-500 text-white' :
                                    'bg-gray-500 text-white'
                                  }`}
                                >
                                  {u.seviye === 'UZMAN' ? '⭐' : u.seviye === 'ORTA' ? '○' : '△'}
                                </span>
                              </div>
                            ))
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center justify-center">
                          <div className={`flex items-center gap-2 px-3 py-2 rounded-xl text-sm font-medium shadow-sm ${
                            tech.aktifRandevuSayisi > 5 ? 'bg-red-100 text-red-800 border border-red-200' :
                            tech.aktifRandevuSayisi > 2 ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
                            tech.aktifRandevuSayisi > 0 ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                            'bg-gray-100 text-gray-600 border border-gray-200'
                          }`}>
                            <span>📅</span>
                            <span className="font-bold">{tech.aktifRandevuSayisi}</span>
                            <span className="text-xs">randevu</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center justify-center">
                          <span className={`inline-flex items-center gap-2 px-4 py-2 rounded-xl text-sm font-medium shadow-sm ${
                            tech.durum === "ACTIVE" ? 
                            "bg-green-100 text-green-800 border border-green-200" : 
                            "bg-gray-100 text-gray-600 border border-gray-200"
                          }`}>
                            {tech.durum === "ACTIVE" ? (
                              <>
                                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                <span>Aktif</span>
                              </>
                            ) : (
                              <>
                                <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                                <span>Pasif</span>
                              </>
                            )}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => handleEdit(tech)}
                            className="h-9 px-4 text-sm border-blue-200 text-blue-700 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 rounded-lg shadow-sm"
                          >
                            <span className="mr-1">✏️</span>
                            Düzenle
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => setDeleteId(tech.id)}
                            className="h-9 px-4 text-sm border-red-200 text-red-700 hover:bg-red-50 hover:border-red-300 transition-all duration-200 rounded-lg shadow-sm"
                          >
                            <span className="mr-1">🗑️</span>
                            Sil
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      <TechnicianDialog
        open={showDialog}
        onClose={() => setShowDialog(false)}
        onSave={handleSave}
        initialData={editingTech ? {
          ad: editingTech.ad,
          soyad: editingTech.soyad,
          telefon: editingTech.telefon,
          aktif: editingTech.durum === "ACTIVE",
          uzmanliklar: editingTech.uzmanlikAlanlari.map(u => ({
            id: u.id,
            seviye: u.seviye
          }))
        } : undefined}
      />
      <AlertDialog open={!!deleteId} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Silme Onayı</AlertDialogTitle>
          </AlertDialogHeader>
          <div className="py-4">Bu teknisyeni silmek istediğinize emin misiniz?</div>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Sil</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 