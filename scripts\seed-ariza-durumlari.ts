import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const durumlar = [
    { ad: 'A<PERSON>ı<PERSON>', renk: '#f87171', aciklama: '<PERSON><PERSON><PERSON><PERSON> kaydı açık', sira: 1 },
    { ad: 'Beklemede', renk: '#fbbf24', aciklama: '<PERSON><PERSON><PERSON><PERSON> beklemede', sira: 2 },
    { ad: 'Tamamlandı', renk: '#34d399', aciklama: 'Ar<PERSON><PERSON> tamamlandı', sira: 3 },
    { ad: 'İptal', renk: '#a3a3a3', aciklama: 'Arıza iptal edildi', sira: 4 },
  ];

  for (const durum of durumlar) {
    await prisma.arizaDurum.upsert({
      where: { sira: durum.sira },
      update: { ad: durum.ad, renk: durum.renk, aciklama: durum.aciklama },
      create: durum,
    });
    console.log(`Durum eklendi/güncellendi: ${durum.ad}`);
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 