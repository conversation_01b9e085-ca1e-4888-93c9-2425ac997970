"use client"

import { useQuery } from "@tanstack/react-query"
import { Card, CardContent } from "@/components/ui/card"

const fetcher = async () => {
  const res = await fetch("/api/projects?page=1&limit=5")
  return res.json()
}

function DashboardProjects() {
  const { data, isLoading } = useQuery({
    queryKey: ["projects", 1, 5],
    queryFn: fetcher
  })

  if (isLoading || !data || !data.projects) {
    return <Card><CardContent>Yükleniyor...</CardContent></Card>
  }

  return (
    <Card>
      <CardContent>
        <h3 className="font-semibold mb-2">Projeler</h3>
        <ul className="space-y-1">
          {data.projects.map((project: { id: string; ad: string; _count?: { bloklar?: number; arizalar?: number } }) => (
            <li key={project.id} className="flex justify-between">
              <span>{project.ad}</span>
              <span className="text-xs text-muted-foreground">{project._count?.bloklar ?? 0} blok / {project._count?.arizalar ?? 0} arıza</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}

export default DashboardProjects
