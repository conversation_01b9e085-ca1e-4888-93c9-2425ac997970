import { z } from "zod"

export const loginSchema = z.object({
  email: z
    .string()
    .min(1, "Email adresi gereklidir")
    .email("Geçerli bir email adresi giriniz"),
  password: z
    .string()
    .min(1, "Şifre gereklidir")
    .min(6, "Şifre en az 6 karakter olmalıdır"),
  remember: z.boolean().default(false).optional(),
})

export const registerSchema = z.object({
  firstName: z
    .string()
    .min(1, "Ad gereklidir")
    .min(2, "Ad en az 2 karakter olmalıdır")
    .max(50, "Ad en fazla 50 karakter olabilir"),
  lastName: z
    .string()
    .min(1, "Soyad gereklidir")
    .min(2, "Soyad en az 2 karakter olmalıdır")
    .max(50, "Soyad en fazla 50 karakter olabilir"),
  email: z
    .string()
    .min(1, "Email adresi gereklidir")
    .email("Geçerli bir email adresi giriniz"),
  phone: z
    .string()
    .min(1, "Telefon numarası gereklidir")
    .regex(/^[0-9]{10,11}$/, "Geçerli bir telefon numarası giriniz"),
  password: z
    .string()
    .min(1, "Şifre gereklidir")
    .min(8, "Şifre en az 8 karakter olmalıdır")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Şifre en az bir küçük harf, bir büyük harf ve bir rakam içermelidir"),
  confirmPassword: z
    .string()
    .min(1, "Şifre tekrarı gereklidir"),
  projectId: z
    .string()
    .min(1, "Proje seçimi gereklidir"),
  blockId: z
    .string()
    .min(1, "Blok seçimi gereklidir"),
  apartmentId: z
    .string()
    .min(1, "Daire seçimi gereklidir"),  acceptTerms: z
    .boolean()
    .refine((val: boolean) => val === true, "Kullanım şartlarını kabul etmelisiniz"),
}).refine((data: { password: string; confirmPassword: string }) => data.password === data.confirmPassword, {
  message: "Şifreler eşleşmiyor",
  path: ["confirmPassword"],
})

export const resetPasswordSchema = z.object({
  email: z
    .string()
    .min(1, "Email adresi gereklidir")
    .email("Geçerli bir email adresi giriniz"),
})

export const newPasswordSchema = z.object({
  token: z.string().min(1, "Doğrulama kodu gereklidir"),
  password: z
    .string()
    .min(1, "Yeni şifre gereklidir")
    .min(8, "Şifre en az 8 karakter olmalıdır")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Şifre en az bir küçük harf, bir büyük harf ve bir rakam içermelidir"),
  confirmPassword: z
    .string()
    .min(1, "Şifre tekrarı gereklidir"),
}).refine((data: { password: string; confirmPassword: string }) => data.password === data.confirmPassword, {
  message: "Şifreler eşleşmiyor",
  path: ["confirmPassword"],
})

export const verifyEmailSchema = z.object({
  token: z.string().min(1, "Doğrulama kodu gereklidir"),
})

export type LoginInput = z.infer<typeof loginSchema>
export type RegisterInput = z.infer<typeof registerSchema>
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>
export type NewPasswordInput = z.infer<typeof newPasswordSchema>
export type VerifyEmailInput = z.infer<typeof verifyEmailSchema>
