// Bu script, public/media/projeler/ altındaki tüm .jpg dosyalarını .webp'ye dönüştürür.
// Çalıştırmak için: node scripts/convert-images-to-webp.js

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

const dir = path.join(__dirname, '../public/media/projeler');

fs.readdirSync(dir).forEach(file => {
  if (file.endsWith('.jpg')) {
    const inputPath = path.join(dir, file);
    const outputPath = inputPath.replace(/\.jpg$/, '.webp');
    sharp(inputPath)
      .webp({ quality: 80 })
      .toFile(outputPath)
      .then(() => console.log(`Converted: ${file} -> ${path.basename(outputPath)}`))
      .catch(err => console.error(`Error converting ${file}:`, err));
  }
}); 