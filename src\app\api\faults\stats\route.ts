import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CacheManager, CACHE_TTL } from "@/lib/cache"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const daireId = searchParams.get("daire")
    
    // Generate cache key based on parameters
    const cacheKey = CacheManager.generateKey('api:faults:stats', { daire: daireId })
    
    // Try to get from cache first
    const cached = await CacheManager.get(cacheKey)
    if (cached) {
      console.log(`Cache hit for faults stats: ${cacheKey}`)
      return NextResponse.json(cached)
    }
    
    console.log(`Cache miss for faults stats: ${cacheKey}`)
    
    const now = new Date()
    const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)

    // Base where clause
    const baseWhere = {
      silindi_mi: false,
      ...(daireId && { daire_id: daireId })
    }

    // Get fault counts by status
    const [
      beklemede,
      devamEdiyor,
      tamamlandi,
      iptal,
      toplam,
      buAy,
      oncekiAy
    ] = await Promise.all([
      // Beklemede - use relation
      prisma.ariza.count({
        where: {
          ...baseWhere,
          durum: {
            ad: "BEKLEMEDE"
          }
        }
      }),
      // Devam ediyor
      prisma.ariza.count({
        where: {
          ...baseWhere,
          durum: {
            ad: "DEVAM_EDIYOR"
          }
        }
      }),
      // Tamamlandı
      prisma.ariza.count({
        where: {
          ...baseWhere,
          durum: {
            ad: "TAMAMLANDI"
          }
        }
      }),
      // İptal
      prisma.ariza.count({
        where: {
          ...baseWhere,
          durum: {
            ad: "IPTAL"
          },
          silindi_mi: false,        }
      }),
      // Toplam
      prisma.ariza.count({
        where: baseWhere
      }),
      // Bu ay oluşturulan
      prisma.ariza.count({
        where: {
          ...baseWhere,
          olusturulma_tarihi: {
            gte: startOfThisMonth,
          }
        }
      }),
      // Önceki ay oluşturulan
      prisma.ariza.count({
        where: {
          ...baseWhere,
          olusturulma_tarihi: {
            gte: startOfLastMonth,
            lt: startOfThisMonth,
          }
        }
      }),
    ])

    // Calculate percentage change
    const yuzdelik = oncekiAy > 0 ? ((buAy - oncekiAy) / oncekiAy) * 100 : 0

    const stats = {
      beklemede,
      devamEdiyor,
      tamamlandi,
      iptal,
      toplam,
      buAy,
      oncekiAy,
      yuzdelik,
    }

    // Store in cache for 5 minutes
    await CacheManager.set(cacheKey, stats, CACHE_TTL.STATS)

    return NextResponse.json(stats)
  } catch (error) {
    console.error("Error fetching fault stats:", error)
    return NextResponse.json(
      { message: "İstatistikler yüklenirken hata oluştu" },
      { status: 500 }
    )
  }
}
